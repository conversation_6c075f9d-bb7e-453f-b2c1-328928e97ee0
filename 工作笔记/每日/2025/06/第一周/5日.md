```markdown
"""请分析以下观察内容，完成分类与有效期判断：

【分类任务】
判断内容是否属于以下类别（选择一项）：
1. 基本信息 - 个人属性（姓名、年龄、性别等）、重要日期（生日、纪念日等）
2. 健康与情绪状态 - 身体状况、疾病症状、心情变化、睡眠质量
3. 生活习惯与偏好 - 饮食喜好、作息规律、兴趣爱好、日常习惯

【有效期判断】
当前时间：{current_time_str}
请根据内容性质确定其失效时间：

• 永久信息（如生日、姓名）：使用"permanent"
• 明确时间信息：
  - 若提及具体日期（如"今晚"、"下周三"），计算实际失效日期
  - 例："今晚想吃火锅" → 失效时间为当前日期的次日

• 未提及时间的信息，按类别推断：
  - 基本信息：通常为"permanent"
  - 健康状态：
    * 急性症状（感冒、发烧）：7天
    * 慢性症状（关节痛）：180天
    * 情绪状态（开心、烦躁）：3-7天
  - 生活习惯：
    * 饮食偏好：14-21天
    * 作息习惯：14天
    * 兴趣爱好：30天

【回复格式】
必须使用以下JSON格式回复：
{
  "category": 数字(0-3，0表示不属于任何类别),
  "expiry_time": "具体日期(YYYY-MM-DD)或permanent",
  "reason": "简要说明判断理由(50字以内)"
}
"""
```

```markdown
"""请分析以下观察内容，完成分类与有效期判断：

【分类任务】
判断内容是否属于以下类别（选择一项）：
1. 基本信息 - 个人属性（姓名、年龄、性别等）、重要日期（生日、纪念日等）
2. 健康与情绪状态 - 身体状况、疾病症状、心情变化、睡眠质量
3. 生活习惯与偏好 - 饮食喜好、作息规律、兴趣爱好、日常习惯

【有效期判断】
当前时间：{current_time_str}
请根据内容性质确定其失效时间（最长不超过3个月）：

• 永久信息：仅限基本属性信息（如姓名、生日），使用"permanent"
• 明确时间信息：
  - 提及"今天"：当天结束（23:59:59）
  - 提及"今晚/今夜"：次日早晨（次日08:00:00）
  - 提及"明天/明日"：明天结束（明日23:59:59）
  - 提及"本周/这周"：本周日结束（周日23:59:59）
  - 提及"下周"：下周日结束（下周日23:59:59）
  - 提及具体日期：该具体日期结束（当日23:59:59）

• 未提及时间的信息，按类别精确推断：
  - 基本信息：
    * 姓名、生日、性别等固定属性：permanent
    * 联系方式、地址等可变信息：90天
    * 临时状态（如旅行中）：14天
  
  - 健康状态：
    * 急性症状（感冒、发烧、头痛）：5天
    * 轻微不适（轻微咳嗽、疲劳）：10天
    * 中度症状（扭伤、皮疹）：21天
    * 慢性症状（过敏、轻度关节痛）：60天
    * 情绪状态（高兴、悲伤、焦虑）：3天
    * 压力状态（工作/学习压力）：7天
    * 睡眠状况：5天
  
  - 生活习惯：
    * 一次性饮食偏好（"想吃火锅"）：3天
    * 一般饮食偏好（"喜欢甜食"）：21天
    * 作息习惯（早起、晚睡）：14天
    * 运动习惯：14天
    * 学习/工作习惯：21天
    * 临时兴趣（新接触的活动）：14天
    * 长期兴趣爱好：60天
    * 社交偏好：30天

【回复格式】
必须使用以下JSON格式回复：
{
  "category": 数字(0-3，0表示不属于任何类别),
  "expiry_time": "具体日期(YYYY-MM-DD)或permanent",
  "reason": "简要说明判断理由(30字以内)"
}
"""
```

```json
"""请为以下无时间标记的历史记忆数据添加有效期信息：

【任务说明】
1. 分析提供的历史记忆数据文本
2. 在每条具体信息后面直接添加到期时间标记：（到期时间YYYY-MM-DD）
3. 保持原文格式和结构不变

【有效期判断规则】
假设当前基准时间为：{current_time_str}
根据信息类型判断有效期：

• 基本信息：
  * 固定属性（姓名、生日、性别等）：标注为（permanent）
  * 可变基本信息（地址、联系方式等）：当前日期 + 90天

• 健康与情绪状态：
  * 急性症状（感冒、发热、头痛等）：当前日期 + 5天
  * 口腔问题（溃疡、牙痛等）：当前日期 + 10天
  * 皮肤问题（痘痘、皮疹等）：当前日期 + 14天
  * 慢性症状（过敏、关节痛等）：当前日期 + 60天
  * 情绪状态（开心、焦虑等）：当前日期 + 3天
  * 睡眠问题：当前日期 + 7天
  * 体重变化：当前日期 + 30天
  * 疲劳、精力状况：当前日期 + 5天

• 生活习惯与偏好：
  * 饮食偏好：当前日期 + 21天
  * 作息习惯：当前日期 + 14天
  * 工作状态（如加班）：当前日期 + 10天
  * 兴趣爱好：当前日期 + 60天

【输出格式】
输出应保持原文格式，只在每条具体信息后添加括号标注的到期时间。
如果一句话中包含多个信息点，每个信息点都需单独添加到期时间。

【示例】
输入：
基本信息：暂无基本信息
健康与情绪状态：
  - 小明近期有口腔溃疡，需要注意饮食。最近睡眠质量不佳。

输出：
基本信息：暂无基本信息
健康与情绪状态：
  - 小明近期有口腔溃疡（到期时间2023-07-25），需要注意饮食。最近睡眠质量不佳（到期时间2023-07-22）。
"""
"""
```

