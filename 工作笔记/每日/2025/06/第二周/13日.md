# 聊天会话管理 API 文档

## 基本信息

- 基础URL：`https://cookery-dev.myroki.com/api/llm`
- 认证方式：Bearer Token
- 内容类型：application/json

## 接口列表

### 1. 更新会话标题

更新指定聊天会话的标题。

**请求方式：** POST

**URL：** `/chat_session/update/title`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| sessionId | Long | 是 | 会话ID |
| title | String | 是 | 新的会话标题 |

**请求示例：**
```json
{
  "sessionId": 1932655968081276928,
  "title": "新标题"
}
```



---

### 2. 会话置顶设置

设置指定聊天会话的置顶状态。

**请求方式：** POST

**URL：** `/chat_session/pin`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| sessionId | Long | 是 | 会话ID |
| pinned | Boolean | 是 | 置顶状态，true为置顶，false为取消置顶 |

**请求示例：**
```json
{
  "sessionId": 1932655968081276928,
  "pinned": true
}
```


---

### 3. 分享聊天会话

创建聊天会话的分享。

**请求方式：** POST

**URL：** `/chat_session/share`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| chatHistoryIds | Array | 是 | 要分享的聊天历史ID数组 |
| originalUrl | String | 是 | 原始URL |

**请求示例：**
```json
{
  "chatHistoryIds": [
    1933346884345274368,
    1933346884957642752
  ],
  "originalUrl": "xxxx"
}
```
**返回示例**
```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "traceId": "4604d8eba8794d",
  "data": "1hSZ2Ub2ZKytDGfmclHeemTsa8",// 短链code
  "timestamp": 1749793915714
}
```


---

### 4. 获取分享的聊天会话

根据分享ID获取已分享的聊天会话内容。

**请求方式：** GET

**URL：** `/chat_session/share/{shareId}`

**URL参数：**
| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| shareId | String | 是 | 分享ID |

**请求头：**
```
Authorization: Bearer {token}
```

**请求示例：**
```
GET /api/llm/chat_session/share/1hSZ2UaagHs9HYz9BZtygfll8o
```

**返回示例**
```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "traceId": "c028a3e5ec9e4b",
  "data": {
    "originalUrl": "originalUrl_xhora",
    "metaData": {
      "histories": [
        {
          "id": "1933346884957642752",
          "consumerId": 1874714578769649664,
          "message": "## 上午好！n`2025-06-13 星期五 农历五月十八`\n\n---\n\n### \uD83D\uDCCD武侯区今日天气|Weather Report\n- **当前气温**：30℃ | 体感温度：33℃\uD83C\uDF21\n- **天气状况**：多云（紫外线强）\n- **昼夜温差**：最高36℃ | 最低22℃\uD83D\uDCC9\n- **运动建议**：气温过高，特别容易中暑，较不适宜户外运动，建议室内运动。\n- **穿衣公式**：天气炎热，衣物轻薄透气，室外注意遮阳避暑，室内酌情添加空调衫，不建议在露天场所逛街。\n\n---\n\n### \uD83C\uDF3E芒种养生 第9天——吃出「清热护津」\n\uD83C\uDF1F饮食三原则：\n\n清热护津\uD83C\uDF43 + 健脾化湿\uD83D\uDCAA + 固表止汗\uD83D\uDCA6\n\n---\n\n### \uD83C\uDF75今日养生C位食材——黑米\n- 花青素+硒元素｜抗氧化防癌双效\uD83D\uDEE1️\n- 膳食纤维超大米5倍｜清肠刮油\uD83D\uDCAA\n- 赖氨酸+维生素E｜养发护发黄金米\uD83D\uDC87\n\n---\n\n### \uD83C\uDF73节气推荐菜谱\n- **杂粮粥**：芒种五谷养生粥，九种食材文火炖，滋补不上火～\uD83C\uDF72\n- **苦瓜炒牛肉**：芒种荤素黄金配，苦瓜解腻牛肉香，清热又补铁～\uD83E\uDD69\n- **茄饼蒸肉**：芒种创意吃法，茄片夹肉蒸制，鲜嫩多汁超满足～\uD83E\uDD5F\n\n---\n\n### \uD83D\uDCA1芒种养生Tips\n多吃黄瓜、西瓜等水分多的食物\uD83C\uDF49\n",
          "traceId": "h5zl5v13k4590178",
          "role": "assistant",
          "vote": "NONE",
          "sessionId": "1932666293841162240",
          "createdAt": "2025-06-13T02:13:13.020Z",
          "docFile": [],
          "cookbookFiles": [
            {
              "ossFileResizeSuffix": "?x-oss-process=image/resize,s_1080/format,jpg",
              "id": "15289",
              "fileName": "杂粮粥",
              "fileType": "cookbookCard",
              "fileSize": "",
              "describe": "",
              "ossFileUrl": "https://oss.myroki.com/recipe/cover/cover11/Gc6pOyOtXjORwasQ3BUDimageCover11w1080h1080.jpg"
            },
            {
              "ossFileResizeSuffix": "?x-oss-process=image/resize,s_1080/format,jpg",
              "id": "16423",
              "fileName": "苦瓜炒牛肉",
              "fileType": "cookbookCard",
              "fileSize": "",
              "describe": "",
              "ossFileUrl": "https://oss.myroki.com/recipe/cover/cover11/kd8Kj5VvUz3qSE28NeC8imageCover11w1080h1080.jpg"
            },
            {
              "ossFileResizeSuffix": "?x-oss-process=image/resize,s_1080/format,jpg",
              "id": "17442",
              "fileName": "茄饼蒸肉",
              "fileType": "cookbookCard",
              "fileSize": "",
              "describe": "",
              "ossFileUrl": "https://oss.myroki.com/recipe/cover/cover11/wNWkL53voBroOZteecd5imageCover11w1080h1080.jpg"
            }
          ],
          "files": [],
          "imgFile": [],
          "healthCard": [],
          "video": [],
          "customer": [],
          "nodes": [],
          "extraDataArray": [
            {
              "data": {
                "cookeryDailyType": "solar-term",
                "subscribeStatus": "already-subscribed"
              },
              "type": "preprocess"
            },
            {
              "data": {
                "primaryType": "cookery-daily",
                "firstCookeryDailySentToday": "true"
              },
              "type": "postprocess"
            }
          ]
        },
        {
          "id": "1933346884345274368",
          "consumerId": 1874714578769649664,
          "message": "食神日报",
          "traceId": "h5zl5v13k4590178",
          "role": "user",
          "vote": "NONE",
          "sessionId": "1932666293841162240",
          "createdAt": "2025-06-13T02:13:11.735Z",
          "docFile": [],
          "cookbookFiles": [],
          "files": [],
          "imgFile": [],
          "healthCard": [],
          "video": [],
          "customer": [],
          "nodes": [],
          "extraDataArray": []
        }
      ]
    }
  },
  "timestamp": 1749793967759
}
```