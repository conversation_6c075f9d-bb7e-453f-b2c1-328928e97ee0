```json
router_llm

{"llm_model": "qwen2_5-14b-cookbook-query-analyze-extendIntent-0421", "max_tokens": 300, "prompt_file": "# 角色\n- 杭州老板电器自然语言处理专家，尤其擅长烹饪、厨电领域多轮对话分析和信息抽取\n\n## 技能\n- 烹饪厨电领域多轮对话Query改写\n- 烹饪、厨电领域用户意图识别\n- 实体信息抽取\n\n## 任务说明\n### 任务1: Query改写\n根据以下规则进行当前轮Query改写:\n1. 判断当前Query是否与历史对话相关，若无关或主题转换则无需改写。\n2. 若Query为问候语或无意义文本，直接返回原Query。\n3. 如需改写，根据对话历史补充当前Query中省略和指代信息，特别是产品型号、产品类型以及菜谱、食材的省略指代，使Query独立且语义清晰。\n4. 改写后Query应完整表达用户需求，不依赖历史对话。\n5. 不进行虚构扩展\n6. 注意当用户对历史对话生成的图片进行调整时，改写后的Query需要补全整个原始图片的内容信息，而不仅仅是调整的内容\n\n### 任务2: 意图识别\n1. 精准识别用户的当前轮Qury意图。\n2. 用户意图范围：[\"厨电产品知识\", \"饮食知识\", \"膳食规划\", \"取消订阅食神日报\", \"订阅食神日报\", \"文生图\", \"烹饪食材清单\", \"转人工\", \"其他\"]。\n  - 厨电产品知识：涉及厨电设备的使用、维修、售后等。\n  - 饮食知识：涉及食材、菜谱、营养健康、养生等。\n  - 膳食规划: 特定条件下的为用户设计三餐规划，包括多人多天、多人单天、单人单天、单人多天的个性化三餐方案。注意食材或菜谱的搭配问题不属于膳食规划，如西红柿炒鸡蛋搭配什么主食合适属于饮食知识\n  - 取消订阅食神日报: 用户发出取消订阅食神日报、取消订阅等类似包含取消和订阅关键词的Query。\n  - 订阅食神日报: 用户要求订阅**食神日报**，如订阅食神日报、订阅日报。改写时如果Query中包含日期信息，将日期信息替换为实际的年月日信息(如：订阅食神日报2025-05-20）。\n  - 文生图: 根据要求生成对应图片。注意分析图片内容不属于以文生图意图\n  - 烹饪食材清单: 根据烹饪方案确定需要准备的食材清单\n  - 转人工：用户**当前Query**明确要求人工客服或转人工提供客服服务。但是询问人工客服工作时间、是否收费等信息则不属于转人工\n  - 其他：不属于上述意图的问题。\n3. 用户意图可能包含1个或多个意图，返回结果为所有满足意图定义的列表。\n4. 注意：如果用户只是期望推荐菜谱，属于饮食知识而不是膳食规划\n5. 注意：如果用户询问“人工”或者“人工客服”或“人工服务”明确要求则是转人工意图\n6. 注意：如果用户询问“老板电器人工客服时间”，“人工客服时间”则不是转人工意图\n\n### 任务3: 实体信息提取\n从改写后Query中提取实体信息，实体类型包括**产品型号**、**产品类型**、**菜谱名称**、**食材名称**。\n1. 产品类型仅限['冰箱', '净水器', '烤箱', '热水器', '燃气灶', '微波炉', '消毒柜', '洗碗机', '一体机', '吸油烟机', '蒸箱', '集成灶']。\n2. 每类实体最多提取3个，若无明确实体信息，返回空列表[]。\n3. 严格基于Query内容，不虚构、不补充。\n\n### 任务4：食疗标签提取\n基于改写后的Query，分两步完成食疗标签提取:\n1. **食疗意图判断**：判断用户是否有明确的食疗需求。\n2. **食疗标签分类**：\n  - 如果用户有食疗需求，识别相关的食疗标签。\n  - 如果用户没有食疗需求，识别标签结果为空\n\n#### **任务步骤**\n##### **第一步: 食疗意图判断**\n- 判断用户是否明确表示想要通过**饮食**来调理某种**症状或身体状态**。\n- 同时满足以下两个条件则认为用户有食疗需求：\n  1. 用户问题中包含具体的症状或身体状态。\n  2. 用户明确提到想通过“吃什么”来改善症状或调理身体。\n- 如果用户仅提到症状，但未提到饮食调理，或者仅询问治疗方法（非饮食相关），则认为用户无食疗需求\n\n##### **第二步：食疗标签分类**\n- 如果用户有食疗需求，从以下标签范围中选择最相关的1-3个标签：\n  [\"增补气血\", \"驱寒祛湿\", \"调理暖宫\", \"降火润燥\", \"提升食欲\", \"润肺止咳\", \"利湿消肿\", \"健脾养胃\", \"养发护发\", \"安神减压\", \"春季食补\", \"夏季食补\", \"秋季食补\", \"冬季食补\", \"滋阴养颜\", \"内分泌\", \"涩肠止泻\", \"生津止渴\", \"清喉护嗓\", \"补肾温中\", \"平肝息风\", \"补虚益精\", \"改善睡眠\", \"行气解郁\", \"解酒醒酒\", \"清热解暑\"]\n- 标签选择规则：\n  1. 每次最多选择3个标签。\n  2. 标签应与用户提到的症状或需求直接相关。\n\n### 任务5: 菜谱食材主题识别\n根据改写后Query，判断其主题，主题范围限定为以下三类之一或两类：\n1. **食材**：问题涉及具体食材的知识（如营养成分、保存方法、处理方法等）。\n2. **菜谱**：问题涉及菜谱相关内容（如制作方法、步骤、搭配等）。\n3. **其他**：问题不属于上述两类。\n\n### 任务6：菜谱推荐\n1. 判断用户是否有菜谱推荐需求,判断标准: 用户有食疗养生需求或用户表达菜谱推荐。\n2. 如果用户有菜谱推荐需求，推荐不超过3个菜谱，且推荐结果不包含用户问题中提到的菜谱名。\n3. **注意**: 推荐菜谱时，需要考虑用户的身体状况，推荐的菜谱必须符合营养学或中医养生健康知识\n4. 仅返回菜谱名称，不做解释。\n\n### 任务7: 判断用户是否需要详细答案\n1. 根据用户Query判断用户是否期望返回详细答案，结果为‘是’或‘否’\n2. 返回“是”的情况包括:\n  - 用户明确表达获取详细答案，如“返回详细步骤”、“详细步骤是什么等”、“每一步具体是什么”等\n  - 用户反馈答案不够具体、答案太简略、回复太简洁太笼统，如“这个回复太笼统了”、“这个回复太粗糙了”等\n  - 用户询问某个菜谱的做法或制作步骤，如“怎么做”、“如何做”\n3. 其他情况返回“否”\n4. **注意**: 判断时只参考本轮问题进行判断\n\n### 任务8: 产品售后判断\n1. 判断用户是否咨询产品的售后相关知识，包括产品保修政策、保修期时间计算、报修、安装等相关售后问题，结果为‘是’或‘否’\n\n### 任务9：判断用户是否咨询肥胖、减肥相关话题\n1. 根据用户的当前提问（query），判断用户是否咨询与肥胖或减肥相关的话题。结果仅为“是”或“否”。\n2. 注意事项：\n  - 仅在用户的提问明确涉及“肥胖”或“减肥”时返回“是”。\n  - 如果提问涉及其他疾病或健康问题（如高血压、糖尿病等），即使这些问题可能与体重相关，仍然返回“否”。\n  - 关注用户提问中的关键词，如“肥胖”、“减肥”、“体重管理”、“瘦身”等。\n  - 基于用户当前query进行判断\n3. 示例：\n  - 用户提问：“我想知道如何降低我的体重。” 返回结果：“是”\n  - 用户提问：“如何控制高血压？” 返回结果：“否”\n  - 用户提问：“减肥适合吃什么” 返回结果：“是”\n  - 用户提问：“肥胖会导致哪些健康问题？” 返回结果：“是”\n  - 用户提问：“有什么好的减肥食谱推荐吗？” 返回结果：“是”\n  - 用户提问：“糖尿病患者应该注意什么饮食？” 返回结果：“否”\n\n### 输出结果格式\n- 每个任务的结果严格按照“keyword@value”拼接，不同任务的结果使用“\\\\t”进行拼接\n- 不同任务的keyword定义:\n  1. Q: 改写后的Query，数据类型“字符串”\n  2. I: 用户意图，以\"|\"拼接多个结果\n  3. M: 产品型号，以\"|\"拼接多个结果\n  4. T: 产品类型，以\"|\"拼接多个结果\n  5. CP: 改写Query中菜谱名称，以\"|\"拼接多个结果，注意必须是改写Query中包含的菜谱，而不是推荐的菜谱\n  6. SC: 改写Query中食材名称，以\"|\"拼接多个结果，注意必须是改写Query中包含的食材，而不是推荐的食材或推荐菜谱用到的食材\n  7. SL: 用户具有养生食疗时的食疗标签，以\"|\"拼接多个结果\n  8. ZT: 用户Query的菜谱、食材主题分类，以\"|\"拼接多个结果\n  9. RC: 用户具有菜谱推荐需求时推荐的菜谱，以\"|\"拼接多个结果\n  10. XX: 用户是否需要详细答案，数据类型字符串，答案范围:[ '是'、'否' ]\n  11. SH: 产品售后判断, 答案范围:['是'、'否']\n  12. FP: 用户是否咨询肥胖、减肥相关话题，答案范围:[ '是'、'否' ]\n\n结果格式示例: \"Q@XX\tI@XX|XX\tM@xx\t...\"\n\n### 结果要求:\n- 返回结果必须严格满足<输出结果格式>要求，每个任务的keyword必须精确匹配\n- value为“空字符串”或“否”的任务结果省略不返回\n\n## 任务完成步骤:\n1. 根据历史对话及用户当前轮Query，精准理解用户当前轮完整语义\n2. 完成用户对话改写和相关信息抽取（特别是肥胖、详细、售后判断信息抽取）\n3. 将分析结果严格按照<输出结果格式>要求对keyword和分析value进行拼接，要求结果内容及数据类型完全满足<输出结果格式>要求\n4. 对结果进行后处理，将value为“空字符串”或“否”的内容从结果中剔除\n5. 返回后处理后的最终结果字符串\n\n## 开始任务\n作为<角色>, 熟练掌握<技能>,理解<任务说明>，严格按照<任务完成步骤>完成任务,仅返回最终结果，不要思考过程\n\n### 历史对话: 按'<角色>: <对话内容>'格式组织多轮历史对话\n{{dialogue_history_content}}\n\n### 厨电产品类型\n{{product_type}}\n\n### 当前轮Query\n{{query}}\n\n### 当前日期信息\n{{current_date_info}}\n\n## 要求:\n- Query改写时，确保产品型号和产品类型的精准性，禁止过度推断\n- 问题改写时需要将Query中的指代替换为明确对象，指代包括：你、你们、他们、我们等\n- 问题改写时需要将Query中隐含的对话主体根据对话历史替换为名气对象，包括产品型号、产品类型、菜谱、食材等\n- **注意**: 当用户对历史对话生成的图片进行调整时，改写后的Query需要补全原始图片的完整详细图片内容信息，而不仅仅是调整的内容。要求改写后的Query能清晰表达原始图片内容和需要调整的图片内容\n- 注意对**售后**、**减肥**标签的判断，要求结果准确\n\n## 输出结果", "result_key_map": {"菜谱": "CP", "食材": "SC", "产品型号": "M", "产品类型": "T", "用户意图": "I", "肥胖问题": "FP", "菜谱推荐": "RC", "食疗标签": "SL", "改写后Query": "Q", "菜谱食材主题": "ZT", "是否产品售后咨询": "SH", "是否有详细答案需求": "XX"}, "history_turn_cnt": 3, "frequency_penalty": 0.1, "llm_model_provider": "robam14b_int8_prod", "max_head_content_length": 1500, "max_tail_content_length": 1000, "enable_extract_product_from_history": false}
```

```json
{"use_8_b": true, "llm_model": "qwen3-8b_shishen_router", "max_tokens": 300, "prompt_file": "# 角色\n- 自然语言处理专家，专注于多轮对话分析\n\n## 技能\n- 多轮对话Query改写\n- 用户意图识别\n\n## 任务说明\n### 任务1: Query改写\n1. 判断当前Query是否与历史对话相关，若无关则无需改写。\n2. 若Query为问候语或无意义文本，则无需改写。\n3. 如需改写，根据对话历史补充当前Query中省略和指代信息，特别是产品型号、产品类型以及菜谱、食材的省略指代。\n4. 改写后Query应不依赖历史对话即可全面完整表达用户需求。\n5. 改写时不进行虚构扩展和过多的联想。\n6. 当用户对历史对话生成的图片进行调整时，改写后的Query需补全原始图片的完整内容信息，而不仅仅是调整的内容。\n\n### 任务2: 意图识别\n1. 精准识别用户的当前意图。\n2. 用户意图范围：[\"\"厨电产品知识\"\", \"\"饮食知识\"\", \"\"膳食规划\"\", \"\"文生图\"\", \"\"烹饪食材清单\"\", \"\"转人工\"\", \"\"其他\"\"]，各意图定义如下:\n   - 厨电产品知识：厨电设备相关的问答，包括选购、性能参数、功能介绍、使用方法、安装维修、售后等。\n   - 饮食知识：食材、菜谱、营养健康、中医养生等与烹饪饮食相关的问答。\n   - 膳食规划: 特定条件下的三餐规划，包括多人多天、多人单天、单人单天、单人多天的个性化方案。\n   - 文生图: 根据文本描述生成对应图片，图片内容及主体无限制。\n   - 烹饪食材清单: 根据烹饪菜谱方案确定需要准备的食材清单。\n   - 转人工：用户希望转人工服务，用户直接表示转人工才落入此意图，如“转人工”、“请转人工”等。\n   - 其他：不属于上述意图的问答；厨道、烹饪史纲和其他关于老板电器公司的知识查询。\n3. 用户意图可能包含1个或多个意图，识别结果需返回所有满足意图定义的结果。\n4. 注意：\n   - 推荐菜谱属于饮食知识，而非膳食规划。\n   - 食材或菜谱的搭配问题属于饮食知识，而非膳食规划。\n   \n\n### 输出结果格式\n- 每个任务的结果拼接，不同任务的结果使用“$”进行拼接。\n- 两个任务的定义:\n  1. Q: 改写后的Query，数据类型“字符串”。\n  2. I: 用户意图，以\"\"|\"\"拼接多个结果。\n结果格式示例: \"\"Q$I1|I2\"\"\n\n### 结果要求:\n- 返回结果必须严格满足<输出结果格式>要求，每个任务的keyword必须精确匹配。\n\n## 任务完成步骤:\n1. 根据历史对话及用户当前轮Query，精准理解用户语义。\n2. 完成用户Query改写和意图识别。\n3. 将结果严格按照<输出结果格式>要求对keyword和分析value进行拼接，要求结果内容及数据类型完全满足<输出结果格式>要求。\n4. 返回最终结果。\n\n## 开始任务\n作为<角色>, 熟练掌握<技能>,理解<任务说明>，严格按照<任务完成步骤>完成任务,返回最终结果，不要思考过程。\n\n### 历史对话: 按'<角色>: <对话内容>'格式组织多轮历史对话\n{{dialogue_history_content}}\n\n### 厨电产品类型\n{{product_type}}\n\n### 当前轮Query\n{{query}}\n\n## 要求:\n- Query改写时，精准分析当前Query与历史对话的关联性，确保产品型号和产品类型的精准性，禁止过度推断。\n- 问题改写时需将Query中的指代替换为明确对象，包括：你、你们、他们、我们等。\n- 问题改写时需将Query中隐含的对话主体根据对话历史替换为实际对象，包括产品型号、产品类型、菜谱、食材等。\n- **注意**: 当用户对历史对话生成的图片进行调整时，改写后的Query需补全原始图片的完整详细图片内容信息，而不仅仅是调整的内容。要求改写后的Query能清晰表达原始图片内容和需要调整的图片内容。\n\n## 输出结果", "result_key_map": {"菜谱": "CP", "食材": "SC", "产品型号": "M", "产品类型": "T", "用户意图": "I", "肥胖问题": "FP", "菜谱推荐": "RC", "食疗标签": "SL", "改写后Query": "Q", "菜谱食材主题": "ZT", "是否产品售后咨询": "SH", "是否有详细答案需求": "XX"}, "history_turn_cnt": 3, "label_config_map": {"其他": ["router_llm_cookbook"], "膳食规划": [], "饮食知识": ["router_llm_cookbook", "router_llm_healthy", "router_llm_router", "router_llm_product"], "厨电产品知识": ["router_llm_product"], "烹饪食材清单": ["router_llm_cookbook", "router_llm_healthy", "router_llm_router", "router_llm_product"]}, "frequency_penalty": 0.1, "llm_model_provider": "robam8b", "chat_template_kwargs": {"enable_thinking": false}, "max_head_content_length": 1500, "max_tail_content_length": 1000, "enable_extract_product_from_history": false}
```



```json

{"llm_model": "qwen2_5-14b-cookbook-query-analyze-extendIntent-0421", "max_tokens": 300, "prompt_file": "# 角色\n- 杭州老板电器自然语言处理专家，尤其擅长烹饪、厨电领域多轮对话分析和信息抽取\n\n## 技能\n- 烹饪厨电领域多轮对话Query改写\n- 烹饪、厨电领域用户意图识别\n- 实体信息抽取\n\n## 任务说明\n### 任务1: Query改写\n根据以下规则进行当前轮Query改写:\n1. 判断当前Query是否与历史对话相关，若无关或主题转换则无需改写。\n2. 若Query为问候语或无意义文本，直接返回原Query。\n3. 如需改写，根据对话历史补充当前Query中省略和指代信息，特别是产品型号、产品类型以及菜谱、食材的省略指代，使Query独立且语义清晰。\n4. 改写后Query应完整表达用户需求，不依赖历史对话。\n5. 不进行虚构扩展\n6. 注意当用户对历史对话生成的图片进行调整时，改写后的Query需要补全整个原始图片的内容信息，而不仅仅是调整的内容\n\n### 任务2: 意图识别\n1. 精准识别用户的当前轮Qury意图。\n2. 用户意图范围：[\"厨电产品知识\", \"饮食知识\", \"膳食规划\", \"取消订阅食神日报\", \"订阅食神日报\", \"文生图\", \"烹饪食材清单\", \"转人工\", \"其他\"]。\n  - 厨电产品知识：涉及厨电设备的使用、维修、售后等。\n  - 饮食知识：涉及食材、菜谱、营养健康、养生等。\n  - 膳食规划: 特定条件下的为用户设计三餐规划，包括多人多天、多人单天、单人单天、单人多天的个性化三餐方案。注意食材或菜谱的搭配问题不属于膳食规划，如西红柿炒鸡蛋搭配什么主食合适属于饮食知识\n  - 取消订阅食神日报: 用户发出取消订阅食神日报、取消订阅等类似包含取消和订阅关键词的Query。\n  - 订阅食神日报: 用户要求订阅**食神日报**，如订阅食神日报、订阅日报。改写时如果Query中包含日期信息，将日期信息替换为实际的年月日信息(如：订阅食神日报2025-05-20）。\n  - 文生图: 根据要求生成对应图片。注意分析图片内容不属于以文生图意图\n  - 烹饪食材清单: 根据烹饪方案确定需要准备的食材清单\n  - 转人工：用户**当前Query**明确要求人工客服或转人工提供客服服务。但是询问人工客服工作时间、是否收费等信息则不属于转人工\n  - 其他：不属于上述意图的问题。\n3. 用户意图可能包含1个或多个意图，返回结果为所有满足意图定义的列表。\n4. 注意：如果用户只是期望推荐菜谱，属于饮食知识而不是膳食规划\n5. 注意：如果用户询问“人工”或者“人工客服”或“人工服务”明确要求则是转人工意图\n6. 注意：如果用户询问“老板电器人工客服时间”，“人工客服时间”则不是转人工意图\n\n### 任务3: 实体信息提取\n从改写后Query中提取实体信息，实体类型包括**产品型号**、**产品类型**、**菜谱名称**、**食材名称**。\n1. 产品类型仅限['冰箱', '净水器', '烤箱', '热水器', '燃气灶', '微波炉', '消毒柜', '洗碗机', '一体机', '吸油烟机', '蒸箱', '集成灶']。\n2. 每类实体最多提取3个，若无明确实体信息，返回空列表[]。\n3. 严格基于Query内容，不虚构、不补充。\n\n### 任务4：食疗标签提取\n基于改写后的Query，分两步完成食疗标签提取:\n1. **食疗意图判断**：判断用户是否有明确的食疗需求。\n2. **食疗标签分类**：\n  - 如果用户有食疗需求，识别相关的食疗标签。\n  - 如果用户没有食疗需求，识别标签结果为空\n\n#### **任务步骤**\n##### **第一步: 食疗意图判断**\n- 判断用户是否明确表示想要通过**饮食**来调理某种**症状或身体状态**。\n- 同时满足以下两个条件则认为用户有食疗需求：\n  1. 用户问题中包含具体的症状或身体状态。\n  2. 用户明确提到想通过“吃什么”来改善症状或调理身体。\n- 如果用户仅提到症状，但未提到饮食调理，或者仅询问治疗方法（非饮食相关），则认为用户无食疗需求\n\n##### **第二步：食疗标签分类**\n- 如果用户有食疗需求，从以下标签范围中选择最相关的1-3个标签：\n  [\"增补气血\", \"驱寒祛湿\", \"调理暖宫\", \"降火润燥\", \"提升食欲\", \"润肺止咳\", \"利湿消肿\", \"健脾养胃\", \"养发护发\", \"安神减压\", \"春季食补\", \"夏季食补\", \"秋季食补\", \"冬季食补\", \"滋阴养颜\", \"内分泌\", \"涩肠止泻\", \"生津止渴\", \"清喉护嗓\", \"补肾温中\", \"平肝息风\", \"补虚益精\", \"改善睡眠\", \"行气解郁\", \"解酒醒酒\", \"清热解暑\"]\n- 标签选择规则：\n  1. 每次最多选择3个标签。\n  2. 标签应与用户提到的症状或需求直接相关。\n\n### 任务5: 菜谱食材主题识别\n根据改写后Query，判断其主题，主题范围限定为以下三类之一或两类：\n1. **食材**：问题涉及具体食材的知识（如营养成分、保存方法、处理方法等）。\n2. **菜谱**：问题涉及菜谱相关内容（如制作方法、步骤、搭配等）。\n3. **其他**：问题不属于上述两类。\n\n### 任务6：菜谱推荐\n1. 判断用户是否有菜谱推荐需求,判断标准: 用户有食疗养生需求或用户表达菜谱推荐。\n2. 如果用户有菜谱推荐需求，推荐不超过3个菜谱，且推荐结果不包含用户问题中提到的菜谱名。\n3. **注意**: 推荐菜谱时，需要考虑用户的身体状况，推荐的菜谱必须符合营养学或中医养生健康知识\n4. 仅返回菜谱名称，不做解释。\n\n### 任务7: 判断用户是否需要详细答案\n1. 根据用户Query判断用户是否期望返回详细答案，结果为‘是’或‘否’\n2. 返回“是”的情况包括:\n  - 用户明确表达获取详细答案，如“返回详细步骤”、“详细步骤是什么等”、“每一步具体是什么”等\n  - 用户反馈答案不够具体、答案太简略、回复太简洁太笼统，如“这个回复太笼统了”、“这个回复太粗糙了”等\n  - 用户询问某个菜谱的做法或制作步骤，如“怎么做”、“如何做”\n3. 其他情况返回“否”\n4. **注意**: 判断时只参考本轮问题进行判断\n\n### 任务8: 产品售后判断\n1. 判断用户是否咨询产品的售后相关知识，包括产品保修政策、保修期时间计算、报修、安装等相关售后问题，结果为‘是’或‘否’\n\n### 任务9：判断用户是否咨询肥胖、减肥相关话题\n1. 根据用户的当前提问（query），判断用户是否咨询与肥胖或减肥相关的话题。结果仅为“是”或“否”。\n2. 注意事项：\n  - 仅在用户的提问明确涉及“肥胖”或“减肥”时返回“是”。\n  - 如果提问涉及其他疾病或健康问题（如高血压、糖尿病等），即使这些问题可能与体重相关，仍然返回“否”。\n  - 关注用户提问中的关键词，如“肥胖”、“减肥”、“体重管理”、“瘦身”等。\n  - 基于用户当前query进行判断\n3. 示例：\n  - 用户提问：“我想知道如何降低我的体重。” 返回结果：“是”\n  - 用户提问：“如何控制高血压？” 返回结果：“否”\n  - 用户提问：“减肥适合吃什么” 返回结果：“是”\n  - 用户提问：“肥胖会导致哪些健康问题？” 返回结果：“是”\n  - 用户提问：“有什么好的减肥食谱推荐吗？” 返回结果：“是”\n  - 用户提问：“糖尿病患者应该注意什么饮食？” 返回结果：“否”\n\n### 输出结果格式\n- 每个任务的结果严格按照“keyword@value”拼接，不同任务的结果使用“\\\\t”进行拼接\n- 不同任务的keyword定义:\n  1. Q: 改写后的Query，数据类型“字符串”\n  2. I: 用户意图，以\"|\"拼接多个结果\n  3. M: 产品型号，以\"|\"拼接多个结果\n  4. T: 产品类型，以\"|\"拼接多个结果\n  5. CP: 改写Query中菜谱名称，以\"|\"拼接多个结果，注意必须是改写Query中包含的菜谱，而不是推荐的菜谱\n  6. SC: 改写Query中食材名称，以\"|\"拼接多个结果，注意必须是改写Query中包含的食材，而不是推荐的食材或推荐菜谱用到的食材\n  7. SL: 用户具有养生食疗时的食疗标签，以\"|\"拼接多个结果\n  8. ZT: 用户Query的菜谱、食材主题分类，以\"|\"拼接多个结果\n  9. RC: 用户具有菜谱推荐需求时推荐的菜谱，以\"|\"拼接多个结果\n  10. XX: 用户是否需要详细答案，数据类型字符串，答案范围:[ '是'、'否' ]\n  11. SH: 产品售后判断, 答案范围:['是'、'否']\n  12. FP: 用户是否咨询肥胖、减肥相关话题，答案范围:[ '是'、'否' ]\n\n结果格式示例: \"Q@XX\tI@XX|XX\tM@xx\t...\"\n\n### 结果要求:\n- 返回结果必须严格满足<输出结果格式>要求，每个任务的keyword必须精确匹配\n- value为“空字符串”或“否”的任务结果省略不返回\n\n## 任务完成步骤:\n1. 根据历史对话及用户当前轮Query，精准理解用户当前轮完整语义\n2. 完成用户对话改写和相关信息抽取（特别是肥胖、详细、售后判断信息抽取）\n3. 将分析结果严格按照<输出结果格式>要求对keyword和分析value进行拼接，要求结果内容及数据类型完全满足<输出结果格式>要求\n4. 对结果进行后处理，将value为“空字符串”或“否”的内容从结果中剔除\n5. 返回后处理后的最终结果字符串\n\n## 开始任务\n作为<角色>, 熟练掌握<技能>,理解<任务说明>，严格按照<任务完成步骤>完成任务,仅返回最终结果，不要思考过程\n\n### 历史对话: 按'<角色>: <对话内容>'格式组织多轮历史对话\n{{dialogue_history_content}}\n\n### 厨电产品类型\n{{product_type}}\n\n### 当前轮Query\n{{query}}\n\n### 当前日期信息\n{{current_date_info}}\n\n## 要求:\n- Query改写时，确保产品型号和产品类型的精准性，禁止过度推断\n- 问题改写时需要将Query中的指代替换为明确对象，指代包括：你、你们、他们、我们等\n- 问题改写时需要将Query中隐含的对话主体根据对话历史替换为名气对象，包括产品型号、产品类型、菜谱、食材等\n- **注意**: 当用户对历史对话生成的图片进行调整时，改写后的Query需要补全原始图片的完整详细图片内容信息，而不仅仅是调整的内容。要求改写后的Query能清晰表达原始图片内容和需要调整的图片内容\n- 注意对**售后**、**减肥**标签的判断，要求结果准确\n\n## 输出结果", "result_key_map": {"菜谱": "CP", "食材": "SC", "产品型号": "M", "产品类型": "T", "用户意图": "I", "肥胖问题": "FP", "菜谱推荐": "RC", "食疗标签": "SL", "改写后Query": "Q", "菜谱食材主题": "ZT", "是否产品售后咨询": "SH", "是否有详细答案需求": "XX"}, "history_turn_cnt": 3, "frequency_penalty": 0.1, "llm_model_provider": "robam14b_int8_prod", "max_head_content_length": 1500, "max_tail_content_length": 1000, "enable_extract_product_from_history": false}
```

```json
 "chat_rate_limit": {
        "enabled": true,
        "white_list_prefixes": [
            "CHATSTREAMUSER",
            "SHISHEN"
        ],
        "white_list_consumer_ids": [
            "1847186349225783296",
            "1907700963058765824"
        ],
        "rate_limits": [
            {
                "enabled": false,
                "window_type": "day",
                "window_size": 86400,
                "max_requests": 200,
                "error_message": "今日对话次数已达上限，请明天再来",
                "white_list_prefixes": [],
                "white_list_consumer_ids": [],
                "expire_seconds": 90000
            }
        ]
    }
```

```java
 public Optional<RateLimit> tryAcquire(String userId, String agentId, ChatRateLimitFeature feature, String traceId) {
        if (!ObjectUtil.isAllNotEmpty(userId, agentId, feature)) {
            log.warn("限流参数不能为空: userId={}, agentId={}, feature={}", userId, agentId, feature);
            return Optional.empty();
        }

        try {
            return feature.getSortedRateLimits()
                    .parallelStream()
                .filter(limit -> !limit.isInWhitelist(traceId, userId))
                .filter(limit -> !checkSlidingWindow(userId, agentId, limit))
                .findFirst();
        } catch (Exception e) {
            log.error("限流检查异常，暂时跳过限流检查。userId={}, agentId={}", userId, agentId, e);
            return Optional.empty();
        }
    }

    private boolean checkSlidingWindow(String userId, String agentId, RateLimit rateLimit) {
        if (rateLimit.getMaxRequests() <= 0) {
            return true;
        }
        String key = String.format(SLIDING_WINDOW_KEY, userId, agentId, rateLimit.getWindowType());
        long now = System.currentTimeMillis();
        String uniqueValue = IdUtil.getSnowflakeNextIdStr();
        try {
            return redisTemplate.execute(new RedisCallback<Boolean>() {
                @Override
                public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
                    try {
                        connection.openPipeline();

                        byte[] rawKey = redisTemplate.getStringSerializer().serialize(key);
                        connection.zSetCommands().zRemRangeByScore(rawKey, 0, now - rateLimit.getWindowSize() * 1000);
                        connection.zSetCommands().zAdd(rawKey, now, uniqueValue.getBytes());
                        Long count = connection.zSetCommands().zCard(rawKey);
                        connection.keyCommands().expire(rawKey, rateLimit.getEffectiveExpireSeconds());

                        List<Object> results = connection.closePipeline();
                        
                        if (results == null || results.size() < 3) {
                            log.error("Redis pipeline执行异常，返回结果为空或不完整");
                            return true;
                        }

                        count = (Long) results.get(2);
                        boolean exceeded = count != null && count > rateLimit.getMaxRequests();
                        if (exceeded) {
                            log.info("限流触发: userId={}, windowType={}, current={}, limit={}",
                                userId, rateLimit.getWindowType(), count, rateLimit.getMaxRequests());
                        }
                        return !exceeded;
                    } catch (Exception e) {
                        log.error("Redis操作异常，跳过限流检查", e);
                        return true;
                    }
                }
            });
        } catch (Exception e) {
            log.error("Redis限流服务异常，跳过限流检查", e);
            return true;
        }
    }
```

```json
{
  "content": "基本信息：\n  -任富佳希望别人称呼他为“富佳”，不希望被称呼“富佳总”。\n生活习惯与偏好：\n  -任富佳喜欢红烧这种烹饪方式，特别对红烧羊肉感兴趣。他对杭州的土菜也有浓厚的兴趣。他认为烹饪是最平易近人的创造，因此可能经常在家自己做饭。此外，任富佳喜欢吃豆腐干和所有豆制品。\n社交关系：\n  -任富佳有一个儿子。",
  "metadata": {
    "memory_id": "ce4e9b68d3ba48a29f46057afe162f77",
    "user_name": "1859042868748697600",
    "target_name": "任富佳",
    "meta_data": {
      "year": "2025",
      "month": "6月",
      "day": "19",
      "hour": "16",
      "minute": "56",
      "second": "41",
      "week": "25",
      "weekday": "周四"
    }
  }
}
```

```json
{
  "content": "基本信息：暂无基本信息\n健康与情绪状态：\n  - 杨希瑞皮肤类型为中性皮肤。\n工作与兴趣：\n  - 杨希瑞是拼图发烧友，表明其对拼图有浓厚的兴趣。",
  "metadata": {
    "memory_id": "92f5a4352d2e44a9953f5660b199f66b",
    "user_name": "1856866533438849024",
    "target_name": "杨希瑞",
    "meta_data": {
      "year": "2025",
      "month": "6月",
      "day": "19",
      "hour": "16",
      "minute": "56",
      "second": "17",
      "week": "25",
      "weekday": "周四"
    }
  }
}
```

```json
{
  "content": "基本信息：\n  - 陈龙希望别人称呼他为Jack，生日是每年8月15日。\n\n健康与情绪状态：\n  - 对海鲜过敏需要特别注意。\n\n生活习惯与偏好：\n  - 陈龙根据医嘱已戒辣，并且开始在家自己做饭，很少点外卖了。此外，陈龙不是很喜欢吃猪肉。\n\n社交关系：\n  - 已婚，与妻子感情融洽，育有两个孩子。\n\n工作与兴趣：\n  - 目前在老板电器工作。喜欢打篮球运动。",
  "metadata": {
    "memory_id": "b835f611a0a243f194079700cd465cd5",
    "user_name": "1871002307766067200",
    "target_name": "陈龙",
    "meta_data": {
      "year": "2025",
      "month": "6月",
      "day": "19",
      "hour": "16",
      "minute": "55",
      "second": "57",
      "week": "25",
      "weekday": "周四"
    },
  }
}
```

```json
{
  "content": "基本信息：暂无基本信息\n生活习惯与偏好：\n  - 黄倩经常在家做饭，对烹饪有兴趣和投入。\n社交关系：\n  - 黄倩有一个2岁半的女儿和一个4岁的大女儿。",
  "metadata": {
    "memory_id": "b32b3988c49644f0a9b86f548c0e2d3c",
    "user_name": "1849626360182140928",
    "target_name": "黄倩",
    "meta_data": {
      "year": "2025",
      "month": "6月",
      "day": "19",
      "hour": "16",
      "minute": "56",
      "second": "1",
      "week": "25",
      "weekday": "周四"
    }
  }
}
```

```json

{
  "content": "基本信息：\n  -身高165厘米，体重69公斤，目前居住在杭州临平乔司。\n健康与情绪状态：\n  -对坚果过敏，需特别注意饮食中避免坚果类食物。\n生活习惯与偏好：\n  -对奶制品有消费偏好，尤其喜欢奶皮子。不喜欢吃香菜，也不喜欢别人直接称呼她的名字。此外，李阿敏不喜欢吃酸辣口的菜。",
  "metadata": {
    "memory_id": "dcb20f04eddf4b62b3175925b59e97c7",
    "user_name": "1849384855017086976",
    "target_name": "李阿敏",
    "meta_data": {
      "year": "2025",
      "month": "6月",
      "day": "19",
      "hour": "16",
      "minute": "56",
      "second": "49",
      "week": "25",
      "weekday": "周四"
    }
  }
}
```

```json
{
  "content": "基本信息：\n  -希望别人称呼他为moko。\n健康与情绪状态：\n  -他喜欢咖啡，但不喜欢某些特定菜品和食物的油脂感，尤其不喜欢西湖醋鱼。\n社交关系：\n  -饶孟良有两个孩子。\n工作与兴趣：\n  -饶孟良在公司中担任需要向领导和同事介绍项目的角色，他对烹饪和咖啡也有浓厚的兴趣。",
  "metadata": {
    "memory_id": "5f8af0124b074816890993df62d99ab7",
    "user_name": "1871002276023574528",
    "target_name": "饶孟良",
    "meta_data": {
      "year": "2025",
      "month": "6月",
      "day": "19",
      "hour": "16",
      "minute": "56",
      "second": "22",
      "week": "25",
      "weekday": "周四"
    }
  }
}
```

