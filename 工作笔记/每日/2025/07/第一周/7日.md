1.小程序、APP端首页改版接口对接
2.APP端 提pingCode bug修复
3.正式环境发版  首页功能上线
4.熟悉 饮食记录新功能 流程梳理

季度总结
1.食神多模态对话中，菜谱识别功能对接、调试
2.短信发送失败设置默认验证码功能开发
3.App端接口对接和相关问题排查
4.workflow可视化功能开发"

1.完成食神中菜谱细粒度识别功能对接，已上线Dev环境
2.增加食神异常捕获，并推送钉钉消息功能
3.长期记忆架构讨论，prompt调整功能适配
4.与App端进行接口对接

1.食神长期记忆服务功能调研，长期记忆抽取、记忆过滤、记忆淘汰、定时淘汰功能更新
2.App端接口对接、case排查
3.菜谱识别功能 问题排查，bug修复
4.调整AI面诊 输出样式、多样性配置

1.食神长期记忆方案梳理，prompt调试，功能提测 ，bug 修复
2.App 提测bug 修改 。目前ASR ,TTS功能已上架
3.调整workflow可视页面（支持选择模型，更新入库，历史更新记录保存）

1.AI技术助理专利助理用户聊天数据每日推送
2.食神生图，图像多轮问答调整，防止生图进入多轮问答
3.食神AI面诊舌诊 相关建议调整只根据答案进行回答
4.APP端提测bug修复目前pingcode只有一个bug未复现

"1.食神拍照识别热量流程配置、功能开发，完成与算法功能联调；
2.人工客服功能开发 与前端完成调试；
3.坐席助手用户登录，注册接口功能开发；"

"1.人工客服功能小程序APP端对接，数据兼容，测试bug修改、功能上线
2.拍照识别热量功能开发、小程序端对接，bug修复
3.产品视频数据召回功能开发
4.提供APP端拍照识别食材免密接口"

"1.食神发版支持设备机器登录和APP端调试
2.食神长期记忆服务功能改造拆分
3.食神海外版功能梳理、调研"


"1.食神长期记忆功能优化，老数据清理，魔盒对接测试
2.完成历史记录过滤、人工服务卡片数据保存、用户对话Excel分类功能
3.梳理食神改版接口，完成置顶、更新、分享接口
4.第一季度专利与代理人沟通 第二季度专利撰写
5.食神海外版英文prompt调试适配"

"1.完成食神 用户举报、用户反馈、H5跳转配置、短链跳转接口并对接
2.长期记忆、食神中控发版，长期记忆老数据清洗更新
3.食神海外版prompt调整 翻译 适配
4.敏感词检测组件开发 更换最新敏感词检测服务 并测试"

"1.技术助理钉钉助手正式环境上线 模版调整功能适配
2.食神bug修复 上线正式环境(图像分析时多图导致问答失效、特殊类型格式图片无法回答、新增END节点)
3.APP端 H5 端 接口对接 问题分析
4.专利交底书撰写提交"