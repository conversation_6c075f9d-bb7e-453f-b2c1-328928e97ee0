1. 设备蓝牙连接
2. 连接列表
3. 用户个人信息（头像、昵称、称号、脉搏、燃脂心率、心率、千卡、连接设备）
4. 健康膳食（早中晚）建议
5. AI健康评估（7天 30天）
6. 个人健康信息
7. 舌诊
8. 脉诊
9. 语音问诊
10. 健康检测结果（个人健康信息、BMI 健康评估、管理目标、健康饮食、药食同源、总热量、推荐菜谱、饮品、运动生活）
11. 综合结论 智能脉诊  AI面诊 AI舌诊 总分计算
12. 家庭账号

**DEBUG**调试信息，开发时使用，用于定位逻辑错误或流程细节。生产环境可按需开启。

**INFO**正常运行中的关键操作、业务流程节点、启动/关闭信息等。

**WARN**潜在问题，但不影响系统正常运行，如配置加载失败但有默认值、资源临时不可用。

**ERROR**错误事件，必须人工介入处理，如异常抛出、服务调用失败、数据库连接中断等

推荐写法

```java
logger.info("User {} logged in at {}", userId, loginTime);
```

不推荐写法

```java
logger.info("用户" + username + "登录成功");
```

### 错误信息打印规范

####  打印完整异常堆栈信息

```java
try {

// do something

} catch (Exception e) {

logger.error("发生异常", e); // 输出完整的堆栈信息

}

```

#### 入参出参打印日志

建议在与接口相关的日志以及关键方法的入参和返回值中添加日志，以便更好地追踪和调试代码。

```java
    public String myMethod(String param1, int param2) {
        logger.info("Entering myMethod. Param1: {}, Param2: {}", param1, param2);
        String result = "some result";
        logger.info("Exiting myMethod. Result: {}", result);
        return result;
    }
```

##### 日志要使用英文

建议在打印日志时使用英文，因为使用中文，可能会有两个问题：

- 中文日志可能会导致编码问题，在日志文件里出现乱码。
- 大多数日志分析工具都是基于英文字符进行匹配和检索的，使用中文可能影响日志的查询和分析。

#### 多分支首行打印日志

在条件分支比较复杂的情况下，建议在进入分支的首行打印相关信息，方便在调试，或者出现问题的时候，知道我们的代码进入的是哪一个分支。

```java
    public void process(String type) {
        if ("xxx".equals(type)) {
            logger.info("Current branch type: xxx");
            // Branch xxx logic
        } else if ("aaa".equals(type)) {
            logger.info("Current branch type: aaa");
            // Branch aaa logic
        } else {
            logger.info("Current branch type: other");
            // Default branch logic
        }
    }
```

打印String日志

```java
public void doSomeThing() {
    log.info("doSomeThing and print log, data={}", ToStringBuilder.reflectionToString(data, ToStringStyle.SHORT_PREFIX_STYLE));
}
```

##### 日志打印不能出现异常

这个反例里直接调用了`data.toString()`方法来打印日志。如果`data`对象为`null`，那么在调用`toString()`方法时会抛出空指针异常，影响正常的业务流程。

```java
public void doSomeThing() {
    log.info("doSomeThing and print log, data={}", data.toString());
    // 业务逻辑
    ...
}
```

##### 异常日志打印完整堆栈

为了打印完整的堆栈信息，我们可以使用日志记录框架提供的方法，将异常对象作为参数传递给日志记录方法。

```java
try {
    // 业务代码处理
} catch (Exception e) {
    log.error("An error occurred:", e);
}
```

##### 不要嵌套异常

```java
try {
    // 业务代码处理
    try {
        // 业务代码处理
    } catch (Exception e) {
        log.error("Your program has an exception", e);
    }
} catch (Exception e) {
    log.error("Your program has an exception", e);
}
```

##### 不要记录异常又抛出

下面的反例中，异常被记录后又抛出了自定义异常。这样做的问题在于，外层可能不会再次处理内层抛出的异常，导致问题得不到正确的处理。

```java
try {
    // 业务代码处理
} catch (Exception e) {
    log.error("IO exception", e);
    throw new MyException(e);
}
```

##### 不要在循环中打印 INFO 级别日志

```java
public void doSomeThing() {
    for(String s : strList) {
        log.info("doSomeThing and print log: {}", s);
        // 业务逻辑
        ...
    }
}
```

##### 对于 trace/debug 级别的日志输出，必须要有开关

```java
public void doSomeThing() {
    String name = "xxx";
    
    if (logger.isTraceEnabled()) {
        logger.trace("print trace log {}", name); 
    }
    
    if (logger.isDebugEnabled()) {
        logger.debug("print debug log {}", name); 
    }
    
    // 业务逻辑
    ...
}
```

