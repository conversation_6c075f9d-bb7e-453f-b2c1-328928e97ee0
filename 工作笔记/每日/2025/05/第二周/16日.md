1.视频输出
2.热量多轮
3.输出调整
4.拍热量 调整IF

```jav
package com.robam.roki.ai.agent.manager.cookerygod.components.cookeryPostProcess;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.robam.ai.aio.common.utils.JsonUtils;
import com.robam.roki.ai.agent.manager.ckb.enums.CacheType;
import com.robam.roki.ai.agent.manager.ckb.model.ComponentConfig;
import com.robam.roki.ai.agent.manager.ckb.model.KnowledgeBase;
import com.robam.roki.ai.agent.manager.ckb.opensearch.model.vo.RetrievalReqVo;
import com.robam.roki.ai.agent.manager.ckb.retrieval.model.FileChunk;
import com.robam.roki.ai.agent.manager.ckb.retrieval.service.RetrievalService;
import com.robam.roki.ai.agent.manager.ckb.service.DBCacheService;
import com.robam.roki.ai.agent.manager.conf.ThreadPoolExecutorConfiguration;
import com.robam.roki.ai.agent.manager.cookerygod.components.cookbookRecommend.model.CookbookRecommendConfig;
import com.robam.roki.ai.agent.manager.cookerygod.enums.CookBookTypeEnum;
import com.robam.roki.ai.agent.manager.cookerygod.enums.ImageType;
import com.robam.roki.ai.agent.manager.cookerygod.model.CookBookDetail;
import com.robam.roki.ai.agent.manager.cookerygod.model.CookeryRetrievalConfig;
import com.robam.roki.ai.agent.manager.cookerygod.model.DetectIngredients;
import com.robam.roki.ai.agent.manager.cookerygod.service.CookeryConfigService;
import com.robam.roki.ai.agent.manager.cookerygod.service.FoodIngredientInfoService;
import com.robam.roki.ai.agent.manager.remote.model.response.cookbook.CookbookSearchResp;
import com.robam.roki.ai.agent.manager.remote.service.CookbookService;
import com.robam.roki.ai.agent.manager.shopguide.exceptions.RetrievalException;
import com.robam.roki.ai.agent.manager.workflow.base.BaseMethodNodeComponent;
import com.robam.roki.ai.agent.manager.workflow.constant.ComponentName;
import com.robam.roki.ai.agent.manager.workflow.context.MdcContextWrapper;
import com.robam.roki.ai.agent.manager.workflow.enums.SystemVariableEnum;
import com.robam.roki.ai.agent.manager.workflow.model.ComponentReq;
import com.robam.roki.ai.agent.manager.workflow.model.ComponentResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.robam.roki.ai.agent.manager.constant.AnswerTagNameConstants.*;
import static com.robam.roki.ai.agent.manager.cookerygod.components.cookbookRecommend.CookbookRecommendComponent.COOKBOOK_RECOMMEND_CONFIG_KEY;

@Slf4j
@Component(ComponentName.COOKERY_POST_PROCESS)
public class CookeryPostProcessComponent implements BaseMethodNodeComponent {
    // 定义输入参数名
    private final String INPUT_COOKERY_ANSWER = "cookeryAnswer";
    private final String INPUT_RETRIEVED_COOKBOOKS = "cookbooks";
    private final String INPUT_LLM_RECOMMENDED_COOKBOOKS = "recommendedCookbooks";
    private final String INPUT_COOKERY_CHUNKS = "cookeryFileChunks";
    private final String INPUT_OTHER_CHUNKS = "otherFileChunks";
    private final String INPUT_FOODSTUFFS_CHUNKS = "foodstuffsFileChunks";

    private final String INPUT_VIDEO_CHUNKS = "videoFileChunks";

    private final String INPUT_LLM_VIDEO_CHUNKS = "videoLLMChunk";
    private final String INPUT_PRODUCT_CHUNKS = "productFileChunks";
    private final String INPUT_PRODUCT_NAMES = "productNames";
    private final String INPUT_PRODUCT_TYPES = "productTypes";
    private final String INPUT_COOKBOOK_CARDS = "cookbookCards";
    private final String INPUT_COOKBOOK_INGREDIENT_THEMES = "cookbookIngredientThemes";
    private final String INPUT_REWRITTEN_QUERY = "rewrittenQuery";
    private final String INPUT_IMAGE_ANALYSIS = "imageAnalysis";
    private final String INPUT_OBESITY_NUTRITION_FILE_CHUNKS = "obesityNutritionFileChunks";
    // 定义输出参数名
    private final String OUTPUT_RECOMMENDED_COOKBOOKS = "postProcessedCookbooks";
    private final String OUTPUT_CITATION_FILES = "citationFiles";

    // 组件名
    private final String NAME = ComponentName.COOKERY_GOD_ROUTER;

    // 组件功能
    private final String DESCRIPTION = "Cookery God router";

    // 组件输入参数，会被用于输入参数验证
    private final Map<String, Class<?>> INPUT_PARAMS = Map.ofEntries(
            Map.entry(INPUT_COOKERY_ANSWER, String.class),
            Map.entry(INPUT_RETRIEVED_COOKBOOKS, List.class),
            Map.entry(INPUT_LLM_RECOMMENDED_COOKBOOKS, List.class),
            Map.entry(INPUT_COOKERY_CHUNKS, List.class),
            Map.entry(INPUT_OTHER_CHUNKS, List.class),
            Map.entry(INPUT_FOODSTUFFS_CHUNKS, List.class),
            Map.entry(INPUT_PRODUCT_CHUNKS, List.class),
            Map.entry(INPUT_COOKBOOK_CARDS, List.class),
            Map.entry(INPUT_COOKBOOK_INGREDIENT_THEMES, List.class),
            Map.entry(INPUT_REWRITTEN_QUERY, String.class),
            Map.entry(INPUT_VIDEO_CHUNKS, List.class),
            Map.entry(INPUT_LLM_VIDEO_CHUNKS, String.class),
            Map.entry(INPUT_IMAGE_ANALYSIS, List.class),
            Map.entry(INPUT_PRODUCT_NAMES, List.class),
            Map.entry(INPUT_PRODUCT_TYPES, List.class),
            Map.entry(INPUT_OBESITY_NUTRITION_FILE_CHUNKS, List.class)
    );

    // 组件输出参数，会被用于输出参数验证
    private final Map<String, Class<?>> OUTPUT_PARAMS = Map.of(
            OUTPUT_RECOMMENDED_COOKBOOKS, List.class,
            OUTPUT_CITATION_FILES, List.class
    );
    @Autowired
    private FoodIngredientInfoService foodIngredientInfoService;
    @Autowired
    private DBCacheService cacheService;
    @Autowired
    private CookeryConfigService cookeryConfigService;
    @Autowired
    private RetrievalService retrievalService;
    @Autowired
    private CookbookService cookbookService;

    @Override
    public ComponentResp execute(ComponentReq req) {
        try {
            // 并行处理cookbook和citation
            CompletableFuture<List<CookBookDetail.CookbookCard>> cookbooksFuture = CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() ->
                    processCookbooks(req)));
            CompletableFuture<List<FileChunk>> citationsFuture = CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() ->
                    citationFilesFilter(req)));
            CompletableFuture<List<FileChunk>> videoFuture = CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() ->
                    searchVideos(req)));

            // 等待所有任务完成
            CompletableFuture.allOf(cookbooksFuture, citationsFuture).get(4, TimeUnit.SECONDS);
            List<CookBookDetail.CookbookCard> cookbookCards = cookbooksFuture.getNow(List.of());
            List<FileChunk> citations = citationsFuture.getNow(List.of());
            return new ComponentResp(true, "", Map.of(
                    OUTPUT_RECOMMENDED_COOKBOOKS, cookbookCards,
                    OUTPUT_CITATION_FILES, citations
            ));
        } catch (Exception e) {
            log.error("Error in CookeryPostProcessComponent", e);
            return new ComponentResp(true, "Error occurred: " + e.getMessage(), Map.of(
                    OUTPUT_RECOMMENDED_COOKBOOKS,
                    req.getParams().get(INPUT_LLM_RECOMMENDED_COOKBOOKS) != null
                            ? req.getParams().get(INPUT_LLM_RECOMMENDED_COOKBOOKS)
                            : List.of(),
                    OUTPUT_CITATION_FILES, List.of()
            ));
        }
    }

    private List<CookBookDetail.CookbookCard> processCookbooks(ComponentReq req) {
        long startTime = System.currentTimeMillis();
        // 获取并检查输入参数
        String cookeryAnswer = req.getParams().get(INPUT_COOKERY_ANSWER) != null
                ? req.getParams().get(INPUT_COOKERY_ANSWER).toString()
                : "";
        List<CookBookDetail.CookbookCard> cookbookCards = (List<CookBookDetail.CookbookCard>) req.getParams().get(INPUT_COOKBOOK_CARDS);
        List<CookBookDetail> retrievedCookbooks = (List<CookBookDetail>) req.getParams().get(INPUT_RETRIEVED_COOKBOOKS);
        List<CookBookDetail.CookbookCard> recommendedCookbooks = (List<CookBookDetail.CookbookCard>) req.getParams().get(INPUT_LLM_RECOMMENDED_COOKBOOKS);
        List<String> ingredientThemes = (List<String>) req.getParams().get(INPUT_COOKBOOK_INGREDIENT_THEMES);
        List<Map<String, String>> imageAnalysis = (List<Map<String, String>>) req.getParams().get(INPUT_IMAGE_ANALYSIS);
        List<String> imageIngredients = Optional.ofNullable(imageAnalysis).orElse(List.of()).stream()
                .filter(info -> ImageType.INGREDIENT.name().equals(info.get("type")))
                .map(info -> info.get("details"))
                .filter(StringUtils::isNotBlank)
                .map(s -> {
                    // 移除首尾的方括号
                    String cleaned = s.replaceAll("^\\[|\\]$", "");
                    // 按逗号分割（如果有多个值的情况）
                    return Arrays.asList(cleaned.split(","));
                })
                .flatMap(List::stream)
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        List<CookBookDetail.CookbookCard> mergedCookbooks = new ArrayList<>();
        String query = req.getParams().get(INPUT_REWRITTEN_QUERY) != null
                ? req.getParams().get(INPUT_REWRITTEN_QUERY).toString()
                : req.getSystemParams().get(SystemVariableEnum.QUERY).toString();

        boolean onlyFoodIngredient = ingredientThemes != null &&
                !ingredientThemes.isEmpty() &&
                ingredientThemes.stream().allMatch(theme -> theme.contains("食材"));

        Set<String> extractedDishNames = extractDishNames(cookeryAnswer);
        // 如果retrievedCookbooks为空，extractedDishNames为空，直接返回recommendedCookbooks
        if ((extractedDishNames == null || extractedDishNames.isEmpty()) && (retrievedCookbooks == null || retrievedCookbooks.isEmpty()) && imageIngredients.isEmpty()) {
            return recommendedCookbooks != null ? recommendedCookbooks : List.of();
        }

        // 获取配置
        CookbookRecommendConfig recommendConfig = null;
        try {
            Map<String, ComponentConfig> cookeryGodConfigMap = cacheService.getDataMapByType(CacheType.COOKERY_GOD_CONFIG);
            ComponentConfig recommendConfigNode = cookeryGodConfigMap.get(COOKBOOK_RECOMMEND_CONFIG_KEY);
            if (recommendConfigNode != null && recommendConfigNode.getJsonValue() != null) {
                recommendConfig = JsonUtils.readValue(recommendConfigNode.getJsonValue(), CookbookRecommendConfig.class);
            }
        } catch (Exception e) {
            log.warn("Failed to get recommend config", e);
        }

        // 过滤和排序matchedCookbooks
        List<CookBookDetail> matchedCookbooks = retrievedCookbooks.stream()
                .filter(cookbook -> cookbook != null
                        && cookbook.getName() != null
                        && cookbook.getCookbookCard() != null
                        && cookeryAnswer.contains(cookbook.getName()))
                .sorted(Comparator.comparingInt(cookbook -> cookeryAnswer.indexOf(cookbook.getName())))
                .toList();

        log.info("processCookbooks matched cookbooks from answer: {}",
                matchedCookbooks.stream().map(CookBookDetail::getName).collect(Collectors.toList()));

        Set<Integer> seenIds = new HashSet<>();
        Set<String> matchedDishNames = new HashSet<>();

        // 添加matchedCookbooks,优先answer中精确出现
        for (CookBookDetail cookbook : matchedCookbooks) {
            if (cookbook.getCookbookCard() != null
                    && cookbook.getCookbookCard().getId() != null
                    && seenIds.add(cookbook.getCookbookCard().getId())) {
                mergedCookbooks.add(cookbook.getCookbookCard());
                matchedDishNames.add(cookbook.getName());
            }
        }
        // 取query中出现的菜
        if (retrievedCookbooks != null) {
            for (CookBookDetail cookbook : retrievedCookbooks) {
                if (cookbook != null
                        && cookbook.getId() != null
                        && query.contains(cookbook.getName())
                        && seenIds.add(cookbook.getId())
                ) {
                    mergedCookbooks.add(cookbook.getCookbookCard());
                }
            }
        }
        // 查询未匹配的菜名对应的菜谱
        Set<String> unmatchedDishNames = new HashSet<>(extractedDishNames);
        unmatchedDishNames.removeAll(matchedDishNames);
        List<CookBookDetail.CookbookCard> answerCookbooks = CollUtil.newArrayList();
        log.info("processCookbooks answer string matched cookbooks:{}, rest cookbooks: {}", matchedDishNames, unmatchedDishNames);
        if (!unmatchedDishNames.isEmpty()) {
            // 再加上answer中出现的菜名，去查一次菜谱，名字一样的排前面，没有名字一样的，就每组取top1，然后把各组结果合并后按分数排序
            List<CookBookDetail.CookbookCard> additionalCookbooks = queryAdditionalCookbooks(unmatchedDishNames, recommendConfig, onlyFoodIngredient);
            for (CookBookDetail.CookbookCard cookbook : additionalCookbooks) {
                if (cookbook != null && cookbook.getId() != null && seenIds.add(cookbook.getId())) {
                    mergedCookbooks.add(cookbook);
                    answerCookbooks.add(cookbook);
                }
            }
            log.info("processCookbooks additional cookbooks added: {}", additionalCookbooks.stream().map(CookBookDetail.CookbookCard::getName).collect(Collectors.toList()));
        }

        // 当非食材主题时，加上大模型推荐的菜
        if (recommendedCookbooks != null && !onlyFoodIngredient) {
            for (CookBookDetail.CookbookCard cookbook : recommendedCookbooks) {
                if (cookbook != null
                        && cookbook.getId() != null
                        && seenIds.add(cookbook.getId())) {
                    mergedCookbooks.add(cookbook);
                }
            }
        }


        // 限制推荐数量
        if (recommendConfig != null && recommendConfig.getTotalCookbookRecommendNum() != null) {
            int maxRecommendedCookbooks = recommendConfig.getTotalCookbookRecommendNum();
            if (maxRecommendedCookbooks > 0 && mergedCookbooks.size() > maxRecommendedCookbooks) {
                mergedCookbooks = mergedCookbooks.subList(0, maxRecommendedCookbooks);
            }
        }
        Set<String> existingIngredients = new HashSet<>();
        if (cookbookCards != null) { // 加上相关食材卡片
            for (CookBookDetail.CookbookCard cookbookCard : cookbookCards) {
                if (cookbookCard != null
                        && cookbookCard.getId() != null
                        && cookbookCard.getType().equals(CookBookTypeEnum.FOOD_IDENTIFICATION.getName())) {
                    existingIngredients.add(cookbookCard.getName());
                    mergedCookbooks.add(cookbookCard);
                }
            }
        }
        if (onlyFoodIngredient || !imageIngredients.isEmpty()) { // 主题只有食材，则从答案中提取食材，优先答案提取的食材，再merge上cookbookCards中的食材，要去重，只推食材卡片
            // 从答案中提取所有菜名
            Set<String> targetIngredients = extractIngredients(cookeryAnswer);
            log.info("processCookbooks food ingredients only. Extracted ingredients from answer: {}", targetIngredients);
            if (!imageIngredients.isEmpty()) {
                targetIngredients.addAll(imageIngredients);
                log.info("processCookbooks food ingredients only. Extracted ingredients from image: {}", imageIngredients);
            }
            // 移除已存在的食材
            targetIngredients.removeAll(existingIngredients);

            // 查询新的食材并合并结果
            if (!targetIngredients.isEmpty()) {
                try {
                    List<CookBookDetail.CookbookCard> newIngredientCards = searchFoodIngredients(new ArrayList<>(targetIngredients))
                            .get(2, TimeUnit.SECONDS);
                    mergedCookbooks.addAll(newIngredientCards.stream().filter(ingredientCard -> !existingIngredients.contains(ingredientCard.getName())).toList());
                } catch (Exception e) {
                    log.error("Error searching for food ingredients", e);
                }
            }
        }
        if (!mergedCookbooks.isEmpty() && mergedCookbooks.size() < 3 && !answerCookbooks.isEmpty()) {
            try {
                int needMore = 3 - mergedCookbooks.size();
                Set<Integer> cookbookIds = mergedCookbooks.stream()
                        .filter(Objects::nonNull)
                        .map(cookbook -> {
                            if (cookbook.getId() == null) {
                                log.warn("Found cookbook with null id in mergedCookbooks");
                                return -1;
                            }
                            return cookbook.getId();
                        })
                        .collect(Collectors.toSet());
                List<CookBookDetail.CookbookCard> supplementCookbooks = answerCookbooks.stream()
                        .filter(cookbook -> cookbook != null
                                && cookbook.getId() != null
                                && !cookbookIds.contains(cookbook.getId()))
                        .limit(needMore)
                        .toList();
                if (!supplementCookbooks.isEmpty()) {
                    mergedCookbooks.addAll(supplementCookbooks);
                    log.info("Supplemented {} cookbooks from answer cookbooks", supplementCookbooks.size());
                }
            } catch (Exception e) {
                log.error("Error while supplementing cookbooks", e);
            }
        }

        log.info("CookeryPostProcessComponent: processCookbooks took {} ms", System.currentTimeMillis() - startTime);
        return mergedCookbooks;
    }

    private CompletableFuture<List<CookBookDetail.CookbookCard>> searchFoodIngredients(List<String> query) {
        return CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            try {
                if (CollUtil.isEmpty(query)) {
                    return List.of();
                }
                List<DetectIngredients.DetectIngredientsResultResp> allFoodIngredientInfoByName = foodIngredientInfoService.findAllFoodIngredientInfoByName(query);
                if (CollUtil.isEmpty(allFoodIngredientInfoByName)) {
                    return List.of();
                }
                ArrayList<CookBookDetail.CookbookCard> result = CollUtil.newArrayList();
                HashSet<String> nameSet = CollUtil.newHashSet();
                allFoodIngredientInfoByName.forEach(detectIngredientsResultResp -> {
                    if (nameSet.contains(detectIngredientsResultResp.getName())) {
                        return;
                    }
                    nameSet.add(detectIngredientsResultResp.getName());
                    CookBookDetail.CookbookCard cookbookCard = new CookBookDetail.CookbookCard();
                    cookbookCard.setId(Convert.toInt(detectIngredientsResultResp.getId()));
                    cookbookCard.setName(detectIngredientsResultResp.getName());
                    cookbookCard.setImgCover11(detectIngredientsResultResp.getCoverUrl());
                    cookbookCard.setType(CookBookTypeEnum.FOOD_IDENTIFICATION.getName());
                    cookbookCard.setIntroduction(detectIngredientsResultResp.getDescription());
                    result.add(cookbookCard);
                });
                return result;
            } catch (RetrievalException e) {
                log.info(e.getMessage());
            } catch (Exception e) {
                log.error("Error during searchFoodIngredients", e);
            } finally {
                stopWatch.stop();
                log.info("cookeryRetrieval searchFoodIngredients execution time: {} ms", stopWatch.getTotalTimeMillis());
            }
            return List.of();
        }), ThreadPoolExecutorConfiguration.COOKERY_EXECUTOR);
    }

    private Set<String> extractDishNames(String answer) {
        Set<String> dishNames = new HashSet<>();
        String pattern = DISH_START_TAG + "(.+?)" + DISH_END_TAG;
        java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = r.matcher(answer);

        while (m.find()) {
            dishNames.add(m.group(1).trim());
        }
        return dishNames;
    }

    // 添加提取食材的方法
    private Set<String> extractIngredients(String answer) {
        Set<String> ingredients = new HashSet<>();
        String pattern = FOOD_START_TAG + "(.+?)" + FOOD_END_TAG;
        java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = r.matcher(answer);

        while (m.find()) {
            String[] foods = m.group(1).split("[、，]");
            for (String food : foods) {
                ingredients.add(food.trim());
            }
        }
        return ingredients;
    }

    private List<CookBookDetail.CookbookCard> queryAdditionalCookbooks(Set<String> dishNames, CookbookRecommendConfig config, boolean onlyExactMatch) {
        long startTime = System.currentTimeMillis();
        final double cookbookNameThreshold = config != null && config.getCookbookNameSimilarityThreshold() != null
                ? config.getCookbookNameSimilarityThreshold()
                : 3d;
        List<CompletableFuture<Map.Entry<String, List<CookbookSearchResp>>>> recommendedFutures = new ArrayList<>();
        if (CollUtil.isNotEmpty(dishNames)) {
            for (String cookbookName : dishNames) {
                CompletableFuture<Map.Entry<String, List<CookbookSearchResp>>> future = CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() -> {
                    RetrievalReqVo.RetrievalReqVoBuilder builder = RetrievalReqVo.builder()
                            .query(cookbookName)
                            .cookSearchTopK(3)
                            .cookSearchScore(cookbookNameThreshold);

                    List<CookbookSearchResp> results = retrievalService.searchCookBook(builder.build());
                    return Map.entry(cookbookName, results);
                }), ThreadPoolExecutorConfiguration.COOKERY_EXECUTOR);
                recommendedFutures.add(future);
            }
        }

        List<CookBookDetail.CookbookCard> result = new ArrayList<>();
        try {
            CompletableFuture.allOf(recommendedFutures.toArray(new CompletableFuture[0])).get(2, TimeUnit.SECONDS);

            // 使用LinkedHashMap保持顺序
            Map<Integer, String> exactMatchMap = new LinkedHashMap<>();
            List<Map.Entry<Integer, Double>> topScoreList = new ArrayList<>();

            // 处理每个查询结果
            for (CompletableFuture<Map.Entry<String, List<CookbookSearchResp>>> future : recommendedFutures) {
                Map.Entry<String, List<CookbookSearchResp>> entry = future.get();
                String queryName = entry.getKey();
                List<CookbookSearchResp> searchResults = entry.getValue();

                if (CollUtil.isEmpty(searchResults)) {
                    continue;
                }

                // 找到完全匹配的结果
                Optional<CookbookSearchResp> exactMatch = searchResults.stream()
                        .filter(resp -> resp.getName() != null &&
                                resp.getName().trim().equals(queryName.trim()))
                        .findFirst();

                if (exactMatch.isPresent()) {
                    // 完全匹配的结果
                    exactMatchMap.put(exactMatch.get().getCookbookId(), exactMatch.get().getName());
                } else {
                    // 没有完全匹配，取top1并记录分数
                    CookbookSearchResp top1 = searchResults.get(0);
                    topScoreList.add(Map.entry(top1.getCookbookId(), top1.getScore()));
                }
            }

            // 对非完全匹配的结果按分数排序
            topScoreList.sort((a, b) -> Double.compare(b.getValue(), a.getValue()));

            // 合并ID列表，保持顺序（完全匹配的在前，其他按分数排序）
            List<Integer> orderedIds = new ArrayList<>();
            orderedIds.addAll(exactMatchMap.keySet());
            if (!onlyExactMatch) {
                orderedIds.addAll(topScoreList.stream()
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList()));
            }

            // 批量查询菜谱详情
            if (!orderedIds.isEmpty()) {
                List<CookBookDetail> cookBookDetails = cookbookService.cookbookDetail(orderedIds);

                // 创建ID到Detail的映射
                Map<Integer, CookBookDetail> idToDetailMap = cookBookDetails.stream()
                        .collect(Collectors.toMap(CookBookDetail::getId, detail -> detail, (existing, replacement) -> existing));

                // 按原有顺序重建结果列表
                for (Integer id : orderedIds) {
                    CookBookDetail detail = idToDetailMap.get(id);
                    if (detail != null && detail.getCookbookCard() != null) {
                        result.add(detail.getCookbookCard());
                    }
                }
            }
            log.info("Additional cookbook query complete. Exact matches: {}, Score-based matches: {}. Query took {} ms",
                    exactMatchMap.size(), topScoreList.size(), System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("Error while querying additional cookbooks", e);
        }

        return result;
    }

    private List<FileChunk> citationFilesFilter(ComponentReq req) {
        long startTime = System.currentTimeMillis();
        Map<String, KnowledgeBase> knowledgeBaseMap = cacheService.getDataMapByType(CacheType.KNOWLEDGE_BASE);
        List<String> productNames = (List<String>) req.getParams().get(INPUT_PRODUCT_NAMES);
        List<String> productTypes = (List<String>) req.getParams().get(INPUT_PRODUCT_TYPES);
        boolean hasProduct = !productNames.isEmpty() || !productTypes.isEmpty();

        CookeryRetrievalConfig cookeryRetrievalConfig = cookeryConfigService.getCookeryRetrievalConfig();
        Set<Long> productCategoryIds = new HashSet<>();
        if (cookeryRetrievalConfig != null && cookeryRetrievalConfig.getCategoryConfig() != null) {
            productCategoryIds.addAll(cookeryRetrievalConfig.getCategoryConfig().getProductCategories());
        }

        // 合并所有chunks并过d滤
        return Stream.of(
                        (List<FileChunk>) req.getParams().get(INPUT_PRODUCT_CHUNKS),
                        (List<FileChunk>) req.getParams().get(INPUT_FOODSTUFFS_CHUNKS),
                        (List<FileChunk>) req.getParams().get(INPUT_COOKERY_CHUNKS),
                        (List<FileChunk>) req.getParams().get(INPUT_OTHER_CHUNKS),
                        (List<FileChunk>) req.getParams().get(INPUT_OBESITY_NUTRITION_FILE_CHUNKS)
                )
                .flatMap(Collection::stream)
                .filter(chunk -> {
                    KnowledgeBase kb = knowledgeBaseMap.get(chunk.getKnowledgeBaseId().toString());
                    if (productCategoryIds.contains(kb.getCategoryId()) && !hasProduct) { // 未抽取出产品相关实体时不展示相关引用
                        return false;
                    }
                    return kb != null && kb.getSourceVisibility() == 1;
                })
                .peek(__ -> log.info("CookeryPostProcessComponent: citationFilesFilter took {} ms",
                        System.currentTimeMillis() - startTime))
                .collect(Collectors.toList());  // Simply collect to list
    }


    private List<FileChunk> searchVideos(ComponentReq req) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            log.info("start searchVideos");
            if (req == null || req.getParams() == null || req.getSystemParams() == null) {
                log.warn("Invalid request parameters for video search");
                return List.of();
            }

            // 获取视频chunks和LLM处理后的视频chunks
            List<FileChunk> videoChunks = (List<FileChunk>) req.getParams().get(INPUT_VIDEO_CHUNKS);
            String videoLLMChunk = (String) req.getParams().get(INPUT_LLM_VIDEO_CHUNKS);
            
            // 如果没有视频相关内容,直接返回空列表
            if (CollUtil.isEmpty(videoChunks)) {
                return List.of();
            }

            // 获取知识库配置
            Map<String, KnowledgeBase> knowledgeBaseMap = cacheService.getDataMapByType(CacheType.KNOWLEDGE_BASE);
            
            // 如果有LLM处理结果，解析索引
            if (StringUtils.isNotBlank(videoLLMChunk)) {
                // 解析索引格式 <x,y>
                if (videoLLMChunk.matches("<\\d+,\\d+>")) {
                    String[] indices = videoLLMChunk.substring(1, videoLLMChunk.length() - 1).split(",");
                    int fileChunkIndex = Integer.parseInt(indices[0]);
                    int contentChunkIndex = Integer.parseInt(indices[1]);
                    
                    // 确保索引有效
                    if (fileChunkIndex < videoChunks.size()) {
                        FileChunk targetChunk = videoChunks.get(fileChunkIndex);
                        // 检查知识库可见性
                        KnowledgeBase kb = knowledgeBaseMap.get(targetChunk.getKnowledgeBaseId().toString());
                        if (kb != null && kb.getSourceVisibility() == 1) {
                            return List.of(targetChunk);
                        }
                    }
                }
                return List.of();
            }

            // 如果没有LLM处理结果，返回所有可见的视频chunks
            return videoChunks.stream()
                .filter(chunk -> {
                    KnowledgeBase kb = knowledgeBaseMap.get(chunk.getKnowledgeBaseId().toString());
                    return kb != null && kb.getSourceVisibility() == 1;
                })
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error during searchVideos", e);
            return List.of();
        } finally {
            stopWatch.stop();
            log.info("{} searchVideos execution time: {} ms", NAME, stopWatch.getTotalTimeMillis());
        }
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }

    @Override
    public Map<String, Class<?>> getInputParams() {
        return INPUT_PARAMS;
    }

    @Override
    public Map<String, Class<?>> getOutputParams() {
        return OUTPUT_PARAMS;
    }
}

```

```java
package com.robam.roki.ai.agent.manager.shopguide.components.retrieval;

import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.robam.ai.aio.common.utils.JsonUtils;
import com.robam.roki.ai.agent.manager.api.model.ChatMessage;
import com.robam.roki.ai.agent.manager.ckb.enums.CacheType;
import com.robam.roki.ai.agent.manager.ckb.mapper.AssistantAgentMapper;
import com.robam.roki.ai.agent.manager.ckb.model.AssistantAgentConfig;
import com.robam.roki.ai.agent.manager.ckb.model.ComponentConfig;
import com.robam.roki.ai.agent.manager.ckb.model.KnowledgeBase;
import com.robam.roki.ai.agent.manager.ckb.opensearch.model.vo.RetrievalReqVo;
import com.robam.roki.ai.agent.manager.ckb.retrieval.model.QAGroup;
import com.robam.roki.ai.agent.manager.ckb.retrieval.model.SearchChunkResult;
import com.robam.roki.ai.agent.manager.ckb.retrieval.service.RetrievalService;
import com.robam.roki.ai.agent.manager.ckb.service.DBCacheService;
import com.robam.roki.ai.agent.manager.conf.ThreadPoolExecutorConfiguration;
import com.robam.roki.ai.agent.manager.cookerygod.model.CookeryRetrievalConfig;
import com.robam.roki.ai.agent.manager.cookerygod.service.CookeryConfigService;
import com.robam.roki.ai.agent.manager.embedding.model.EmbeddingResult;
import com.robam.roki.ai.agent.manager.embedding.service.EmbeddingService;
import com.robam.roki.ai.agent.manager.shopguide.exceptions.RetrievalException;
import com.robam.roki.ai.agent.manager.shopguide.model.Product;
import com.robam.roki.ai.agent.manager.shopguide.model.ProductCategory;
import com.robam.roki.ai.agent.manager.shopguide.model.dto.ProductKnowledgeBaseDTO;
import com.robam.roki.ai.agent.manager.workflow.base.BaseMethodNodeComponent;
import com.robam.roki.ai.agent.manager.workflow.constant.ComponentName;
import com.robam.roki.ai.agent.manager.workflow.constant.EnvKey;
import com.robam.roki.ai.agent.manager.workflow.context.MdcContextWrapper;
import com.robam.roki.ai.agent.manager.workflow.enums.SystemVariableEnum;
import com.robam.roki.ai.agent.manager.workflow.model.ComponentReq;
import com.robam.roki.ai.agent.manager.workflow.model.ComponentResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.robam.roki.ai.agent.manager.cookerygod.components.constant.ConfigKey.RETRIEVAL_RULE_KEY_VIDEO;

@Slf4j
@Component(ComponentName.RETRIEVAL)
public class RetrievalComponent implements BaseMethodNodeComponent {
    public final static long SUITE_CATEGORY_ID = 10018; // 套系id，暂时先写死
    //    public final static long COOKERY_SUITE_CATEGORY_ID = 20017;
    private final static String AFTER_SALES_CONFIG = "warranty_config";
    private final static String PRODUCT_WARRANTY_RULE = "warranty_rule";
    private final static String PRODUCT_KEY_PARTS = "key_parts";
    private final static String WARRANTY_RULE_COMMON = "warranty_rule_common";
    private final static String WARRANTY_CASE = "warranty_case";
    // 定义输入参数名
    private final String INPUT_QUERY = "query";
    private final String INPUT_AGENT_ID = "agentId";
    private final String INPUT_PRODUCT_NAMES = "productNames";

    private final String INPUT_INTENT = "intent";
    private final String INPUT_PRODUCT_TYPES = "productTypes";
    private final String INPUT_PRODUCT_CATEGORYS = "productCategorys";
    private final String INPUT_CATEGORY_PRODUCTS_MODELS = "categoryProductsModels";
    private final String INPUT_OTHER_INTENTS = "otherIntents";
    // 定义输出参数名
    private final String OUTPUT_FILE_CHUNKS = "fileChunks";
    private final String OUTPUT_QA_GROUPS = "qaGroups";
    private final String OUTPUT_QA_ANSWER = "qaAnswer";
    private final String OUTPUT_PLACEHOLDER_MAP = "placeholderMap";
    private final String OUTPUT_AFTER_SALES_CONTENT = "afterSalesContent";

    private final String OUTPUT_VIDEO_FILE_CHUNKS = "videoFileChunks";
    // 组件名
    private final String NAME = ComponentName.CONVERSATION_ANALYSIS;
    // 组件功能
    private final String DESCRIPTION = "结合对话历史信息（可），对客户当前轮query进行对话理解，包括Query改写、意图识别和实体提取，实体范围包括：商品型号列表、商品类型、主推商品型号";
    // 组件输入参数，会被用于输入参数验证
    private final Map<String, Class<?>> INPUT_PARAMS = Map.of(
            INPUT_QUERY, String.class,
            INPUT_INTENT, String.class,
            INPUT_AGENT_ID, String.class,
            INPUT_PRODUCT_NAMES, List.class,
            INPUT_PRODUCT_TYPES, List.class,
            INPUT_PRODUCT_CATEGORYS, List.class,
            INPUT_CATEGORY_PRODUCTS_MODELS, List.class
    );
    // 组件输出参数，会被用于输出参数验证
    private final Map<String, Class<?>> OUTPUT_PARAMS = Map.of(
            OUTPUT_FILE_CHUNKS, List.class,
            OUTPUT_QA_ANSWER, String.class,
            OUTPUT_QA_GROUPS, List.class,
            OUTPUT_AFTER_SALES_CONTENT, String.class,
            OUTPUT_PLACEHOLDER_MAP, Map.class
    );


    private final EmbeddingService embeddingService;

    private final RetrievalService retrievalService;

    private final DBCacheService dbCacheService;

    private final AssistantAgentMapper assistantAgentMapper;

    private final CookeryConfigService cookeryConfigService;


    @Value("${retrieval.chunk.rerank-topK:5}")
    private Integer chunkRerankTopK;

    @Value("${retrieval.chunk.pre-rank-threshold:0.2d}")
    private Double chunkPreRankScoreThreshold;

    @Value("${retrieval.chunk.pre-rank-topK:50}")
    private Integer chunkPreRankTopK;

    @Value("${retrieval.chunk.hybrid-enabled:false}")
    private boolean isHybridEnabled;

    @Value("${retrieval.chunk.hyde-enabled:false}")
    private boolean isHydeEnabled;

    @Value("${retrieval.chunk.hyde-model-provider:qwen}")
    private String hydeModelProvider;

    @Value("${retrieval.chunk.hyde-model:qwen-plus}")
    private String hydeModel;

    @Value("${retrieval.qa.rerank-topK:5}")
    private Integer qaRerankTopK;

    @Value("${retrieval.qa.pre-rank-threshold:0.2d}")
    private Double qaPreRankScoreThreshold;

    @Value("${retrieval.qa.rerank-threshold:3d}")
    private Double qaRankScoreThreshold;

    @Value("${retrieval.chunk.rerank-threshold:-10d}")
    private Double chunkRankScoreThreshold;

    @Value("${retrieval.qa.direct-answer-threshold:8d}")
    private Double directAnswerThreshold;

    @Value("${retrieval.qa.pre-rank-topK:50}")
    private Integer qaPreRankTopK;

    @Value("${retrieval.chunk.max-chunk-length:5000}")
    private int maxChunkLength;
    @Value("${retrieval.cook.search-topK:5}")
    private Integer cookSearchTopK;
    @Value("${retrieval.cook.search-score:1.0}")
    private Double cookSearchScore;
    private final int defaultRetrievalTimeout = 8000;

    public RetrievalComponent(EmbeddingService embeddingService, RetrievalService retrievalService, DBCacheService dbCacheService, AssistantAgentMapper assistantAgentMapper,CookeryConfigService cookeryConfigService) {
        this.embeddingService = embeddingService;
        this.retrievalService = retrievalService;
        this.dbCacheService = dbCacheService;
        this.assistantAgentMapper = assistantAgentMapper;
        this.cookeryConfigService = cookeryConfigService;
    }

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }

    @Override
    public Map<String, Class<?>> getInputParams() {
        return INPUT_PARAMS;
    }

    @Override
    public Map<String, Class<?>> getOutputParams() {
        return OUTPUT_PARAMS;
    }

    @Override
    public ComponentResp execute(ComponentReq req) {
        Map<String, Object> output = new HashMap<>(Map.of(
                OUTPUT_QA_ANSWER, "",
                OUTPUT_FILE_CHUNKS, List.of(),
                OUTPUT_QA_GROUPS, List.of(),
                OUTPUT_AFTER_SALES_CONTENT, "",
                OUTPUT_PLACEHOLDER_MAP, Map.of()
        ));
        return Optional.ofNullable(req.getSystemParams().get(SystemVariableEnum.AGENT_ID))
                .map(Object::toString)
                .map(Long::parseLong)
                .map(assistantAgentMapper::selectById)
                .filter(agent -> agent.getIsDeleted() != 1)
                .map(agent -> {
                    String intent = req.getParams().get(INPUT_INTENT).toString();
                    AssistantAgentConfig config = JsonUtils.readValue(agent.getConfig(), AssistantAgentConfig.class);
                    CookeryRetrievalConfig cookeryRetrievalConfig = cookeryConfigService.getCookeryRetrievalConfig();
                    List<Long> videoCategoryIds = new ArrayList<>();
                    if (cookeryRetrievalConfig != null && cookeryRetrievalConfig.getCategoryConfig() != null) {
                        videoCategoryIds = cookeryRetrievalConfig.getCategoryConfig().getVideoCategories();
                    }
                    AssistantAgentConfig videoConfig = new AssistantAgentConfig();
                    Map<String, KnowledgeBase> knowledgeBaseMap = dbCacheService.getDataMapByType(CacheType.KNOWLEDGE_BASE);
                    List<AssistantAgentConfig.KnowledgeBaseConfig> videoBases = new ArrayList<>();
                    for (AssistantAgentConfig.KnowledgeBaseConfig kbConfig : config.getKnowledgeBases()) {
                        KnowledgeBase kb = knowledgeBaseMap.get(kbConfig.getId().toString());
                        if (videoCategoryIds.contains(kb.getId())) {
                            videoBases.add(kbConfig);
                        }
                    }
                    videoConfig.setKnowledgeBases(videoBases);
                    Map<String, CookeryRetrievalConfig.RetrievalRule> retrievalRuleMap = new HashMap<>();
                    if (cookeryRetrievalConfig.getRuleMap() != null) {
                        retrievalRuleMap = MapUtil.emptyIfNull(cookeryRetrievalConfig.getRuleMap().get(intent));
                    }

                    CookeryRetrievalConfig.RetrievalRule videoRetrievalRule = new CookeryRetrievalConfig.RetrievalRule(chunkRerankTopK, chunkRankScoreThreshold, cookSearchTopK, cookSearchScore, maxChunkLength, defaultRetrievalTimeout);
                    if (retrievalRuleMap.get(RETRIEVAL_RULE_KEY_VIDEO) != null) {
                        videoRetrievalRule = retrievalRuleMap.get(RETRIEVAL_RULE_KEY_VIDEO);
                    }
                    String query = req.getParams().get(INPUT_QUERY).toString();
                    String hydeContent;
                    if (isHydeEnabled) {
                        hydeContent = retrievalService.generateHypotheticalAnswers(hydeModelProvider, hydeModel, (List<ChatMessage>) req.getSystemParams().get(SystemVariableEnum.HISTORY), query, 1).stream().findFirst().orElse(null);
                    } else {
                        hydeContent = null;
                    }
                    CacheType configKey;
                    if (req.getEnvironmentParams().get(EnvKey.CONFIG_KEY) != null) {
                        configKey = CacheType.fromValue(req.getEnvironmentParams().get(EnvKey.CONFIG_KEY).toString());
                    } else {
                        configKey = CacheType.SHOP_GUIDE_CONFIG;
                    }
                    List<String> textsToEmbed = new ArrayList<>(List.of(query));
                    if (hydeContent != null) {
                        textsToEmbed.add(hydeContent);
                    }

                    StopWatch stopWatch = new StopWatch();
                    stopWatch.start();
                    List<List<Float>> embeddings = embeddingService.getEmbedding(textsToEmbed).stream()
                            .map(EmbeddingResult::getEmbedding)
                            .toList();
                    stopWatch.stop();
                    log.info(
                            "getEmbedding cost: {} ms",
                            stopWatch.getTotalTimeMillis()
                    );
                    List<Float> queryVector = embeddings.get(0);
                    List<Float> hydeVector = hydeContent != null ? embeddings.get(1) : null;

                    List<String> productTypes = ((List<?>) req.getParams().get(INPUT_PRODUCT_TYPES)).stream().map(Object::toString).toList(); // 吸油烟机、燃气灶..

                    CompletableFuture<SearchChunkResult> productVideoChunksFuture = searchVideoChunks(query, queryVector, hydeContent, hydeVector, videoConfig, null, videoRetrievalRule, RetrievalReqVo.RetrievalType.PRODUCT_CHUNK);

                    CompletableFuture<SearchChunkResult> chunksFuture = CompletableFuture.supplyAsync(
                            MdcContextWrapper.wrapSupplier(() -> {
                                try {
                                    List<String> productNames = (List<String>) req.getParams().get(INPUT_PRODUCT_NAMES); // all normalized product names extract from query
                                    List<String> suites = (List<String>) req.getParams().get(INPUT_PRODUCT_CATEGORYS); // 套系
                                    List<String> suiteModels = (List<String>) req.getParams().get(INPUT_CATEGORY_PRODUCTS_MODELS); // 套系型号
                                    if (!suites.isEmpty()) {
                                        Map<String, Product> productMap = dbCacheService.getDataMapByType(CacheType.PRODUCTS);
                                        productMap.values().stream()
                                                .filter(product -> product.getCategoryId() != null && product.getCategoryId().equals(SUITE_CATEGORY_ID))
                                                .filter(product -> suites.stream().anyMatch(suite -> product.getModelName().contains(suite.toLowerCase())))
                                                .forEach(product -> {
                                                    productNames.add(product.getModelName());
                                                    log.info(
                                                            "Added suite {} to product names",
                                                            product.getModelName()
                                                    );
                                                });
                                    }
                                    if (!suiteModels.isEmpty() && !productTypes.isEmpty()) { //套系型号来源于产品类别
                                        productNames.addAll(suiteModels);
                                        log.info(
                                                "Added suite models to product names: {}",
                                                suiteModels
                                        );
                                    }

                                    List<ProductCategory> categories = dbCacheService.getDataListByType(CacheType.CATEGORIES);
                                    List<AssistantAgentConfig.ProductTypeItem> productTypeItems = new ArrayList<>(categories.stream()
                                            .filter(category -> productTypes.contains(category.getName()))
                                            .map(category -> new AssistantAgentConfig.ProductTypeItem(category.getId(), category.getName()))
                                            .toList());
                                    if (!suites.isEmpty()) {
                                        productTypeItems.add(new AssistantAgentConfig.ProductTypeItem(SUITE_CATEGORY_ID, ""));
                                        log.info(
                                                "Added suite to product type items results: {}",
                                                productTypeItems
                                        );
                                    }
                                    if (config != null) {
                                        config.setProductTypeItems(productTypeItems);
                                    }
                                    RetrievalReqVo.RetrievalReqVoBuilder newBuilder = createRetrievalReqBaseBuilder(query, queryVector, config).retrievalType(RetrievalReqVo.RetrievalType.PRODUCT_CHUNK);
                                    if (productNames != null && !productNames.isEmpty()) {
                                        List<ProductKnowledgeBaseDTO> relatedProducts = dbCacheService.getRelatedProductsByAgentId(agent.getId().toString());

                                        Map<String, List<ProductKnowledgeBaseDTO>> modelNameMap = relatedProducts.stream()
                                                .collect(Collectors.groupingBy(
                                                        product -> product.getModelName().toLowerCase().trim()
                                                ));

                                        List<ProductKnowledgeBaseDTO> matchedProducts = productNames.stream()
                                                .map(name -> modelNameMap.getOrDefault(name.toLowerCase().trim(), Collections.emptyList()))
                                                .flatMap(List::stream)
                                                .collect(Collectors.toList());

                                        Map<Long, List<ProductKnowledgeBaseDTO>> groupedByKnowledgeBase = matchedProducts.stream()
                                                .collect(Collectors.groupingBy(ProductKnowledgeBaseDTO::getKnowledgeBaseId));

                                        List<AssistantAgentConfig.KnowledgeBaseConfig> kbConfigs = groupedByKnowledgeBase.entrySet().stream()
                                                .map(entry -> new AssistantAgentConfig.KnowledgeBaseConfig(
                                                        entry.getKey(),
                                                        0,
                                                        entry.getValue().stream()
                                                                .map(product -> new AssistantAgentConfig.ProductItem(product.getId(), product.getModelName()))
                                                                .collect(Collectors.toList()),
                                                        null
                                                ))
                                                .collect(Collectors.toList());
                                        if (!kbConfigs.isEmpty()) {
                                            newBuilder.productSpecificConfig(new AssistantAgentConfig(kbConfigs, null));
                                        }
                                    }
                                    return retrievalService.searchContentChunks(
                                            newBuilder.preRankTopK(chunkPreRankTopK)
                                                    .rerankTopK(chunkRerankTopK)
                                                    .threshold(chunkPreRankScoreThreshold)
                                                    .rerankThreshold(chunkRankScoreThreshold)
                                                    .HyDEContent(hydeContent)
                                                    .HyDEVector(hydeVector)
                                                    .maxChunkLength(maxChunkLength)
                                                    .build()
                                    );
                                } catch (RetrievalException e) {
                                    log.info(e.getMessage());
                                } catch (Exception e) {
                                    log.error("Error during SearchContentChunks", e);
                                }
                                return new SearchChunkResult(List.of(), Map.of());
                            })
                    );

                    CompletableFuture<List<QAGroup>> qaFuture = CompletableFuture.supplyAsync(
                            MdcContextWrapper.wrapSupplier(() -> {
                                try {
                                    RetrievalReqVo.RetrievalReqVoBuilder newBuilder = createRetrievalReqBaseBuilder(query, queryVector, config).retrievalType(RetrievalReqVo.RetrievalType.QA);
                                    return retrievalService.searchQaGroups(
                                            newBuilder.preRankTopK(qaPreRankTopK)
                                                    .rerankTopK(qaRerankTopK)
                                                    .threshold(qaPreRankScoreThreshold)
                                                    .rerankThreshold(qaRankScoreThreshold)
                                                    .build()
                                    );
                                } catch (RetrievalException e) {
                                    log.info(e.getMessage());
                                } catch (Exception e) {
                                    log.error("Error during searchQaGroups", e);
                                }
                                return List.of();
                            })
                    );

                    try {
//                        CompletableFuture<Object> anyResult = CompletableFuture.anyOf(
//                                qaFuture.thenApply(result -> result),
//                                chunksFuture.thenApply(result -> result)
//                        );

//                        Object result = anyResult.get(5, TimeUnit.SECONDS); // 1秒超时
                        // faq first
//                        if (result instanceof List && !((List<?>) result).isEmpty()) {
//                            output.put(OUTPUT_QA_ANSWER, result);
//                            return new ComponentResp(true, "", output);
//                        } else {
                        // wait all result
                        List<String> otherIntents = ((List<String>) req.getParams().getOrDefault(INPUT_OTHER_INTENTS, List.of()));

                        String afterSalesContent = buildAfterSalesContent(otherIntents, productTypes, configKey);

                        CompletableFuture.allOf(chunksFuture, qaFuture,productVideoChunksFuture).get(8, TimeUnit.SECONDS);
                        List<QAGroup> qaGroups = qaFuture.getNow(List.of());
                        SearchChunkResult searchChunkResult = chunksFuture.getNow(new SearchChunkResult(List.of(), Map.of()));
                        SearchChunkResult searchVideoChunkResult = productVideoChunksFuture.getNow(new SearchChunkResult(List.of(), Map.of()));
                        List<String> qaAnswers = qaGroups.stream()
                                .filter(x -> x.getScore() >= directAnswerThreshold)
                                .max(Comparator.comparing(QAGroup::getScore))
                                .map(QAGroup::getAnswers)
                                .orElse(List.of());
                        String qaAnswer = switch (qaAnswers.size()) {
                            case 0 -> "";
                            case 1 -> qaAnswers.get(0);
                            default -> IntStream.range(0, qaAnswers.size())
                                    .mapToObj(i -> String.format("%s", qaAnswers.get(i)))
                                    .collect(Collectors.joining("\n\n\n\n"));
                        };
                        output.put(OUTPUT_QA_ANSWER, qaAnswer);
                        output.put(OUTPUT_QA_GROUPS, qaGroups);
                        output.put(OUTPUT_FILE_CHUNKS, searchChunkResult.getFileChunks());
                        output.put(OUTPUT_PLACEHOLDER_MAP, searchChunkResult.getPlaceholderMap());
                        output.put(OUTPUT_VIDEO_FILE_CHUNKS, searchVideoChunkResult.getFileChunks());
                        output.put(OUTPUT_AFTER_SALES_CONTENT, afterSalesContent);

                        return (qaAnswer == "" && (searchChunkResult == null || searchChunkResult.getFileChunks().isEmpty()))
                                ? new ComponentResp(true, "No results found", output)
                                : new ComponentResp(true, "", output);
//                        }
                    } catch (Exception e) {
                        log.error("Error during parallel RetrievalComponent execution", e);
                        return new ComponentResp(true, "Error during retrieval", output);
                    }
                })
                .orElse(new ComponentResp(false, "Agent not found or deleted", output));
    }

    private String buildAfterSalesContent(List<String> otherIntents, List<String> productTypes, CacheType configKey) {
        if (!otherIntents.contains("after_sales_service")) {
            return "";
        }

        ComponentConfig afterSalesConfig = (ComponentConfig) dbCacheService.getDataMapByType(configKey).get(AFTER_SALES_CONFIG);
        if (afterSalesConfig == null || !afterSalesConfig.getJsonValue().has(PRODUCT_WARRANTY_RULE)) {
            return "";
        }

        JsonNode config = afterSalesConfig.getJsonValue();
        Map<String, String> warrantyRules = JsonUtils.readValue(config.get(PRODUCT_WARRANTY_RULE), Map.class);
        Map<String, String> keyParts = JsonUtils.readValue(config.get(PRODUCT_KEY_PARTS), Map.class);

        if (!productTypes.isEmpty()) {
            Predicate<String> containsProductType = key ->
                    productTypes.stream().anyMatch(key::contains);
            warrantyRules = warrantyRules.entrySet().stream()
                    .filter(e -> containsProductType.test(e.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            keyParts = keyParts.entrySet().stream()
                    .filter(e -> containsProductType.test(e.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        if (warrantyRules.isEmpty()) {
            return "";
        }

        String content = String.format("产品保修信息：\n%s\n主要部件清单：\n%s",
                warrantyRules.entrySet().stream()
                        .map(e -> e.getKey() + ": " + e.getValue())
                        .collect(Collectors.joining("\n")),
                keyParts.entrySet().stream()
                        .map(e -> e.getKey() + ": " + e.getValue())
                        .collect(Collectors.joining("\n"))
        );

        String commonRule = config.get(WARRANTY_RULE_COMMON) != null ? config.get(WARRANTY_RULE_COMMON).asText() : "";
        String warrantyCase = config.get(WARRANTY_CASE) != null ? config.get(WARRANTY_CASE).asText() : "";

        if (commonRule != null && !commonRule.isEmpty()) {
            content += "\n保修期计算规则:\n" + commonRule;
        }
        if (warrantyCase != null && !warrantyCase.isEmpty()) {
            content += "\n特殊情况举例：\n" + warrantyCase;
        }

        return content;
    }

    private RetrievalReqVo.RetrievalReqVoBuilder createRetrievalReqBaseBuilder(String query, List<Float> queryVector, AssistantAgentConfig config) {
        return RetrievalReqVo.builder()
                .query(query)
                .knowledgeConfigs(config)
                .isHybrid(isHybridEnabled)
                .vector(queryVector);
    }

    private CompletableFuture<SearchChunkResult> searchVideoChunks(String query, List<Float> queryVector, String hydeContent, List<Float> hydeVector, AssistantAgentConfig config, AssistantAgentConfig productSpecificConfig, CookeryRetrievalConfig.RetrievalRule retrievalRule, RetrievalReqVo.RetrievalType chunkType) {
        return CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            try {
                RetrievalReqVo.RetrievalReqVoBuilder builder = createRetrievalReqBaseBuilder(query, queryVector, config)
                        .retrievalType(chunkType)
                        .preRankTopK(retrievalRule.getPreRerankTopK())
                        .rerankTopK(retrievalRule.getRerankTopK())
                        .threshold(retrievalRule.getScoreThreshold())
                        .rerankThreshold(retrievalRule.getRerankThreshold())
                        .HyDEContent(hydeContent)
                        .HyDEVector(hydeVector)
                        .maxChunkLength(retrievalRule.getMaxChunkLength());
                if (productSpecificConfig != null) {
                    builder.productSpecificConfig(productSpecificConfig);
                }
                SearchChunkResult searchChunkResult = retrievalService.searchContentChunks(builder.build());
                log.info("Retrieval video result: {}",searchChunkResult.getFileChunks());
                return searchChunkResult;
            } catch (RetrievalException e) {
                log.info(e.getMessage());
            } catch (Exception e) {
                log.error("Error during SearchContentChunks", e);
            } finally {
                stopWatch.stop();
                log.info("cookeryRetrieval searchContentChunks for {} type cost: {} ms", chunkType, stopWatch.getTotalTimeMillis());
            }
            return new SearchChunkResult(List.of(), Map.of());
        }), ThreadPoolExecutorConfiguration.COOKERY_EXECUTOR);
    }
}

```

