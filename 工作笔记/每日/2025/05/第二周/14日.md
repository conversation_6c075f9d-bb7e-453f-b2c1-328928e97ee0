```java
package com.robam.roki.ai.agent.manager.cookerygod.components.cookeryPostProcess;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.robam.ai.aio.common.utils.JsonUtils;
import com.robam.roki.ai.agent.manager.ckb.enums.CacheType;
import com.robam.roki.ai.agent.manager.ckb.mapper.AssistantAgentMapper;
import com.robam.roki.ai.agent.manager.ckb.model.AssistantAgentConfig;
import com.robam.roki.ai.agent.manager.ckb.model.ComponentConfig;
import com.robam.roki.ai.agent.manager.ckb.model.KnowledgeBase;
import com.robam.roki.ai.agent.manager.ckb.opensearch.model.vo.RetrievalReqVo;
import com.robam.roki.ai.agent.manager.ckb.retrieval.model.FileChunk;
import com.robam.roki.ai.agent.manager.ckb.retrieval.model.SearchChunkResult;
import com.robam.roki.ai.agent.manager.ckb.retrieval.service.RetrievalService;
import com.robam.roki.ai.agent.manager.ckb.service.DBCacheService;
import com.robam.roki.ai.agent.manager.conf.ThreadPoolExecutorConfiguration;
import com.robam.roki.ai.agent.manager.cookerygod.components.cookbookRecommend.model.CookbookRecommendConfig;
import com.robam.roki.ai.agent.manager.cookerygod.enums.CookBookTypeEnum;
import com.robam.roki.ai.agent.manager.cookerygod.enums.ImageType;
import com.robam.roki.ai.agent.manager.cookerygod.model.CookBookDetail;
import com.robam.roki.ai.agent.manager.cookerygod.model.CookeryRetrievalConfig;
import com.robam.roki.ai.agent.manager.cookerygod.model.DetectIngredients;
import com.robam.roki.ai.agent.manager.cookerygod.service.CookeryConfigService;
import com.robam.roki.ai.agent.manager.cookerygod.service.FoodIngredientInfoService;
import com.robam.roki.ai.agent.manager.embedding.model.EmbeddingResult;
import com.robam.roki.ai.agent.manager.embedding.service.EmbeddingService;
import com.robam.roki.ai.agent.manager.remote.model.response.cookbook.CookbookSearchResp;
import com.robam.roki.ai.agent.manager.remote.service.CookbookService;
import com.robam.roki.ai.agent.manager.shopguide.exceptions.RetrievalException;
import com.robam.roki.ai.agent.manager.workflow.base.BaseMethodNodeComponent;
import com.robam.roki.ai.agent.manager.workflow.constant.ComponentName;
import com.robam.roki.ai.agent.manager.workflow.context.MdcContextWrapper;
import com.robam.roki.ai.agent.manager.workflow.enums.SystemVariableEnum;
import com.robam.roki.ai.agent.manager.workflow.model.ComponentReq;
import com.robam.roki.ai.agent.manager.workflow.model.ComponentResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.robam.roki.ai.agent.manager.constant.AnswerTagNameConstants.*;
import static com.robam.roki.ai.agent.manager.cookerygod.components.constant.ConfigKey.*;
import static com.robam.roki.ai.agent.manager.cookerygod.components.cookbookRecommend.CookbookRecommendComponent.COOKBOOK_RECOMMEND_CONFIG_KEY;

@Slf4j
@Component(ComponentName.COOKERY_POST_PROCESS)
public class CookeryPostProcessComponent implements BaseMethodNodeComponent {
    // 定义输入参数名
    private final String INPUT_COOKERY_ANSWER = "cookeryAnswer";
    private final String INPUT_RETRIEVED_COOKBOOKS = "cookbooks";
    private final String INPUT_LLM_RECOMMENDED_COOKBOOKS = "recommendedCookbooks";
    private final String INPUT_COOKERY_CHUNKS = "cookeryFileChunks";
    private final String INPUT_OTHER_CHUNKS = "otherFileChunks";

    private final String INPUT_INTENT = "intent";
    private final String INPUT_FOODSTUFFS_CHUNKS = "foodstuffsFileChunks";
    private final String INPUT_PRODUCT_CHUNKS = "productFileChunks";
    private final String INPUT_PRODUCT_NAMES = "productNames";
    private final String INPUT_PRODUCT_TYPES = "productTypes";
    private final String INPUT_COOKBOOK_CARDS = "cookbookCards";
    private final String INPUT_COOKBOOK_INGREDIENT_THEMES = "cookbookIngredientThemes";
    private final String INPUT_REWRITTEN_QUERY = "rewrittenQuery";
    private final String INPUT_IMAGE_ANALYSIS = "imageAnalysis";
    private final String INPUT_OBESITY_NUTRITION_FILE_CHUNKS = "obesityNutritionFileChunks";
    // 定义输出参数名
    private final String OUTPUT_RECOMMENDED_COOKBOOKS = "postProcessedCookbooks";
    private final String OUTPUT_CITATION_FILES = "citationFiles";

    // 组件名
    private final String NAME = ComponentName.COOKERY_GOD_ROUTER;

    // 组件功能
    private final String DESCRIPTION = "Cookery God router";

    // 组件输入参数，会被用于输入参数验证
    private final Map<String, Class<?>> INPUT_PARAMS = Map.ofEntries(
            Map.entry(INPUT_COOKERY_ANSWER, String.class),
            Map.entry(INPUT_RETRIEVED_COOKBOOKS, List.class),
            Map.entry(INPUT_LLM_RECOMMENDED_COOKBOOKS, List.class),
            Map.entry(INPUT_COOKERY_CHUNKS, List.class),
            Map.entry(INPUT_OTHER_CHUNKS, List.class),
            Map.entry(INPUT_FOODSTUFFS_CHUNKS, List.class),
            Map.entry(INPUT_PRODUCT_CHUNKS, List.class),
            Map.entry(INPUT_COOKBOOK_CARDS, List.class),
            Map.entry(INPUT_COOKBOOK_INGREDIENT_THEMES, List.class),
            Map.entry(INPUT_REWRITTEN_QUERY, String.class),
            Map.entry(INPUT_IMAGE_ANALYSIS, List.class),
            Map.entry(INPUT_PRODUCT_NAMES, List.class),
            Map.entry(INPUT_PRODUCT_TYPES, List.class),
            Map.entry(INPUT_OBESITY_NUTRITION_FILE_CHUNKS, List.class),
            Map.entry(INPUT_INTENT, String.class)
    );

    // 组件输出参数，会被用于输出参数验证
    private final Map<String, Class<?>> OUTPUT_PARAMS = Map.of(
            OUTPUT_RECOMMENDED_COOKBOOKS, List.class,
            OUTPUT_CITATION_FILES, List.class
    );
    @Autowired
    private FoodIngredientInfoService foodIngredientInfoService;
    @Autowired
    private DBCacheService cacheService;
    @Autowired
    private CookeryConfigService cookeryConfigService;
    @Autowired
    private RetrievalService retrievalService;
    @Autowired
    private CookbookService cookbookService;
    @Autowired
    private AssistantAgentMapper assistantAgentMapper;
    @Autowired
    private EmbeddingService embeddingService;

    @Override
    public ComponentResp execute(ComponentReq req) {
        try {
            // 并行处理cookbook和citation
//            CompletableFuture<List<CookBookDetail.CookbookCard>> cookbooksFuture = CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() ->
//                    processCookbooks(req)));
//            CompletableFuture<List<FileChunk>> citationsFuture = CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() ->
//                    citationFilesFilter(req)));
//            CompletableFuture<List<FileChunk>> videosFuture = CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() ->
//                    searchVideos(req)));
            List<FileChunk> videos = searchVideos(req);

            // 等待所有任务完成
//            CompletableFuture.allOf(videosFuture).get(4, TimeUnit.SECONDS);
//            List<CookBookDetail.CookbookCard> cookbookCards = cookbooksFuture.getNow(List.of());
//            List<FileChunk> citations = citationsFuture.getNow(List.of());
//            List<FileChunk> videos = videosFuture.get();
//            citations.addAll(videos);

            return new ComponentResp(true, "", Map.of(
                    OUTPUT_RECOMMENDED_COOKBOOKS, List.of(),
                    OUTPUT_CITATION_FILES, videos
            ));
        } catch (Exception e) {
            log.error("Error in CookeryPostProcessComponent", e);
            return new ComponentResp(true, "Error occurred: " + e.getMessage(), Map.of(
                    OUTPUT_RECOMMENDED_COOKBOOKS,
                    req.getParams().get(INPUT_LLM_RECOMMENDED_COOKBOOKS) != null
                            ? req.getParams().get(INPUT_LLM_RECOMMENDED_COOKBOOKS)
                            : List.of(),
                    OUTPUT_CITATION_FILES, List.of()
            ));
        }
    }

    private List<CookBookDetail.CookbookCard> processCookbooks(ComponentReq req) {
        long startTime = System.currentTimeMillis();
        // 获取并检查输入参数
        String cookeryAnswer = req.getParams().get(INPUT_COOKERY_ANSWER) != null
                ? req.getParams().get(INPUT_COOKERY_ANSWER).toString()
                : "";
        List<CookBookDetail.CookbookCard> cookbookCards = (List<CookBookDetail.CookbookCard>) req.getParams().get(INPUT_COOKBOOK_CARDS);
        List<CookBookDetail> retrievedCookbooks = (List<CookBookDetail>) req.getParams().get(INPUT_RETRIEVED_COOKBOOKS);
        List<CookBookDetail.CookbookCard> recommendedCookbooks = (List<CookBookDetail.CookbookCard>) req.getParams().get(INPUT_LLM_RECOMMENDED_COOKBOOKS);
        List<String> ingredientThemes = (List<String>) req.getParams().get(INPUT_COOKBOOK_INGREDIENT_THEMES);
        List<Map<String, String>> imageAnalysis = (List<Map<String, String>>) req.getParams().get(INPUT_IMAGE_ANALYSIS);
        List<String> imageIngredients = Optional.ofNullable(imageAnalysis).orElse(List.of()).stream()
                .filter(info -> ImageType.INGREDIENT.name().equals(info.get("type")))
                .map(info -> info.get("details"))
                .filter(StringUtils::isNotBlank)
                .map(s -> {
                    // 移除首尾的方括号
                    String cleaned = s.replaceAll("^\\[|\\]$", "");
                    // 按逗号分割（如果有多个值的情况）
                    return Arrays.asList(cleaned.split(","));
                })
                .flatMap(List::stream)
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        List<CookBookDetail.CookbookCard> mergedCookbooks = new ArrayList<>();
        String query = req.getParams().get(INPUT_REWRITTEN_QUERY) != null
                ? req.getParams().get(INPUT_REWRITTEN_QUERY).toString()
                : req.getSystemParams().get(SystemVariableEnum.QUERY).toString();

        boolean onlyFoodIngredient = ingredientThemes != null &&
                !ingredientThemes.isEmpty() &&
                ingredientThemes.stream().allMatch(theme -> theme.contains("食材"));

        Set<String> extractedDishNames = extractDishNames(cookeryAnswer);
        Set<String> targetIngredients = extractIngredients(cookeryAnswer);

        // 如果retrievedCookbooks为空，extractedDishNames为空，直接返回recommendedCookbooks
        if ((extractedDishNames == null || extractedDishNames.isEmpty()) && (retrievedCookbooks == null || retrievedCookbooks.isEmpty()) && imageIngredients.isEmpty() && (targetIngredients.isEmpty())) {
            return recommendedCookbooks != null ? recommendedCookbooks : List.of();
        }

        // 获取配置
        CookbookRecommendConfig recommendConfig = null;
        try {
            Map<String, ComponentConfig> cookeryGodConfigMap = cacheService.getDataMapByType(CacheType.COOKERY_GOD_CONFIG);
            ComponentConfig recommendConfigNode = cookeryGodConfigMap.get(COOKBOOK_RECOMMEND_CONFIG_KEY);
            if (recommendConfigNode != null && recommendConfigNode.getJsonValue() != null) {
                recommendConfig = JsonUtils.readValue(recommendConfigNode.getJsonValue(), CookbookRecommendConfig.class);
            }
        } catch (Exception e) {
            log.warn("Failed to get recommend config", e);
        }

        // 过滤和排序matchedCookbooks
        List<CookBookDetail> matchedCookbooks = retrievedCookbooks.stream()
                .filter(cookbook -> cookbook != null
                        && cookbook.getName() != null
                        && cookbook.getCookbookCard() != null
                        && cookeryAnswer.contains(cookbook.getName()))
                .sorted(Comparator.comparingInt(cookbook -> cookeryAnswer.indexOf(cookbook.getName())))
                .toList();

        log.info("processCookbooks matched cookbooks from answer: {}",
                matchedCookbooks.stream().map(CookBookDetail::getName).collect(Collectors.toList()));

        Set<Integer> seenIds = new HashSet<>();
        Set<String> matchedDishNames = new HashSet<>();

        // 添加matchedCookbooks,优先answer中精确出现
        for (CookBookDetail cookbook : matchedCookbooks) {
            if (cookbook.getCookbookCard() != null
                    && cookbook.getCookbookCard().getId() != null
                    && seenIds.add(cookbook.getCookbookCard().getId())) {
                mergedCookbooks.add(cookbook.getCookbookCard());
                matchedDishNames.add(cookbook.getName());
            }
        }
        // 取query中出现的菜
        if (retrievedCookbooks != null) {
            for (CookBookDetail cookbook : retrievedCookbooks) {
                if (cookbook != null
                        && cookbook.getId() != null
                        && query.contains(cookbook.getName())
                        && seenIds.add(cookbook.getId())
                ) {
                    mergedCookbooks.add(cookbook.getCookbookCard());
                }
            }
        }
        // 查询未匹配的菜名对应的菜谱
        Set<String> unmatchedDishNames = new HashSet<>(extractedDishNames);
        unmatchedDishNames.removeAll(matchedDishNames);
        List<CookBookDetail.CookbookCard> answerCookbooks = CollUtil.newArrayList();
        log.info("processCookbooks answer string matched cookbooks:{}, rest cookbooks: {}", matchedDishNames, unmatchedDishNames);
        if (!unmatchedDishNames.isEmpty()) {
            // 再加上answer中出现的菜名，去查一次菜谱，名字一样的排前面，没有名字一样的，就每组取top1，然后把各组结果合并后按分数排序
            List<CookBookDetail.CookbookCard> additionalCookbooks = queryAdditionalCookbooks(unmatchedDishNames, recommendConfig, onlyFoodIngredient);
            for (CookBookDetail.CookbookCard cookbook : additionalCookbooks) {
                if (cookbook != null && cookbook.getId() != null && seenIds.add(cookbook.getId())) {
                    mergedCookbooks.add(cookbook);
                    answerCookbooks.add(cookbook);
                }
            }
            log.info("processCookbooks additional cookbooks added: {}", additionalCookbooks.stream().map(CookBookDetail.CookbookCard::getName).collect(Collectors.toList()));
        }

        // 当非食材主题时，加上大模型推荐的菜
        if (recommendedCookbooks != null && !onlyFoodIngredient) {
            for (CookBookDetail.CookbookCard cookbook : recommendedCookbooks) {
                if (cookbook != null
                        && cookbook.getId() != null
                        && seenIds.add(cookbook.getId())) {
                    mergedCookbooks.add(cookbook);
                }
            }
        }


        // 限制推荐数量
        if (recommendConfig != null && recommendConfig.getTotalCookbookRecommendNum() != null) {
            int maxRecommendedCookbooks = recommendConfig.getTotalCookbookRecommendNum();
            if (maxRecommendedCookbooks > 0 && mergedCookbooks.size() > maxRecommendedCookbooks) {
                mergedCookbooks = mergedCookbooks.subList(0, maxRecommendedCookbooks);
            }
        }
        Set<String> existingIngredients = new HashSet<>();
        if (cookbookCards != null) { // 加上相关食材卡片
            for (CookBookDetail.CookbookCard cookbookCard : cookbookCards) {
                if (cookbookCard != null
                        && cookbookCard.getId() != null
                        && cookbookCard.getType().equals(CookBookTypeEnum.FOOD_IDENTIFICATION.getName())) {
                    existingIngredients.add(cookbookCard.getName());
                    mergedCookbooks.add(cookbookCard);
                }
            }
        }
        if (onlyFoodIngredient || !imageIngredients.isEmpty()) { // 主题只有食材，则从答案中提取食材，优先答案提取的食材，再merge上cookbookCards中的食材，要去重，只推食材卡片
            // 从答案中提取所有菜名
//            Set<String> targetIngredients = extractIngredients(cookeryAnswer);
            log.info("processCookbooks food ingredients only. Extracted ingredients from answer: {}", targetIngredients);
            if (!imageIngredients.isEmpty()) {
                targetIngredients.addAll(imageIngredients);
                log.info("processCookbooks food ingredients only. Extracted ingredients from image: {}", imageIngredients);
            }
            // 移除已存在的食材
            targetIngredients.removeAll(existingIngredients);

            // 查询新的食材并合并结果
            if (!targetIngredients.isEmpty()) {
                try {
                    List<CookBookDetail.CookbookCard> newIngredientCards = searchFoodIngredients(new ArrayList<>(targetIngredients))
                            .get(2, TimeUnit.SECONDS);
                    mergedCookbooks.addAll(newIngredientCards.stream().filter(ingredientCard -> !existingIngredients.contains(ingredientCard.getName())).toList());
                } catch (Exception e) {
                    log.error("Error searching for food ingredients", e);
                }
            }
        }
        if (!mergedCookbooks.isEmpty() && mergedCookbooks.size() < 3 && !answerCookbooks.isEmpty()) {
            try {
                int needMore = 3 - mergedCookbooks.size();
                Set<Integer> cookbookIds = mergedCookbooks.stream()
                        .filter(Objects::nonNull)
                        .map(cookbook -> {
                            if (cookbook.getId() == null) {
                                log.warn("Found cookbook with null id in mergedCookbooks");
                                return -1;
                            }
                            return cookbook.getId();
                        })
                        .collect(Collectors.toSet());
                List<CookBookDetail.CookbookCard> supplementCookbooks = answerCookbooks.stream()
                        .filter(cookbook -> cookbook != null
                                && cookbook.getId() != null
                                && !cookbookIds.contains(cookbook.getId()))
                        .limit(needMore)
                        .toList();
                if (!supplementCookbooks.isEmpty()) {
                    mergedCookbooks.addAll(supplementCookbooks);
                    log.info("Supplemented {} cookbooks from answer cookbooks", supplementCookbooks.size());
                }
            } catch (Exception e) {
                log.error("Error while supplementing cookbooks", e);
            }
        }

        log.info("CookeryPostProcessComponent: processCookbooks took {} ms", System.currentTimeMillis() - startTime);
        return mergedCookbooks;
    }

    private CompletableFuture<List<CookBookDetail.CookbookCard>> searchFoodIngredients(List<String> query) {
        return CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            try {
                if (CollUtil.isEmpty(query)) {
                    return List.of();
                }
                List<DetectIngredients.DetectIngredientsResultResp> allFoodIngredientInfoByName = foodIngredientInfoService.findAllFoodIngredientInfoByName(query);
                if (CollUtil.isEmpty(allFoodIngredientInfoByName)) {
                    return List.of();
                }
                ArrayList<CookBookDetail.CookbookCard> result = CollUtil.newArrayList();
                HashSet<String> nameSet = CollUtil.newHashSet();
                allFoodIngredientInfoByName.forEach(detectIngredientsResultResp -> {
                    if (nameSet.contains(detectIngredientsResultResp.getName())) {
                        return;
                    }
                    nameSet.add(detectIngredientsResultResp.getName());
                    CookBookDetail.CookbookCard cookbookCard = new CookBookDetail.CookbookCard();
                    cookbookCard.setId(Convert.toInt(detectIngredientsResultResp.getId()));
                    cookbookCard.setName(detectIngredientsResultResp.getName());
                    cookbookCard.setImgCover11(detectIngredientsResultResp.getCoverUrl());
                    cookbookCard.setType(CookBookTypeEnum.FOOD_IDENTIFICATION.getName());
                    cookbookCard.setIntroduction(detectIngredientsResultResp.getDescription());
                    result.add(cookbookCard);
                });
                return result;
            } catch (RetrievalException e) {
                log.info(e.getMessage());
            } catch (Exception e) {
                log.error("Error during searchFoodIngredients", e);
            } finally {
                stopWatch.stop();
                log.info("cookeryRetrieval searchFoodIngredients execution time: {} ms", stopWatch.getTotalTimeMillis());
            }
            return List.of();
        }), ThreadPoolExecutorConfiguration.COOKERY_EXECUTOR);
    }

    private Set<String> extractDishNames(String answer) {
        Set<String> dishNames = new HashSet<>();
        String pattern = DISH_START_TAG + "(.+?)" + DISH_END_TAG;
        java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = r.matcher(answer);

        while (m.find()) {
            dishNames.add(m.group(1).trim());
        }
        return dishNames;
    }

    // 添加提取食材的方法
    private Set<String> extractIngredients(String answer) {
        Set<String> ingredients = new HashSet<>();
        String pattern = FOOD_START_TAG + "(.+?)" + FOOD_END_TAG;
        java.util.regex.Pattern r = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = r.matcher(answer);

        while (m.find()) {
            String[] foods = m.group(1).split("[、，]");
            for (String food : foods) {
                ingredients.add(food.trim());
            }
        }
        return ingredients;
    }

    private List<CookBookDetail.CookbookCard> queryAdditionalCookbooks(Set<String> dishNames, CookbookRecommendConfig config, boolean onlyExactMatch) {
        long startTime = System.currentTimeMillis();
        final double cookbookNameThreshold = config != null && config.getCookbookNameSimilarityThreshold() != null
                ? config.getCookbookNameSimilarityThreshold()
                : 3d;
        List<CompletableFuture<Map.Entry<String, List<CookbookSearchResp>>>> recommendedFutures = new ArrayList<>();
        if (CollUtil.isNotEmpty(dishNames)) {
            for (String cookbookName : dishNames) {
                CompletableFuture<Map.Entry<String, List<CookbookSearchResp>>> future = CompletableFuture.supplyAsync(MdcContextWrapper.wrapSupplier(() -> {
                    RetrievalReqVo.RetrievalReqVoBuilder builder = RetrievalReqVo.builder()
                            .query(cookbookName)
                            .cookSearchTopK(3)
                            .cookSearchScore(cookbookNameThreshold);

                    List<CookbookSearchResp> results = retrievalService.searchCookBook(builder.build());
                    return Map.entry(cookbookName, results);
                }), ThreadPoolExecutorConfiguration.COOKERY_EXECUTOR);
                recommendedFutures.add(future);
            }
        }

        List<CookBookDetail.CookbookCard> result = new ArrayList<>();
        try {
            CompletableFuture.allOf(recommendedFutures.toArray(new CompletableFuture[0])).get(2, TimeUnit.SECONDS);

            // 使用LinkedHashMap保持顺序
            Map<Integer, String> exactMatchMap = new LinkedHashMap<>();
            List<Map.Entry<Integer, Double>> topScoreList = new ArrayList<>();

            // 处理每个查询结果
            for (CompletableFuture<Map.Entry<String, List<CookbookSearchResp>>> future : recommendedFutures) {
                Map.Entry<String, List<CookbookSearchResp>> entry = future.get();
                String queryName = entry.getKey();
                List<CookbookSearchResp> searchResults = entry.getValue();

                if (CollUtil.isEmpty(searchResults)) {
                    continue;
                }

                // 找到完全匹配的结果
                Optional<CookbookSearchResp> exactMatch = searchResults.stream()
                        .filter(resp -> resp.getName() != null &&
                                resp.getName().trim().equals(queryName.trim()))
                        .findFirst();

                if (exactMatch.isPresent()) {
                    // 完全匹配的结果
                    exactMatchMap.put(exactMatch.get().getCookbookId(), exactMatch.get().getName());
                } else {
                    // 没有完全匹配，取top1并记录分数
                    CookbookSearchResp top1 = searchResults.get(0);
                    topScoreList.add(Map.entry(top1.getCookbookId(), top1.getScore()));
                }
            }

            // 对非完全匹配的结果按分数排序
            topScoreList.sort((a, b) -> Double.compare(b.getValue(), a.getValue()));

            // 合并ID列表，保持顺序（完全匹配的在前，其他按分数排序）
            List<Integer> orderedIds = new ArrayList<>();
            orderedIds.addAll(exactMatchMap.keySet());
            if (!onlyExactMatch) {
                orderedIds.addAll(topScoreList.stream()
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList()));
            }

            // 批量查询菜谱详情
            if (!orderedIds.isEmpty()) {
                List<CookBookDetail> cookBookDetails = cookbookService.cookbookDetail(orderedIds);

                // 创建ID到Detail的映射
                Map<Integer, CookBookDetail> idToDetailMap = cookBookDetails.stream()
                        .collect(Collectors.toMap(CookBookDetail::getId, detail -> detail, (existing, replacement) -> existing));

                // 按原有顺序重建结果列表
                for (Integer id : orderedIds) {
                    CookBookDetail detail = idToDetailMap.get(id);
                    if (detail != null && detail.getCookbookCard() != null) {
                        result.add(detail.getCookbookCard());
                    }
                }
            }
            log.info("Additional cookbook query complete. Exact matches: {}, Score-based matches: {}. Query took {} ms",
                    exactMatchMap.size(), topScoreList.size(), System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("Error while querying additional cookbooks", e);
        }

        return result;
    }

    private List<FileChunk> citationFilesFilter(ComponentReq req) {
        long startTime = System.currentTimeMillis();
        Map<String, KnowledgeBase> knowledgeBaseMap = cacheService.getDataMapByType(CacheType.KNOWLEDGE_BASE);
        List<String> productNames = (List<String>) req.getParams().get(INPUT_PRODUCT_NAMES);
        List<String> productTypes = (List<String>) req.getParams().get(INPUT_PRODUCT_TYPES);
        boolean hasProduct = !productNames.isEmpty() || !productTypes.isEmpty();

        CookeryRetrievalConfig cookeryRetrievalConfig = cookeryConfigService.getCookeryRetrievalConfig();
        Set<Long> productCategoryIds = new HashSet<>();
        if (cookeryRetrievalConfig != null && cookeryRetrievalConfig.getCategoryConfig() != null) {
            productCategoryIds.addAll(cookeryRetrievalConfig.getCategoryConfig().getProductCategories());
        }

        // 合并所有chunks并过d滤
        return Stream.of(
                        (List<FileChunk>) req.getParams().get(INPUT_PRODUCT_CHUNKS),
                        (List<FileChunk>) req.getParams().get(INPUT_FOODSTUFFS_CHUNKS),
                        (List<FileChunk>) req.getParams().get(INPUT_COOKERY_CHUNKS),
                        (List<FileChunk>) req.getParams().get(INPUT_OTHER_CHUNKS),
                        (List<FileChunk>) req.getParams().get(INPUT_OBESITY_NUTRITION_FILE_CHUNKS)
                )
                .flatMap(Collection::stream)
                .filter(chunk -> {
                    KnowledgeBase kb = knowledgeBaseMap.get(chunk.getKnowledgeBaseId().toString());
                    if (productCategoryIds.contains(kb.getCategoryId()) && !hasProduct) { // 未抽取出产品相关实体时不展示相关引用
                        return false;
                    }
                    return kb != null && kb.getSourceVisibility() == 1;
                })
                .peek(__ -> log.info("CookeryPostProcessComponent: citationFilesFilter took {} ms",
                        System.currentTimeMillis() - startTime))
                .collect(Collectors.toList());  // Simply collect to list
    }

    private List<FileChunk> searchVideos(ComponentReq req) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            log.info("start searchVideos");
            if (req == null || req.getParams() == null || req.getSystemParams() == null) {
                log.warn("Invalid request parameters for video search");
                return List.of();
            }

            String query = req.getSystemParams().get(SystemVariableEnum.QUERY)+"";

            if (StringUtils.isBlank(query)) {
                log.warn("Empty query for video search");
                return List.of();
            }

            return (List<FileChunk>) Optional.ofNullable(req.getSystemParams().get(SystemVariableEnum.AGENT_ID))
                    .map(Object::toString)
                    .map(Long::parseLong)
                    .map(assistantAgentMapper::selectById)
                    .filter(agent -> agent != null && agent.getIsDeleted() != 1)
                    .map(agent -> {
                        String intent = Optional.ofNullable(req.getParams().get(INPUT_INTENT))
                                .map(Object::toString)
                                .orElse("");

                        if (!INTENT_COOKERY_PRODUCT.equals(intent) && !INTENT_PRODUCT_ONLY.equals(intent)) {
                            log.info("Skipping video search for intent: {}", intent);
                            return List.of();
                        }

                        try {
                            AssistantAgentConfig config = JsonUtils.readValue(agent.getConfig(), AssistantAgentConfig.class);
                            if (config == null || config.getKnowledgeBases() == null) {
                                log.warn("Invalid agent config for video search");
                                return List.of();
                            }

                            List<AssistantAgentConfig.KnowledgeBaseConfig> videoKnConfig = new ArrayList<>();
                            List<Long> videoKnowledgeBaseIds = new ArrayList<>();

                            CookeryRetrievalConfig cookeryRetrievalConfig = cookeryConfigService.getCookeryRetrievalConfig();
                            if (cookeryRetrievalConfig == null || cookeryRetrievalConfig.getCategoryConfig() == null) {
                                log.warn("Missing cookery retrieval config for video search");
                                return List.of();
                            }

                            videoKnowledgeBaseIds = cookeryRetrievalConfig.getCategoryConfig().getVideoCategories();
                            if (CollUtil.isEmpty(videoKnowledgeBaseIds)) {
                                log.warn("No video categories configured");
                                return List.of();
                            }

                            // 过滤出视频知识库配置
                            for (AssistantAgentConfig.KnowledgeBaseConfig knowledgeBase : config.getKnowledgeBases()) {
                                if (knowledgeBase != null && knowledgeBase.getId() != null 
                                    && videoKnowledgeBaseIds.contains(knowledgeBase.getId())) {
                                    videoKnConfig.add(knowledgeBase);
                                }
                            }

                            if (CollUtil.isEmpty(videoKnConfig)) {
                                log.warn("No matching video knowledge bases found");
                                return List.of();
                            }

                            AssistantAgentConfig videoConfig = new AssistantAgentConfig();
                            videoConfig.setKnowledgeBases(videoKnConfig);

                            Map<String, CookeryRetrievalConfig.RetrievalRule> ruleMap = 
                                MapUtil.emptyIfNull(cookeryRetrievalConfig.getRuleMap().get(intent));
                            CookeryRetrievalConfig.RetrievalRule retrievalRule = ruleMap.get(RETRIEVAL_RULE_KEY_VIDEO);
                            
                            if (retrievalRule == null) {
                                log.warn("No video retrieval rule found for intent: {}", intent);
                                return List.of();
                            }

                            List<EmbeddingResult> embedding = embeddingService.getEmbedding(List.of(query));
                            if (CollUtil.isEmpty(embedding)) {
                                log.warn("No embedding found for query: {}", query);
                                return List.of();
                            }

                            RetrievalReqVo.RetrievalReqVoBuilder builder = createRetrievalReqBaseBuilder(query, List.of(), videoConfig)
                                    .retrievalType(RetrievalReqVo.RetrievalType.PRODUCT_CHUNK)
                                    .preRankTopK(retrievalRule.getPreRerankTopK())
                                    .rerankTopK(retrievalRule.getRerankTopK())
                                    .maxChunkLength(retrievalRule.getMaxChunkLength())
                                    .vector(embedding.get(0).getEmbedding())
                                    .threshold(retrievalRule.getScoreThreshold())
                                    .rerankThreshold(retrievalRule.getRerankThreshold());

                            SearchChunkResult searchChunkResult = retrievalService.searchContentChunks(builder.build());
                            if (searchChunkResult == null || CollUtil.isEmpty(searchChunkResult.getFileChunks())) {
                                log.info("No video search results found");
                                return List.of();
                            }
                            return searchChunkResult.getFileChunks();
                        } catch (Exception e) {
                            log.error("Error processing video search for agent {}: {}", agent.getId(), e.getMessage());
                            return List.of();
                        }
                    })
                    .orElseGet(() -> {
                        log.warn("No valid agent found for video search");
                        return List.of();
                    });

        } catch (Exception e) {
            log.error("Error during searchVideos", e);
            return List.of();
        } finally {
            stopWatch.stop();
            log.info("{} searchVideos execution time: {} ms", NAME, stopWatch.getTotalTimeMillis());
        }
    }

    private RetrievalReqVo.RetrievalReqVoBuilder createRetrievalReqBaseBuilder(String query, List<Float> queryVector, AssistantAgentConfig config) {
        return RetrievalReqVo.builder()
                .query(query)
                .knowledgeConfigs(config)
                .isHybrid(false)
                .vector(queryVector);
    }
    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }

    @Override
    public Map<String, Class<?>> getInputParams() {
        return INPUT_PARAMS;
    }

    @Override
    public Map<String, Class<?>> getOutputParams() {
        return OUTPUT_PARAMS;
    }
}

```

```
ConfigKey
public final static String RETRIEVAL_RULE_KEY_VIDEO = "robam_product_video";

KnowledgeCategoryConfig
        @JsonProperty("video_categories")
        private List<Long> videoCategories;
```

