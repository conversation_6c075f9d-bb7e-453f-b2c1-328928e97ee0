```java
 if (chunks != null && !chunks.isEmpty()) {
            if (sb.length() > 1) sb.append(",");
            sb.append("\"知识内容\":[");
            for (int i = 0; i < chunks.size(); i++) {
                ChunkContent chunk = chunks.get(i);
                if (i > 0) sb.append(",");
                Integer no = i + 1;
                sb.append("{");
                if (chunk.getId() != null) {
                    sb.append("\"知识ID\":\"").append(chunk.getId()).append("\",");
                }
                sb.append("\"内容").append(no);
                sb.append("\":\"").append(chunk.getContent()).append("\"}");
            }
            sb.append("]");
        }
```

