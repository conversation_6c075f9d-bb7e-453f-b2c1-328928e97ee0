```vue
<template>
  <div class="llm-debug-page">
    <div class="header">
      <a-button class="back-button" @click="goBack">
        <template #icon><icon-left /></template>
        返回
      </a-button>
      <h2 class="title">LLM 节点调试: {{ route.query.title }}</h2>
    </div>

    <div class="main-content">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <div class="node-info">
          <div class="info-item">
            <span class="label">节点 ID:</span>
            <span class="value">{{ nodeId }}</span>
          </div>
          <div class="info-item">
            <span class="label">节点标题:</span>
            <span class="value">{{ route.query.title }}</span>
          </div>
        </div>

        <div class="config-sections">
          <!-- 模型配置 -->
          <div class="model-config section">
            <h4>模型配置</h4>
            
            <!-- 模型提供商 -->
            <div class="config-item">
              <span class="config-label">模型提供商:</span>
              <a-select
                v-model="modelConfig.provider"
                placeholder="选择模型提供商"
                :loading="loadingModels"
                allow-clear
              >
                <a-option 
                  v-for="provider in llmModels" 
                  :key="provider.name" 
                  :value="provider.name"
                >
                  {{ provider.name }}
                </a-option>
              </a-select>
            </div>

            <!-- 模型名称 -->
            <div class="config-item">
              <span class="config-label">模型名称:</span>
              <a-select
                v-model="modelConfig.name"
                placeholder="选择模型"
                :disabled="!modelConfig.provider"
                allow-clear
              >
                <a-option 
                  v-for="model in availableModels" 
                  :key="model" 
                  :value="model"
                >
                  {{ model }}
                </a-option>
              </a-select>
            </div>

            <!-- 温度滑块 -->
            <div class="config-item">
              <span class="config-label">温度: {{ modelConfig.temperature }}</span>
              <a-slider
                v-model="modelConfig.temperature"
                :min="0"
                :max="2"
                :step="0.1"
                show-ticks
              />
            </div>
          </div>

          <!-- 提示词配置 -->
          <div class="prompt-config section">
            <h4>提示词配置</h4>
            <div class="prompt-list">
              <div v-for="(prompt, index) in prompts" :key="index" class="prompt-item">
                <div class="prompt-header">
                  <a-select v-model="prompt.role" class="role-select">
                    <a-option value="system">System</a-option>
                    <a-option value="user">User</a-option>
                    <a-option value="assistant">Assistant</a-option>
                  </a-select>
                  <a-button type="text" status="danger" @click="removePrompt(index)">
                    <template #icon><icon-delete /></template>
                  </a-button>
                </div>
                <a-textarea
                  v-model="prompt.content"
                  :placeholder="getPromptPlaceholder(prompt.role)"
                  :auto-size="{ minRows: 6, maxRows: 15 }"
                  @input="updatePromptDisplay(index)"
                />
              </div>
            </div>
            <a-button class="add-prompt-btn" @click="addPrompt">
              <template #icon><icon-plus /></template>
              添加提示词
            </a-button>
          </div>

          <!-- 变量配置 -->
          <div class="variables-config section">
            <h4>变量配置</h4>
            <div class="variables-list">
              <div v-for="(value, key) in debugInput" :key="key" class="variable-item">
                <div class="variable-header">
                  <span class="var-name">{{ variableNames[key] }}</span>
                  <div class="var-actions">
                    <a-button type="text" status="danger" @click="removeVariable(key)">
                      <template #icon><icon-delete /></template>
                    </a-button>
                  </div>
                </div>
                <a-textarea
                  v-model="debugInput[key]"
                  :placeholder="`输入 ${variableNames[key]} 的值`"
                  :auto-size="{ minRows: 2, maxRows: 5 }"
                />
              </div>
            </div>
            <div class="variables-hint" v-if="Object.keys(debugInput).length === 0">
              在提示词中使用 {{变量名}} 格式添加变量
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧聊天面板 -->
      <div class="chat-container">
        <div class="messages-container">
          <div v-for="(message, index) in chatMessages" :key="index" 
               :class="['message', message.role]">
            <div class="message-header">
              <img :src="message.role === 'assistant' ? '/ai-avatar.png' : '/user-avatar.png'" 
                   :alt="message.role" class="avatar">
              <span class="role">{{ message.role === 'assistant' ? 'AI助手' : '用户' }}</span>
              <span class="time">{{ message.time }}</span>
            </div>
            <div class="message-content">{{ message.content }}</div>
          </div>
        </div>
        
        <div class="input-area">
          <t-space direction="vertical" style="width: 100%">
            <t-textarea
              v-model="debugInput"
              :autosize="{ minRows: 3, maxRows: 5 }"
              placeholder="请输入调试内容..."
            />
            <t-space>
              <t-button @click="handleSend" theme="primary">发送</t-button>
              <t-button @click="clearMessages">清空对话</t-button>
            </t-space>
          </t-space>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconLeft, IconPlus, IconDelete, IconRefresh } from '@arco-design/web-vue/es/icon'
import { useLLMStore } from '../stores/LLMStore'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import { getLlmModels } from '@/api/workflow'
import { Button, Space, Textarea } from 'tdesign-vue-next'

const route = useRoute()
const router = useRouter()
const llmStore = useLLMStore()

const nodeId = ref(route.params.nodeId)
const isDebugging = ref(false)
const debugInput = ref({})
const debugOutput = ref('')
const variableNames = ref({})
const chatMessages = ref([])
const userInput = ref('')
const chatMessagesRef = ref(null)

// 模型配置
const modelConfig = ref({
  provider: '',
  name: '',
  temperature: 0.7
})

// 提示词列表
const prompts = ref([
  {
    role: 'system',
    content: ''
  }
])

// 模型列表相关状态
const llmModels = ref([])
const loadingModels = ref(false)

// 获取模型列表
const fetchLlmModels = async () => {
  loadingModels.value = true
  try {
    const response = await getLlmModels()
    if (response.code === 200) {
      llmModels.value = response.data
    } else {
      Message.error('获取模型列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
    Message.error('获取模型列表失败: ' + error.message)
  } finally {
    loadingModels.value = false
  }
}

// 获取当前提供商支持的模型列表
const availableModels = computed(() => {
  if (!modelConfig.value.provider) return []
  const provider = llmModels.value.find(p => p.name === modelConfig.value.provider)
  return provider ? provider.supportModels : []
})

// 获取提示词占位符
const getPromptPlaceholder = (role) => {
  switch (role) {
    case 'system':
      return '输入系统提示词...'
    case 'user':
      return '输入用户提示词...'
    case 'assistant':
      return '输入助手提示词...'
    default:
      return '输入提示词...'
  }
}

// 添加提示词
const addPrompt = () => {
  prompts.value.push({
    role: 'user',
    content: ''
  })
}

// 删除提示词
const removePrompt = (index) => {
  prompts.value.splice(index, 1)
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 添加变量
const addVariable = () => {
  const varKey = Date.now().toString()
  debugInput.value[varKey] = ''
  variableNames.value[varKey] = `variable_${Object.keys(debugInput.value).length}`
}

// 删除变量
const removeVariable = (key) => {
  delete debugInput.value[key]
  delete variableNames.value[key]
}

// 清空输出
const clearOutput = () => {
  debugOutput.value = ''
}

// 复制输出
const copyOutput = async () => {
  try {
    await navigator.clipboard.writeText(debugOutput.value)
    Message.success('已复制到剪贴板')
  } catch (err) {
    Message.error('复制失败')
  }
}

// 格式化消息内容（支持 Markdown）
const formatMessage = (content) => {
  const html = marked.parse(content)
  return DOMPurify.sanitize(html, { ADD_ATTR: ['class'] })
}

// 获取角色名称
const getRoleName = (role) => {
  switch (role) {
    case 'system': return '系统'
    case 'user': return '用户'
    case 'assistant': return '助手'
    default: return role
  }
}

// 清空聊天
const clearChat = () => {
  chatMessages.value = []
}

// 复制聊天内容
const copyChat = async () => {
  try {
    const chatText = chatMessages.value
      .map(msg => `${getRoleName(msg.role)}: ${msg.content}`)
      .join('\n\n')
    await navigator.clipboard.writeText(chatText)
    Message.success('已复制对话内容')
  } catch (err) {
    Message.error('复制失败')
  }
}

// 提取提示词中的变量占位符
const extractVariables = (prompts) => {
  const variables = new Set()
  const pattern = /\{\{([^}]+)\}\}/g
  
  prompts.forEach(prompt => {
    let match
    while ((match = pattern.exec(prompt.content)) !== null) {
      variables.add(match[1].trim())
    }
  })
  
  return Array.from(variables)
}

// 更新变量配置
const updateVariables = () => {
  const variables = extractVariables(prompts.value)
  const currentVars = Object.values(variableNames.value)
  
  // 移除不再使用的变量
  Object.entries(variableNames.value).forEach(([key, name]) => {
    if (!variables.includes(name)) {
      delete variableNames.value[key]
      delete debugInput.value[key]
    }
  })
  
  // 添加新变量
  variables.forEach(varName => {
    if (!currentVars.includes(varName)) {
      const varKey = Date.now().toString() + Math.random().toString(36).substr(2, 5)
      variableNames.value[varKey] = varName
      debugInput.value[varKey] = ''
    }
  })
}

// 监听提示词变化
watch(() => prompts.value, (newPrompts) => {
  updateVariables()
}, { deep: true })

// 检查提示词是否包含变量
const hasVariables = (content) => {
  const pattern = /\{\{([^}]+)\}\}/g
  return pattern.test(content)
}


// 监听变量值变化，更新所有提示词显示
watch(() => debugInput.value, () => {
  prompts.value = [...prompts.value]
}, { deep: true })


// 获取拼接后的提示词内容
const getCombinedPrompts = () => {
  const result = []
  prompts.value.forEach(prompt => {
    let content = prompt.content
    // 替换变量
    Object.entries(variableNames.value).forEach(([key, name]) => {
      const value = debugInput.value[key]
      if (value && value.trim()) {
        content = content.replace(new RegExp(`\\{\\{${name}\\}\\}`, 'g'), value)
      }
    })
    // 添加角色标记
    result.push(`[${getRoleName(prompt.role)}]:\n${content}\n`)
  })
  return result.join('\n')
}

// 刷新拼接内容
const refreshCombinedPrompts = () => {
  userInput.value = getCombinedPrompts()
}

// 修改发送消息函数
const sendMessage = async () => {
  if (!userInput.value.trim() || isDebugging.value) return

  try {
    isDebugging.value = true

    // 添加用户消息
    chatMessages.value.push({
      role: 'user',
      content: userInput.value,
      time: new Date().toLocaleTimeString()
    })

    // 构建调试数据
    const debugData = {
      model: {
        provider: modelConfig.value.provider,
        name: modelConfig.value.name,
        temperature: modelConfig.value.temperature
      },
      prompts: prompts.value.map(prompt => {
        let content = prompt.content
        Object.entries(variableNames.value).forEach(([key, name]) => {
          const value = debugInput.value[key]
          if (value && value.trim()) {
            content = content.replace(new RegExp(`\\{\\{${name}\\}\\}`, 'g'), value)
          }
        })
        return { ...prompt, content }
      }),
      variables: Object.entries(debugInput.value).reduce((acc, [key, value]) => {
        acc[variableNames.value[key]] = value
        return acc
      }, {})
    }

    // 这里添加实际的调试逻辑
    // const response = await debugLLMNode(nodeId.value, debugData)
    
    // 模拟调试过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 添加助手回复
    chatMessages.value.push({
      role: 'assistant',
      content: '这是一个模拟的回复消息。\n\n在实际实现中，这里将显示 LLM 的真实响应。\n\n```python\ndef hello():\n    print("Hello, World!")\n```',
      time: new Date().toLocaleTimeString()
    })

    // 滚动到底部
    await nextTick()
    if (chatMessagesRef.value) {
      chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    Message.error(`发送失败: ${error.message}`)
  } finally {
    isDebugging.value = false
  }
}

// 监听变量和提示词变化，自动更新输入框
watch([() => debugInput.value, () => prompts.value], () => {
  refreshCombinedPrompts()
}, { deep: true })

onMounted(async () => {
  // 获取模型列表
  await fetchLlmModels()
  
  // 获取节点数据
  const nodeData = llmStore.getLlmNodeById(nodeId.value)
  if (nodeData) {
    console.log('已找到节点数据:', nodeData)
    
    // 初始化模型配置
    if (nodeData.data.model) {
      modelConfig.value = {
        provider: nodeData.data.model.provider || '',
        name: nodeData.data.model.name || '',
        temperature: nodeData.data.model.completion_params?.temperature || 0.7
      }
    }

    // 初始化提示词
    if (nodeData.data.prompt_template) {
      prompts.value = nodeData.data.prompt_template.map(item => ({
        role: item.role,
        content: item.text
      }))
    }

    // 初始化变量
    const initialVar = `variable_1`
    const varKey = Date.now().toString()
    debugInput.value[varKey] = ''
    variableNames.value[varKey] = initialVar
  } else {
    console.warn('未找到节点数据')
    Message.warning('未找到节点数据')
  }
})
</script>

<style lang="less" scoped>
.llm-debug-container {
  height: 100%;
  display: flex;
  
  .chat-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--td-component-border);
    border-radius: 8px;
    overflow: hidden;
  }

  .messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: #f5f5f5;
  }

  .message {
    margin-bottom: 16px;
    max-width: 85%;

    &.user {
      margin-left: auto;
      .message-header {
        flex-direction: row-reverse;
      }
      .message-content {
        background-color: #e3f2fd;
        border-radius: 12px 2px 12px 12px;
      }
    }

    &.assistant {
      margin-right: auto;
      .message-content {
        background-color: #fff;
        border-radius: 2px 12px 12px 12px;
      }
    }
  }

  .message-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      margin: 0 8px;
    }

    .role {
      font-weight: 500;
      color: #333;
    }

    .time {
      font-size: 12px;
      color: #999;
      margin-left: 8px;
    }
  }

  .message-content {
    padding: 12px 16px;
    line-height: 1.5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .input-area {
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid var(--td-component-border);
  }
}
</style> 
```

