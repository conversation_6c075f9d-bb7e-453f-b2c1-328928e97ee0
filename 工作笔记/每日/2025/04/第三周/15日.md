- [ ] memory服务调研出文档
- [ ] memoryJava服务开发
- [ ] 正式环境发版



```shell
git branch -r //查看远程分支
git fetch --all //更新远程分支
```





```java
package com.robam.roki.ai.agent.manager.api.controller.dm;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.robam.ai.aio.common.utils.JsonUtils;
import com.robam.roki.ai.agent.manager.api.model.cookerygod.request.WorkflowUpdateReq;
import com.robam.roki.ai.agent.manager.api.model.cookerygod.response.WorkflowHistoryVO;
import com.robam.roki.ai.agent.manager.api.model.dm.response.AppClientResq;
import com.robam.roki.ai.agent.manager.ckb.mapper.WorkflowMapper;
import com.robam.roki.ai.agent.manager.dm.service.IAppClientService;
import com.robam.roki.ai.agent.manager.service.WorkflowChatService;
import com.robam.roki.ai.agent.manager.utils.RedisService;
import com.robam.roki.ai.agent.manager.workflow.model.Workflow;
import com.robam.roki.ai.agent.manager.workflow.model.WorkflowHistory;
import com.robam.roki.ai.agent.manager.workflow.model.graph.Graph;
import com.robam.roki.ai.base.common.core.Result;
import com.robam.roki.ai.base.common.helper.ErrorHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 工作流管理接口
 * <AUTHOR>
 * @Date 2024/11/12 10:21
 */
@RestController
@RequestMapping("/api/llm/workflow")
@Tag(name = "工作流管理接口")
@Slf4j
public class WorkflowController {

    @Autowired
    private WorkflowChatService workflowChatService;
    @Autowired
    private IAppClientService appClientService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private WorkflowMapper workflowMapper;

    private final static String WORKFLOW_CACHE_KEY = "workflow:cache:";

//    @Operation(summary = "更新工作流")
//    @PostMapping("/update")
//    public Result<Void> updateWorkflow(@Validated @RequestBody Graph graph) {
//        workflowService.updateWorkflow(graph);
//        return Result.success();
//    }


    @Operation(summary = "获取工作流graph数据")
    @GetMapping("/get/graph")
    @SaIgnore
    public Result<Object> getWorkflowGraph(@RequestParam("clientKey") String clientKey){
        AppClientResq appClient = appClientService.getAppClientList()
                .stream()
                .filter(appClientResq -> appClientResq.getClientKey().equals(clientKey))
                .findFirst()
                .orElse(null);
        if (appClient == null) {
            return Result.error("clientKey不存在");
        }
        Workflow workflow = workflowChatService.getWorkflow(appClient.getAgentId());

        ObjectNode wrapperNode = objectMapper.createObjectNode();
        try {
            JsonNode graphNode = objectMapper.readTree(workflow.getGraph());
            wrapperNode.setAll((ObjectNode) graphNode);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return Result.success(wrapperNode);
    }


    @Operation(summary = "获取工作流历史版本")
    @GetMapping("/cache")
    @SaIgnore
    public Result<List<WorkflowHistoryVO>> getWorkflowCache(@RequestParam("clientKey") String clientKey) {
        List<Object> cacheList = redisService.getCacheList(WORKFLOW_CACHE_KEY + clientKey);
        if (cacheList == null || cacheList.isEmpty()) {
            return Result.success(Collections.emptyList());
        }
        return Result.success(cacheList.stream()
                .map(item -> {
                    WorkflowHistory history = (WorkflowHistory) item;
                    return new WorkflowHistoryVO(
                            history.getId(),
                            history.getUpdateTime(),
                            history.getDescription()
                    );
                })
                .sorted((a, b) -> b.getUpdateTime().compareTo(a.getUpdateTime()))
                .collect(Collectors.toList()));
    }

    @Operation(summary = "暂存工作流")
    @PostMapping("/cache/update")
    @SaIgnore
    public Result<Void> cacheUpdateWorkflow(@RequestBody WorkflowUpdateReq req) {
        Graph graph = JsonUtils.readValue(req.getGraphString(), Graph.class);
        ErrorHelper.errIf(graph == null, "graph数据格式错误");
        ErrorHelper.errIf(graph.getNodes().isEmpty(), "graph数据不能为空");
        return handleWorkflowUpdate(req, false);
    }

    @Operation(summary = "保存工作流")
    @PostMapping("/update")
    @SaIgnore
    public Result<Void> updateWorkflow(@Validated @RequestBody WorkflowUpdateReq req) {
        return handleWorkflowUpdate(req, true);
    }

    private Result<Void> handleWorkflowUpdate(WorkflowUpdateReq req, boolean saveToDb) {
        AppClientResq appClient = appClientService.getAppClientList()
                .stream()
                .filter(appClientResq -> appClientResq.getClientKey().equals(req.getClientKey()))
                .findFirst()
                .orElse(null);
        if (appClient == null) {
            return Result.error("clientKey不存在");
        }

        Graph graph = JsonUtils.readValue(req.getGraphString(), Graph.class);
        ErrorHelper.errIf(graph == null, "graph数据格式错误");
        ErrorHelper.errIf(graph.getNodes().isEmpty(), "graph数据不能为空");

        if (saveToDb) {
            Workflow workflow = workflowChatService.getWorkflow(appClient.getAgentId());
            if (workflow == null) {
                return Result.error("工作流不存在");
            }

            int updateResult = workflowMapper.updateGraphById(workflow.getId(), req.getGraphString());
            if (updateResult <= 0) {
                return Result.error("更新工作流失败");
            }
        }

        // 创建历史记录
        ObjectNode wrapperNode = objectMapper.createObjectNode();
        try {
            JsonNode graphNode = objectMapper.readTree(req.getGraphString());
            wrapperNode.setAll((ObjectNode) graphNode);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        WorkflowHistory history = new WorkflowHistory(
                IdUtil.getSnowflakeNextIdStr(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                req.getDescription(),
                wrapperNode
        );

        String cacheKey = WORKFLOW_CACHE_KEY + req.getClientKey();
        redisService.rightPush(cacheKey, history);

        List<Object> cacheList = redisService.getCacheList(cacheKey);
        if (!cacheList.isEmpty() && cacheList.size() > 10) {
            redisService.leftPop(cacheKey);
        }

        return Result.success();
    }

    @Operation(summary = "获取工作流历史版本详情")
    @GetMapping("/cache/detail")
    @SaIgnore
    public Result<Object> getWorkflowCacheDetail(
            @RequestParam("clientKey") String clientKey,
            @RequestParam("id") String id) {
        List<Object> cacheList = redisService.getCacheList(WORKFLOW_CACHE_KEY + clientKey);
        if (cacheList == null || cacheList.isEmpty()) {
            return Result.error("未找到历史记录");
        }

        // 查找指定id的历史记录
        Optional<WorkflowHistory> historyOpt = cacheList.stream()
                .map(item -> (WorkflowHistory) item)
                .filter(history -> history.getId().equals(id))
                .findFirst();

        if (historyOpt.isPresent()) {
           return Result.success(historyOpt.get().getGraphString());
        } else {
            return Result.error("未找到指定版本的历史记录");
        }
    }

    @Operation(summary = "获取工作流节点名称列表")
    @GetMapping("/nodes/names")
    @SaIgnore
    public Result<List<String>> getNodeNames(@RequestParam("clientKey") String clientKey) {
        AppClientResq appClient = appClientService.getAppClientList()
                .stream()
                .filter(appClientResq -> appClientResq.getClientKey().equals(clientKey))
                .findFirst()
                .orElse(null);
        if (appClient == null) {
            return Result.error("clientKey不存在");
        }
        Workflow workflow = workflowChatService.getWorkflow(appClient.getAgentId());
        List<String> llmNodes = workflow.getGraphObj().getNodes()
                .stream()
                .filter(node -> {
                    JsonNode data = node.getData();
                    return data != null &&
                            data.has("type") &&
                            "llm".equalsIgnoreCase(data.get("type").asText());
                })
                .map(node -> {
                    JsonNode data = node.getData();
                    return data.get("desc").asText();
                })
                .collect(Collectors.toList());


        return Result.success(llmNodes);
    }

//    @Operation(summary = "添加测试历史数据")
//    @PostMapping("/cache/init")
//    @SaIgnore
//    public Result<Void> initWorkflowCache(@RequestParam("clientKey") String clientKey) {
//        // 获取当前工作流
//        AppClientResq appClient = appClientService.getAppClientList()
//                .stream()
//                .filter(appClientResq -> appClientResq.getClientKey().equals(clientKey))
//                .findFirst()
//                .orElse(null);
//        if (appClient == null) {
//            return Result.error("clientKey不存在");
//        }
//        Workflow workflow = workflowChatService.getWorkflow(appClient.getAgentId());
//        List<Node> currentNodes = workflow.getGraphObj().getNodes();
//
//        String cacheKey = WORKFLOW_CACHE_KEY + clientKey;
//        // 先清除旧数据
//        redisService.deleteObject(cacheKey);
//
//        // 生成5条测试数据
//        for (int i = 0; i < 5; i++) {
//            WorkflowHistory history = new WorkflowHistory(
//                    IdUtil.getSnowflakeNextIdStr(),
//                    LocalDateTime.now().minusDays(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
//                    "版本更新" + (10 - i),
//                    currentNodes
//            );
//            // 使用leftPush将数据添加到列表头部
//            redisService.leftPush(cacheKey, history);
//        }
//        return Result.success();
//    }
}
```

```java
  @Update("UPDATE workflow SET graph = #{graph}, updated_at = NOW() WHERE id = #{id}")
  int updateGraphById(@Param("id") String id, @Param("graph") String graph);

 <update id="updateGraphById">
        UPDATE workflow 
        SET graph = #{graph},
            updated_at = NOW()
        WHERE id = #{id}
          AND is_deleted = 0
    </update>
              

    @NotBlank(message = "更新人不能为空")
    private String updateBy;              

```



