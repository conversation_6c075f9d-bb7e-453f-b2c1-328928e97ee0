## 总结

看看收获 看看结果 看看成长 看看反思

- 记录一周笔记感觉 还行 至少每天 乱扔 文件有地方放，也有地方能查 
- 这种突破了下 记录了长期记忆服务 感觉这是 好的开始
- 做workflow可视化这个 进一步说明了一个 问题 
  - AI 越来越强大 我们只要了解整个项目构建 其实 就能开发出一个 所以我们学习思路 应该是 框架思维 整体思维 流程思维 核心思维 从易到难 层层递进
  - 比如 我们做前端 那个 我们有vite+vue 基础 知道 
    - 前端项目怎么搭建
    - 需要什么组件 axios pinia router UI 组件 知道这些是干啥的 
    - 然后从核心内容开始 给一个 清晰示例 让他了解 你要干啥 先做个demo 然后层层加码																								

**总结** 

了解整体运行需要什么 -》 每个都是干啥的 -》找到核心功能 -》匹配简单示例 -》 层层递进



这周还看了一个 https://rncg5jvpme.feishu.cn/wiki/U9rYwRHQoil6vBkitY8cbh5tnL9

他们做的教程 让我反思 该这么做笔记 笔记让人看懂 让人 学习到 我们才有价值