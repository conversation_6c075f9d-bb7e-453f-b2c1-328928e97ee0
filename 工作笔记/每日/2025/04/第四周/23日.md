```mermaid
graph TD
    A[MemoryScope] --> B[MemoryscopeContext]
    A --> C[ConfigManager]
    
    B --> D[ThreadPool]
    B --> E[MemoryStore]
    B --> F[ModelDict]
    B --> G[MemoryServiceDict]
    B --> H[MemoryChatDict]
    
    F --> I[EmbeddingModel]
    F --> J[GenerationModel]
    F --> K[RankModel]
    
    G --> L[BaseMemoryService]
    L --> M[BaseWorker]
    M --> N[WorkflowContext]
    
    H --> O[BaseMemoryChat]
    O --> P[ChatWorkflow]
    P --> Q[MemoryRetrievalWorker]
    P --> R[ResponseGenerationWorker]
    P --> S[ContextPrepareWorker]
```

```mermaid
graph TB
    A[MemoryScope] --> B[Config初始化]
    B --> C[Context初始化]
    C --> D[Service初始化]
    D --> E[Model初始化]
    E --> F[Worker初始化]

    subgraph 运行时
        G[Service] --> H[Worker执行]
        H --> I[Model调用]
        I --> J[存储操作]
    end
```

```mermaid
graph LR
    A[MemoryScope] --> B[MemoryscopeContext]
    B --> C[Service层]
    B --> D[Model层]
    B --> E[Storage层]
    
    C --> F[Worker]
    F --> G[Operation]
    G --> D
    G --> E
```

```JAVA
package com.robam.roki.ai.agent.manager.api.controller.dm;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.robam.ai.aio.common.utils.JsonUtils;
import com.robam.roki.ai.agent.manager.api.model.ChatMessage;
import com.robam.roki.ai.agent.manager.api.model.cookerygod.request.WorkflowUpdateReq;
import com.robam.roki.ai.agent.manager.api.model.cookerygod.response.WorkflowHistoryVO;
import com.robam.roki.ai.agent.manager.api.model.dm.request.LlmChatRequest;
import com.robam.roki.ai.agent.manager.api.model.dm.request.WorkflowRollbackReq;
import com.robam.roki.ai.agent.manager.api.model.dm.response.AppClientResq;
import com.robam.roki.ai.agent.manager.ckb.mapper.WorkflowMapper;
import com.robam.roki.ai.agent.manager.dm.config.LlmConfig;
import com.robam.roki.ai.agent.manager.dm.service.IAppClientService;
import com.robam.roki.ai.agent.manager.service.WorkflowChatService;
import com.robam.roki.ai.agent.manager.utils.LLMUtils;
import com.robam.roki.ai.agent.manager.utils.RedisService;
import com.robam.roki.ai.agent.manager.workflow.config.ModelConfig;
import com.robam.roki.ai.agent.manager.workflow.model.Workflow;
import com.robam.roki.ai.agent.manager.workflow.model.WorkflowHistory;
import com.robam.roki.ai.agent.manager.workflow.model.graph.Graph;
import com.robam.roki.ai.base.common.core.PageResult;
import com.robam.roki.ai.base.common.core.Result;
import com.robam.roki.ai.base.common.helper.ErrorHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.BodyInserters;
import reactor.core.publisher.Flux;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.OpenAiApi.ChatCompletionRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 工作流管理接口
 * <AUTHOR>
 * @Date 2024/11/12 10:21
 */
@RestController
@RequestMapping("/api/llm/workflow")
@Tag(name = "工作流管理接口")
@Slf4j
public class WorkflowController {

    private final static String WORKFLOW_CACHE_KEY = "workflow:cache:";
    @Autowired
    private WorkflowChatService workflowChatService;
    @Autowired
    private IAppClientService appClientService;
    @Autowired
    private LlmConfig llmProviderConfig;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private WorkflowMapper workflowMapper;
    @Autowired
    private ModelConfig modelConfig;

//    @Operation(summary = "更新工作流")
//    @PostMapping("/update")
//    public Result<Void> updateWorkflow(@Validated @RequestBody Graph graph) {
//        workflowService.updateWorkflow(graph);
//        return Result.success();
//    }

    @Operation(summary = "获取工作流graph数据")
    @GetMapping("/get/graph")
    @SaIgnore
    public Result<Object> getWorkflowGraph(@RequestParam("clientKey") String clientKey){
        AppClientResq appClient = appClientService.getAppClientList()
                .stream()
                .filter(appClientResq -> appClientResq.getClientKey().equals(clientKey))
                .findFirst()
                .orElse(null);
        if (appClient == null) {
            return Result.error("clientKey不存在");
        }
        Workflow workflow = workflowChatService.getWorkflow(appClient.getAgentId());

        ObjectNode wrapperNode = objectMapper.createObjectNode();
        try {
            JsonNode graphNode = objectMapper.readTree(workflow.getGraph());
            wrapperNode.setAll((ObjectNode) graphNode);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        return Result.success(wrapperNode);
    }


    @Operation(summary = "获取工作流历史版本")
    @GetMapping("/cache")
    @SaIgnore
    public Result<PageResult<WorkflowHistoryVO>> getWorkflowCache(
            @RequestParam("clientKey") String clientKey,
            @RequestParam(value = "pageNum", defaultValue = "1") Long pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Long pageSize) {
        List<Object> cacheList = redisService.getCacheList(WORKFLOW_CACHE_KEY + clientKey);
        if (cacheList == null || cacheList.isEmpty()) {
            PageResult<WorkflowHistoryVO> emptyPageResult = new PageResult<>();
            emptyPageResult.setPageNum(pageNum);
            emptyPageResult.setPageSize(pageSize);
            emptyPageResult.setTotal(0L);
            return Result.success(emptyPageResult);
        }

        List<WorkflowHistoryVO> historyList = cacheList.stream()
                .map(item -> {
                    WorkflowHistory history = (WorkflowHistory) item;
                    return new WorkflowHistoryVO(
                            history.getId(),
                            history.getUpdateTime(),
                            history.getUpdateBy(),
                            history.getDescription()
                    );
                })
                .sorted((a, b) -> b.getUpdateTime().compareTo(a.getUpdateTime()))
                .collect(Collectors.toList());

        // 计算分页
        long total = historyList.size();
        long start = (pageNum - 1) * pageSize;
        long end = Math.min(start + pageSize, total);

        List<WorkflowHistoryVO> pageList = start >= total ?
                Collections.emptyList() :
                historyList.subList((int)start, (int)end);

        PageResult<WorkflowHistoryVO> pageResult = new PageResult<>();
        pageResult.setPageNum(pageNum);
        pageResult.setPageSize(pageSize);
        pageResult.setList(pageList);
        pageResult.setTotal(total);

        return Result.success(pageResult);
    }



    @Operation(summary = "暂存工作流")
    @PostMapping("/cache/update")
    @SaIgnore
    public Result<Void> cacheUpdateWorkflow(@RequestBody WorkflowUpdateReq req) {
        Graph graph = JsonUtils.readValue(req.getGraphString(), Graph.class);
        ErrorHelper.errIf(graph == null, "graph数据格式错误");
        ErrorHelper.errIf(graph.getNodes().isEmpty(), "graph数据不能为空");
        return handleWorkflowUpdate(req, false);
    }

    @Operation(summary = "保存工作流")
    @PostMapping("/update")
    @SaIgnore
    public Result<Void> updateWorkflow(@Validated @RequestBody WorkflowUpdateReq req) {
        return handleWorkflowUpdate(req, true);
    }

    private Result<Void> handleWorkflowUpdate(WorkflowUpdateReq req, boolean saveToDb) {
        AppClientResq appClient = appClientService.getAppClientList()
                .stream()
                .filter(appClientResq -> appClientResq.getClientKey().equals(req.getClientKey()))
                .findFirst()
                .orElse(null);
        if (appClient == null) {
            return Result.error("clientKey不存在");
        }

        Graph graph = JsonUtils.readValue(req.getGraphString(), Graph.class);
        ErrorHelper.errIf(graph == null, "graph数据格式错误");
        ErrorHelper.errIf(graph.getNodes().isEmpty(), "graph数据不能为空");

        Workflow workflow = workflowChatService.getWorkflow(appClient.getAgentId());
        if (saveToDb) {
            if (workflow == null) {
                return Result.error("工作流不存在");
            }

            int updateResult = workflowMapper.updateGraphById(workflow.getId(), req.getGraphString());
            if (updateResult <= 0) {
                return Result.error("更新工作流失败");
            }
        }

        String cacheKey = WORKFLOW_CACHE_KEY + req.getClientKey();
        List<Object> cacheList = redisService.getCacheList(cacheKey);



        // 创建历史记录
        ObjectNode wrapperNode = objectMapper.createObjectNode();
        try {
            JsonNode graphNode = objectMapper.readTree(req.getGraphString());
            wrapperNode.setAll((ObjectNode) graphNode);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        WorkflowHistory history = new WorkflowHistory(
                IdUtil.getSnowflakeNextIdStr(),
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                req.getUpdateBy(),
                req.getDescription(),
                wrapperNode
        );

        redisService.rightPush(cacheKey, history);


        // 如果是第一次更新，保存当前工作流数据
        if (cacheList == null || cacheList.isEmpty()) {
            ObjectNode wrapperNodeNew = objectMapper.createObjectNode();
            try {
                JsonNode graphNode = objectMapper.readTree(workflow.getGraph());
                wrapperNodeNew.setAll((ObjectNode) graphNode);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            if (workflow != null) {
                WorkflowHistory currentHistory = new WorkflowHistory(
                        IdUtil.getSnowflakeNextIdStr(),
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                        "系统",
                        "备份工作流数据",
                        wrapperNodeNew
                );
                redisService.rightPush(cacheKey, currentHistory);
            }
        }

        // 限制缓存数量，从头部移除最旧的数据
        if (!cacheList.isEmpty() && cacheList.size() > 30) {
            redisService.leftPop(cacheKey);
        }

        return Result.success();
    }

    @Operation(summary = "获取工作流历史版本详情")
    @GetMapping("/cache/detail")
    @SaIgnore
    public Result<Object> getWorkflowCacheDetail(
            @RequestParam("clientKey") String clientKey,
            @RequestParam("id") String id) {
        List<Object> cacheList = redisService.getCacheList(WORKFLOW_CACHE_KEY + clientKey);
        if (cacheList == null || cacheList.isEmpty()) {
            return Result.error("未找到历史记录");
        }

        // 查找指定id的历史记录
        Optional<WorkflowHistory> historyOpt = cacheList.stream()
                .map(item -> (WorkflowHistory) item)
                .filter(history -> history.getId().equals(id))
                .findFirst();

        if (historyOpt.isPresent()) {
            return Result.success(historyOpt.get().getGraphString());
        } else {
            return Result.error("未找到指定版本的历史记录");
        }
    }

    @Operation(summary = "获取工作流节点名称列表")
    @GetMapping("/nodes/names")
    @SaIgnore
    public Result<List<String>> getNodeNames(@RequestParam("clientKey") String clientKey) {
        AppClientResq appClient = appClientService.getAppClientList()
                .stream()
                .filter(appClientResq -> appClientResq.getClientKey().equals(clientKey))
                .findFirst()
                .orElse(null);
        if (appClient == null) {
            return Result.error("clientKey不存在");
        }
        Workflow workflow = workflowChatService.getWorkflow(appClient.getAgentId());
        List<String> llmNodes = workflow.getGraphObj().getNodes()
                .stream()
                .filter(node -> {
                    JsonNode data = node.getData();
                    return data != null &&
                            data.has("type") &&
                            "llm".equalsIgnoreCase(data.get("type").asText());
                })
                .map(node -> {
                    JsonNode data = node.getData();
                    return data.get("desc").asText();
                })
                .collect(Collectors.toList());


        return Result.success(llmNodes);
    }

//    @Operation(summary = "添加测试历史数据")
//    @PostMapping("/cache/init")
//    @SaIgnore
//    public Result<Void> initWorkflowCache(@RequestParam("clientKey") String clientKey) {
//        // 获取当前工作流
//        AppClientResq appClient = appClientService.getAppClientList()
//                .stream()
//                .filter(appClientResq -> appClientResq.getClientKey().equals(clientKey))
//                .findFirst()
//                .orElse(null);
//        if (appClient == null) {
//            return Result.error("clientKey不存在");
//        }
//        Workflow workflow = workflowChatService.getWorkflow(appClient.getAgentId());
//        List<Node> currentNodes = workflow.getGraphObj().getNodes();
//
//        String cacheKey = WORKFLOW_CACHE_KEY + clientKey;
//        // 先清除旧数据
//        redisService.deleteObject(cacheKey);
//
//        // 生成5条测试数据
//        for (int i = 0; i < 5; i++) {
//            WorkflowHistory history = new WorkflowHistory(
//                    IdUtil.getSnowflakeNextIdStr(),
//                    LocalDateTime.now().minusDays(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
//                    "版本更新" + (10 - i),
//                    currentNodes
//            );
//            // 使用leftPush将数据添加到列表头部
//            redisService.leftPush(cacheKey, history);
//        }
//        return Result.success();
//    }

    @Operation(summary = "回滚工作流到指定历史版本")
    @PostMapping("/rollback")
    @SaIgnore
    public Result<Void> rollbackWorkflow(@Validated @RequestBody WorkflowRollbackReq req) {
        // 1. 校验 clientKey
        AppClientResq appClient = appClientService.getAppClientList()
                .stream()
                .filter(appClientResq -> appClientResq.getClientKey().equals(req.getClientKey()))
                .findFirst()
                .orElse(null);
        if (appClient == null) {
            return Result.error("clientKey不存在");
        }

        String cacheKey = WORKFLOW_CACHE_KEY + req.getClientKey();
        List<Object> cacheList = redisService.getCacheList(cacheKey);
        if (cacheList == null || cacheList.isEmpty()) {
            return Result.error("未找到历史记录");
        }

        Optional<WorkflowHistory> historyOpt = cacheList.stream()
                .map(item -> (WorkflowHistory) item)
                .filter(history -> history.getId().equals(req.getId()))
                .findFirst();

        if (!historyOpt.isPresent()) {
            return Result.error("未找到指定版本的历史记录");
        }

        WorkflowHistory targetHistory = historyOpt.get();

        Workflow currentWorkflow = workflowChatService.getWorkflow(appClient.getAgentId());
        if (currentWorkflow == null) {
            return Result.error("当前工作流不存在");
        }

        String graphString;
        try {
            graphString = objectMapper.writeValueAsString(targetHistory.getGraphString());
        } catch (JsonProcessingException e) {
            log.error("序列化历史记录Graph失败, historyId: {}", req.getId(), e);
            return Result.error("序列化历史记录Graph失败");
        }
        int updateResult = workflowMapper.updateGraphById(currentWorkflow.getId(), graphString);
        if (updateResult <= 0) {
            log.error("回滚工作流失败, workflowId: {}, historyId: {}", currentWorkflow.getId(), req.getId());
            return Result.error("回滚工作流失败");
        }
        log.info("工作流回滚成功, workflowId: {},回滚至 historyId: {}", currentWorkflow.getId(), req.getId());
        return Result.success();
    }

    @Operation(summary = "获取llm")
    @GetMapping("/llm")
    @SaIgnore
    public Result<List<LlmConfig.Provider>> getWorkflowLlm() {
        List<LlmConfig.Provider> providers = llmProviderConfig.getProviders();
        return Result.success(providers);
    }

    @Operation(summary = "LLM 聊天接口")
    @PostMapping(value = "/llm/chat")
    @SaIgnore
    public Object chat(@RequestBody LlmChatRequest request) {
        try {
            OpenAiApi api = modelConfig.getOpenAiApi(request.getProvider(), request.getModelName());
            ErrorHelper.errIf(api == null, "不支持的llm提供商");
            ChatCompletionRequest req = new ChatCompletionRequest(
                    convertToOpenAiMessages(request.getMessages()),  // 使用转换方法
                    request.getModelName(),
                    request.getTemperature(),
                    request.getStream()
            );
            if (request.getStream()) {
                return LLMUtils.callLLMStream(request.getProvider(), api, req)
                        .map(this::processData)
                        .concatWith(Flux.just(ServerSentEvent.builder("[DONE]").build()));
            }
            return Result.success(LLMUtils.callLLM(request.getProvider(), api, req));
        } catch (Exception e) {
            log.error("创建聊天流失败", e);
            return Flux.error(e);
        }
    }

    private ServerSentEvent<String> processData(OpenAiApi.ChatCompletionChunk data) {
        return ServerSentEvent.<String>builder()
                .data(JsonUtils.dump(data))
                .build();
    }

    private List<OpenAiApi.ChatCompletionMessage> convertToOpenAiMessages(List<ChatMessage> messages) {
        return messages.stream()
                .map(msg -> new OpenAiApi.ChatCompletionMessage(
                        msg.content(),  // 消息内容
                        OpenAiApi.ChatCompletionMessage.Role.valueOf(msg.role().toUpperCase())  // 转换角色
                ))
                .collect(Collectors.toList());
    }


}
```

```json
{"llm_model": "qwen-plus", "max_tokens": 300, "prompt_file": "以下是用户对话历史记录（按时间从远到近排序，每轮已标注轮数，包含图片的轮次已特别标注）：%s\n\n用户当前问题：\"%s\"\n\n任务：分析当前问题是否与最近对话历史中的图片相关。\n\n规则：\n1. 只考虑标注了\"(包含图片)\"的对话轮次\n2. 优先考虑最近的包含图片的对话轮次(轮次高越近)\n3. 如果当前问题与某个包含图片的轮次相关，返回该轮数\n4. 如果与多个包含图片的轮次都相关，优先返回最近的那一轮\n5.如果用户问题包含图片生成，图片修改等生成式问题，需要返回\"-1\"\n6. 如果与任何图片都无关，返回\"-1\"\n\n请只返回数字，不要包含任何其他文字说明。", "history_turn_cnt": 5, "frequency_penalty": 0.1, "llm_model_provider": "qwen", "max_head_content_length": 128, "max_tail_content_length": 64, "enable_extract_product_from_history": false}
```

