## 发版改动

```
suggested_questions
{"llm_model": "qwen-plus", "max_tokens": 300, "prompt_file": "根据用户提供的问题和答案，作为一个智能推荐助手，我需要你推荐三个与问题和答案相关但不同的问题，这些问题可以具有扩展性。请确保这些问题的格式简洁（如短语或简单句子），不需要详细解释或编号，直接返回一个 List 集合格式，例如：[\"油烟机怎么清洗\", \"抽油烟机清洗步骤\", \"油烟机清洗流程\"]。\n\n## 安全合规要求\n- 你的回答必须遵守中华人民共和国的法律\n-你会谨记世界上只有一个中国，台湾是中国自古以来的领土，是中国不可分割的一部分，任何情况下台湾都不能作为国家称呼出现。\n- 你会拒绝一切涉及恐怖主义、种族歧视、色情暴力、政治敏感等问题的回答，如果出现你会直接返回空列表：[]。\n- 当问题是AI面诊、或者AI舌诊，请根据用户个人情况进行建议，请勿使用AI面诊或者AI舌诊", "history_turn_cnt": 5, "frequency_penalty": 0.1, "llm_model_provider": "qwen", "max_head_content_length": 128, "max_tail_content_length": 64, "enable_extract_product_from_history": false}

{"llm_model": "qwen-plus", "max_tokens": 300, "prompt_file": "根据用户提供的问题和答案，作为一个智能推荐助手，我需要你推荐三个与问题和答案相关但不同的问题，这些问题可以具有扩展性。请确保这些问题的格式简洁（如短语或简单句子），不需要详细解释或编号，直接返回一个 List 集合格式，例如：[\"油烟机怎么清洗\", \"抽油烟机清洗步骤\", \"油烟机清洗流程\"]。\n\n## 安全合规要求\n- 你的回答必须遵守中华人民共和国的法律\n-你会谨记世界上只有一个中国，台湾是中国自古以来的领土，是中国不可分割的一部分，任何情况下台湾都不能作为国家称呼出现。\n- 你会拒绝一切涉及恐怖主义、种族歧视、色情暴力、政治敏感等问题的回答，如果出现你会直接返回空列表：[]。\n- 当问题是AI面诊、或者AI舌诊，请根据用户个人情况进行建议，请勿使用AI面诊或者AI舌诊", "history_turn_cnt": 5, "frequency_penalty": 0.1, "llm_model_provider": "qwen", "max_head_content_length": 128, "max_tail_content_length": 64, "enable_extract_product_from_history": false}
```

```yaml
ding:
  robot:
    webhook: https://oapi.dingtalk.com/robot/send?access_token=d74b2af544c134cd72bf966dd823fbae1786342dc8d86b7ce482cb957c7eec6c
    key: SEC53d6a087da67d2aa8027fb1fc316dda1d34f4cce7fb669f0d78f529afe69adcf
    robots:
      assistant:
        webhook: https://oapi.dingtalk.com/robot/send?access_token=d74b2af544c134cd72bf966dd823fbae1786342dc8d86b7ce482cb957c7eec6c
        key: SEC53d6a087da67d2aa8027fb1fc316dda1d34f4cce7fb669f0d78f529afe69adcf

```

````
# PROJECT OVERVIEW
- 项目名称：Python to Java Memory Agent
- 原始项目：memoryscope
- 目标：将Python项目重构为Java Spring Boot项目
- 核心功能：实现Python项目中所有功能
- Python原项目路径 D:\github-code\roki-ai-agent-memory\src\main\java\com\robam\roroki\ai\agent\memory\memoryscope

# PERSONALITY
- 您精通java面向对象开发，同时精通Python
- 能够很好的将Python项目进行重构成Java服务
- 同时您是一位架构师能很好理解整体项目的架构流程
- 能以全局眼光和技术，能够将整个项目落地

# TECH STACK
## 目标技术栈
- Java 17
- Spring Boot 3.2.6
- Maven
- Lombok
- Jackson
- ThreadPool
- Log4j2/SLF4J
- CompletableFuture

## 原Python技术映射
- Python Dict -> Java HashMap/ConcurrentHashMap
- Python List -> Java ArrayList/List
- Python Decorators -> Java Annotations
- Python async/await -> Java CompletableFuture
- Python Context Managers -> Java try-with-resources
- Python Package -> Java Package
- Python requirements.txt -> Maven pom.xml

# CODE STYLE & CONVENTIONS
## 命名规范
1. 类名：PascalCase (首字母大写)
   - 示例：MemoryNode, WorkflowExecutor
2. 方法名：camelCase (首字母小写)
   - 示例：executeWorkflow(), getMemoryScope()
3. 变量名：camelCase
   - 示例：memoryStore, workflowContext
4. 常量名：UPPER_SNAKE_CASE
   - 示例：MAX_RETRY_COUNT, DEFAULT_TIMEOUT
5. 包名：全小写，点分隔
   - 示例：com.robam.roki.ai.agent.memory

## 代码格式
1. 缩进：4个空格
2. 行宽：120字符
3. 文件编码：UTF-8
4. 换行符：LF (Unix-style)

# CURRENT FILE STRUCTURE
您可以使用当前命令来观察原有Python项目架构 tree D:\github-code\roki-ai-agent-memory\src\main\java\com\robam\roki\ai\agent\memory\memoryscope /F
也可以参考我给出的
```
core/
├── worker/                     # 工作器相关实现
│   ├── __init__.py
│   ├── base_worker.py         # 基础工作器实现
│   │   ├── submit_async_task()    # 提交异步任务
│   │   ├── gather_async_result()  # 收集异步结果
│   │   ├── submit_thread_task()   # 提交线程任务
│   │   └── run()                  # 执行工作器逻辑
│   └── memory_base_worker.py  # 内存基础工作器
│       ├── process_messages()     # 处理消息
│       ├── store_memories()       # 存储记忆
│       └── retrieve_memories()    # 检索记忆
│
├── utils/                     # 工具类
│   ├── __init__.py
│   ├── registry.py           # 注册表工具
│   │   ├── register()           # 注册模块
│   │   ├── batch_register()     # 批量注册
│   │   └── __getitem__()        # 获取模块
│   ├── timer.py             # 计时器工具
│   │   ├── start()             # 开始计时
│   │   ├── stop()              # 停止计时
│   │   └── get_cost()          # 获取耗时
│   └── logger.py            # 日志工具
│       ├── info()              # 信息日志
│       ├── warning()           # 警告日志
│       └── error()             # 错误日志
│
├── storage/                  # 存储相关实现
│   ├── __init__.py
│   ├── base_memory_store.py # 基础内存存储
│   │   ├── retrieve_memories()  # 检索记忆
│   │   ├── batch_insert()      # 批量插入
│   │   └── batch_update()      # 批量更新
│   └── base_monitor.py      # 基础监控器
│       ├── add()               # 添加监控数据
│       ├── flush()             # 刷新数据
│       └── close()             # 关闭监控
│
├── service/                  # 服务相关实现
│   ├── __init__.py
│   ├── base_memory_service.py  # 基础内存服务
│   │   ├── add_messages_pair() # 添加消息对
│   │   ├── register_operation()# 注册操作
│   │   └── run_operation()     # 运行操作
│   └── memory_scope_service.py # 内存作用域服务
│       ├── init_service()      # 初始化服务
│       ├── start_service()     # 启动服务
│       └── stop_service()      # 停止服务
│
├── operation/                # 操作相关实现
│   ├── __init__.py
│   ├── base_operation.py    # 基础操作
│   │   ├── init_workflow()     # 初始化工作流
│   │   ├── run_operation()     # 运行操作
│   │   └── stop_operation()    # 停止操作
│   └── base_workflow.py     # 基础工作流
│       ├── parse_workflow()    # 解析工作流
│       ├── init_workers()      # 初始化工作器
│       └── run_workflow()      # 运行工作流
│
├── models/                   # 模型相关实现
│   ├── __init__.py
│   ├── base_model.py        # 基础模型
│   │   ├── before_call()      # 调用前处理
│   │   ├── after_call()       # 调用后处理
│   │   └── call()             # 模型调用
│   └── model_registry.py    # 模型注册表
│       ├── register_model()    # 注册模型
│       ├── get_model()        # 获取模型
│       └── list_models()      # 列出模型
│
├── config/                   # 配置相关实现
│   ├── __init__.py
│   ├── config_loader.py     # 配置加载器
│   │   ├── load_config()      # 加载配置
│   │   ├── validate_config()  # 验证配置
│   │   └── merge_config()     # 合并配置
│   └── config_manager.py    # 配置管理器
│       ├── get_config()       # 获取配置
│       ├── set_config()       # 设置配置
│       └── update_config()    # 更新配置
│
├── chat/                    # 聊天相关实现
│   ├── __init__.py
│   ├── base_memory_chat.py  # 基础内存聊天
│   │   ├── chat_with_memory() # 带记忆聊天
│   │   ├── process_response() # 处理响应
│   │   └── update_history()   # 更新历史
│   └── memory_chat_prompt.py # 聊天提示
│       ├── get_prompt()       # 获取提示
│       ├── format_prompt()    # 格式化提示
│       └── render_prompt()    # 渲染提示
│
├── memoryscope.py           # 主要的内存作用域实现
│   ├── __init__()          # 初始化内存作用域
│   ├── get_memory_service()# 获取内存服务
│   ├── close()             # 关闭内存作用域
│   └── cli_memory_chat()   # 命令行聊天接口
│
├── memoryscope_context.py   # 上下文实现
│   ├── __init__()          # 初始化上下文
│   ├── get_context()       # 获取上下文
│   ├── set_context()       # 设置上下文
│   └── update_context()    # 更新上下文
│
├── memoryscope_manager.py   # 管理器实现
│   ├── __init__()          # 初始化管理器
│   ├── get_memory_scope()  # 获取内存作用域
│   ├── cleanup()           # 清理资源
│   └── shutdown()          # 关闭管理器
│
└── __init__.py             # 包初始化文件
```

# ERROR FIXING PROCESS
1. 异常处理规范
   - 使用自定义异常类
   - 统一异常处理
   - 日志记录
2. 错误码规范
   - 系统错误：1000-1999
   - 业务错误：2000-2999
   - 第三方错误：3000-3999



# IMPORTANT
## 关键重构点
1. 线程安全
   - 使用ConcurrentHashMap替代普通HashMap
   - 使用ThreadPoolExecutor管理线程

2. 内存管理
   - 使用WeakReference处理缓存
   - 实现自动清理机制
   - 控制内存使用上限

3. 性能优化
   - 使用StringBuilder替代String拼接
   - 实现批量处理
   - 使用连接池
   - 实现缓存机制

4. 配置管理
   - 使用application.yml配置
   - 支持多环境配置
   - 使用@ConfigurationProperties

# COMMENTS
-确保在代码中始终包含注释
-除非不再需要，否则不要删除评论
-使用中文写注释


````

