```mermaid
graph TD
    A[MemoryScope] --> B[MemoryscopeContext]
    B --> C1[memory_chat_dict]
    B --> C2[memory_service_dict]
    B --> C3[model_dict]
    B --> C4[worker_conf_dict]
    B --> C5[memory_store]
    B --> C6[monitor]
    
    D[配置文件demo_config.yaml] --> |初始化| A
    
    subgraph 上下文初始化
        A --> |1.读取global配置|E1[设置语言/线程池等]
        A --> |2.初始化chat|E2[创建chat实例]
        A --> |3.初始化service|E3[创建service实例]
        A --> |4.初始化model|E4[创建model实例]
        A --> |5.初始化store|E5[创建store实例]
        A --> |6.初始化monitor|E6[创建monitor实例]
    end
    
    subgraph FastAPI应用
        F[main.py] --> |创建|A
        F --> |使用|B
    end
```

```mermaid
graph TD
    A[MemoryScope] --> B[MemoryscopeContext]
    B --> C1[memoryChatMap]
    B --> C2[memoryServiceMap]
    B --> C3[modelMap]
    B --> C4[workerConfigMap]
    B --> C5[memoryStore]
    B --> C6[monitor]
    
    D[application.yml] --> |ConfigurationProperties| E[ConfigProperties]
    E --> |注入| A
```

```mermaid
classDiagram
    class MemoryscopeContext {
        +ConcurrentMap<String, BaseMemoryChat> memoryChatMap
        +ConcurrentMap<String, BaseMemoryService> memoryServiceMap
        +ConcurrentMap<String, BaseModel> modelMap
        +ConcurrentMap<String, Map<String, Object>> workerConfigMap
        +BaseMemoryStore memoryStore
        +BaseMonitor monitor
        +ThreadPoolExecutor threadPool
        +String language
        +String memoryScopeUuid
        +Map<String, Object> metaData
    }

    class MemoryScope {
        -MemoryscopeContext context
        -Map<String, MemoryServiceEntry> serviceDict
        -ReentrantLock serviceLock
        -ScheduledExecutorService cleanupExecutor
        +getMemoryService(String userId, String name): BaseMemoryService
        +close()
        +initContextByConfig()
    }

    MemoryScope --> MemoryscopeContext
```

