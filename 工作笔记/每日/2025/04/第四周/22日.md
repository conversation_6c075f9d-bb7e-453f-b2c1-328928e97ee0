```java
 public GenerateWebofficeTokenResponseBody getOssImm(String ossUrl, String fileName) {
        try {
            String decodedUrl = ossUrl;
            try {
                String tempUrl = URLDecoder.decode(ossUrl, StandardCharsets.UTF_8.name());
                if (tempUrl.contains("oss-cn-") && tempUrl.contains(".aliyuncs.com")) {
                    decodedUrl = tempUrl;
                }
            } catch (Exception e) {
                log.warn("URL解码失败，将使用原始URL: {}", ossUrl);
            }
            log.info("获取图片预览地址oss:{}", decodedUrl);
            String sourceURI = "oss://%s" + decodedUrl.substring(decodedUrl.indexOf(".com") + 4);
            String bucketName = decodedUrl.substring(decodedUrl.indexOf("//") + 2, decodedUrl.indexOf(".", decodedUrl.indexOf("//") + 2));
            String formatted = sourceURI.formatted(bucketName);
            String mainName = FileNameUtil.mainName(ossUrl);
            if (StringUtils.isBlank(fileName)) {
                String suffix = Optional.of(FileNameUtil.getSuffix(ossUrl))
                        .filter(s -> !s.isEmpty())
                        .map(String::toLowerCase)
                        .orElseThrow(()-> new CommonException("OSS地址有误"));
                fileName = mainName + "." + suffix;
            }
            com.aliyun.imm20200930.models.WebofficePermission webofficePermission = new com.aliyun.imm20200930.models.WebofficePermission()
                    .setReadonly(true);
            com.aliyun.imm20200930.models.GenerateWebofficeTokenRequest generateWebofficeTokenRequest = new com.aliyun.imm20200930.models.GenerateWebofficeTokenRequest()
                    .setProjectName(aliyunImmProperties.getProjectName())
                    .setFilename(fileName)
                    .setSourceURI(formatted)
                    .setHidecmb(true)
                    .setPermission(webofficePermission);
            RuntimeOptions runtime = new RuntimeOptions();
            try {
                com.aliyun.imm20200930.Client client = createImmClient();
                GenerateWebofficeTokenResponse generateWebofficeTokenResponse = client.generateWebofficeTokenWithOptions(generateWebofficeTokenRequest, runtime);
                return generateWebofficeTokenResponse.body;
            } catch (Exception error) {
                log.error("Imm Exception : {} , req: {}", error.getMessage(), JsonUtils.dump(generateWebofficeTokenRequest));
                throw new CommonException("Imm获取数据异常");
            }
        } catch (Exception e) {
            log.error("处理OSS URL失败: {}", ossUrl, e);
            throw new CommonException("OSS地址处理失败");
        }
    }

  <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>imm20200930</artifactId>
            <version>${imm.version}</version>
        </dependency>
      
      <imm.version>4.5.4</imm.version>
```

```
基本信息：身高158厘米，体重52千克，目前居住在杭州临平乔司。希望被称呼为“vpo”。
健康与情绪状态：对坚果过敏，需特别注意饮食中避免坚果类食物。患有慢性咽炎（到期时间：2025-10-14）。可能有油性肤质（到期时间：2025-10-14）。有鼻炎（到期时间：2025-10-14）。胃溃疡已痊愈（到期时间：2026-04-22）。
生活习惯与偏好：对奶制品有消费偏好，尤其喜欢奶皮子。不喜欢吃香菜，也不喜欢别人直接称呼她的名字。此外，不喜欢吃酸辣口的菜。
```

```
基本信息：任富佳希望别人称呼他为“富佳”。\n\n生活习惯与偏好：任富佳喜欢红烧这种烹饪方式，特别对红烧羊肉感兴趣，并在寻找临平地区好吃的红烧羊肉。他对杭州的土菜也有浓厚的兴趣。他认为烹饪是最平易近人的创造，因此可能经常在家自己做饭。最近他脸上发油，正在寻求保养方法。此外，任富佳喜欢吃豆腐干和所有豆制品。\n\n社交关系：任富佳有一个儿子，儿子计划在2025年1月5日去美国。2025年3月3日，任富佳有家庭聚餐。\n\n生活轨迹：2025年3月3日，任富佳有家庭聚餐。
```

```json
{"llm_model": "qwen-plus", "max_tokens": 300, "prompt_file": "根据用户提供的问题和答案，作为一个智能推荐助手，我需要你推荐三个与问题和答案相关但不同的问题，这些问题可以具有扩展性。请确保这些问题的格式简洁（如短语或简单句子），不需要详细解释或编号，直接返回一个 List 集合格式，例如：[\"油烟机怎么清洗\", \"抽油烟机清洗步骤\", \"油烟机清洗流程\"]。\n\n## 安全合规要求\n- 你的回答必须遵守中华人民共和国的法律\n-你会谨记世界上只有一个中国，台湾是中国自古以来的领土，是中国不可分割的一部分，任何情况下台湾都不能作为国家称呼出现。\n- 你会拒绝一切涉及恐怖主义、种族歧视、色情暴力、政治敏感等问题的回答，如果出现你会直接返回空列表：[]。\n- 当问题是AI面诊、或者AI舌诊，请根据用户个人情况进行建议", "history_turn_cnt": 5, "frequency_penalty": 0.1, "llm_model_provider": "qwen", "max_head_content_length": 128, "max_tail_content_length": 64, "enable_extract_product_from_history": false}
```

```
基本信息：陈龙希望别人称呼他为Jack，生日是每年8月15日。\n\n健康与情绪状态：陈龙最近情绪不太开心，有轻度焦虑倾向。之前存在失眠问题，现通过冥想训练已明显改善。对海鲜过敏需要特别注意。2025年1月8日吃了火锅后，第二天嘴上长泡了。\n\n生活习惯与偏好：陈龙以前喜欢吃辣的食物，但现在根据医嘱已戒辣，并且开始在家自己做饭，很少点外卖了。最近有减肥的打算。此外，陈龙不是很喜欢吃猪肉。\n\n社交关系：已婚，与妻子感情融洽，育有两个孩子，目前正在考虑要二胎。\n\n工作与兴趣：目前在老板电器工作。喜欢打篮球运动。\n\n生活轨迹：2023年3月从阿里巴巴跳槽到字节跳动，近期生活重心逐渐转向家庭建设。家中的油烟机型号是8008，灶具型号是7b031。
```

