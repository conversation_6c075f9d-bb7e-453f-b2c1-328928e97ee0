## 技术助理 SQL 查询
```sql
        
        WITH base_history AS (
        SELECT
        h.trace_id,
        h.session_id,
        h.consumer_id,
        MA<PERSON>(h.vote) as max_vote,
        MAX(CASE WHEN h.role = 'assistant' THEN h.feedback END) as assistant_feedback,
        MAX(h.rewrite_query) AS rewrite_query,
        MAX(h.created_at) AS created_at,
        MAX(h.feedback) AS feedback,
        MAX(h.chat_file) AS chat_file
        FROM rag_chat_history h
        WHERE created_at >= '2025-01-01'
        AND h.client_key in (1,2)
        AND h.trace_id NOT LIKE '%myroki_test_com'
        GROUP BY h.trace_id, h.session_id, h.consumer_id
        )
        SELECT
        b.trace_id,
        CASE
        WHEN b.trace_id LIKE 'h5%' THEN 'H5端'
        WHEN b.trace_id LIKE 'applet%' THEN '小程序端'
        WHEN b.trace_id LIKE 'app%' THEN 'APP端'
        WHEN b.trace_id LIKE 'box%' THEN '魔盒端'
        ELSE '其他来源'
        END AS user_source,
        b.session_id,
        b.consumer_id,
        user_msg.message AS user_message,
        assistant_msg.message AS assistant_message,
        user_msg.llm_message AS llm_message,
        CASE b.max_vote
        WHEN 0 THEN '正常'
        WHEN 1 THEN '点赞'
        WHEN 2 THEN '点踩'
        ELSE '未知'
        END AS vote,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.reason')) AS reason,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.type')) AS question_type,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.problems')) AS problem_description,
        b.rewrite_query,
        b.created_at,
        b.feedback,
        user_msg.id AS query_id,
        assistant_msg.id AS answer_id,
        b.chat_file,
        c.nickname
        FROM base_history b
        LEFT JOIN rag_chat_history user_msg
        ON b.trace_id = user_msg.trace_id
        AND user_msg.role = 'user'
        LEFT JOIN rag_chat_history assistant_msg
        ON b.trace_id = assistant_msg.trace_id
        AND assistant_msg.role = 'assistant'
        JOIN rag_consumer c
        ON b.consumer_id = c.id
        ORDER BY b.created_at ASC
```

## 周报

memory记忆定时淘汰任务更新，调整示例，prompt

AI 面诊 无序列表调整，prompt调整

App端 pingcode 上提的bug 问题排查修复 



食神长期记忆方案梳理，参与讨论会议，对齐最新功能

食神长期记忆 功能测试 prompt调整

调整workflow可视页面增加 更新功能

app端 提的bug问题排查 关闭



1.食神长期记忆方案梳理，prompt调试，功能提测 ，bug 修复

2.App 提测bug 修改 。目前ASR ,TTS功能已上架

3.调整workflow可视页面（支持选择模型，更新入库，历史更新记录保存）





```java
    @Operation(summary = "LLM 聊天接口")
    @PostMapping(value = "/llm/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @SaIgnore
    public Object chat(@RequestBody LlmChatRequest request) {
        try {
            OpenAiApi api = modelConfig.getOpenAiApi(request.getProvider(), request.getModelName());
            ErrorHelper.errIf(api == null, "不支持的llm提供商");
            ChatCompletionRequest req = new ChatCompletionRequest(
                    convertToOpenAiMessages(request.getMessages()),  // 使用转换方法
                    request.getModelName(),
                    request.getTemperature(),
                    request.getStream()
            );
            if (request.getStream()) {
                return LLMUtils.callLLMStream(request.getProvider(), api, req)
                        .map(this::processData)
                        .concatWith(Flux.just(ServerSentEvent.builder("[DONE]").build()));
            }
            return LLMUtils.callLLM(request.getProvider(), api, req);
        } catch (Exception e) {
            log.error("创建聊天流失败", e);
            return Flux.error(e);
        }
    }

    private ServerSentEvent<String> processData(OpenAiApi.ChatCompletionChunk data) {
        return ServerSentEvent.<String>builder()
                .data(JsonUtils.dump(data))
                .build();
    }

    private List<OpenAiApi.ChatCompletionMessage> convertToOpenAiMessages(List<ChatMessage> messages) {
        return messages.stream()
                .map(msg -> new OpenAiApi.ChatCompletionMessage(
                        msg.content(),  // 消息内容
                        OpenAiApi.ChatCompletionMessage.Role.valueOf(msg.role().toUpperCase())  // 转换角色
                ))
                .collect(Collectors.toList());
    }

```

```java
@Data
public class LlmChatRequest {
        @NotBlank(message = "provider 不能为空")
        private String provider;
        
        @NotBlank(message = "modelName 不能为空")
        private String modelName;
        
        @NotEmpty(message = "messages 不能为空")
        private List<ChatMessage> messages;

        private Boolean stream = false;

        private Double temperature = 0.7;
}
```

