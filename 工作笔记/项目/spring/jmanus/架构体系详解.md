# Spring AI Alibaba JManus 架构体系详解

## 整体架构概览

Spring AI Alibaba JManus 采用了模块化、分层的架构设计，遵循了 OpenManus 多智能体框架的核心理念，同时结合了 Spring 生态系统的优势。整体架构可以分为以下几个关键层次：

```
+-------------------------------------------+
|              前端展示层 (UI)               |
+-------------------------------------------+
                    ↑↓
+-------------------------------------------+
|              控制器层 (Controller)         |
+-------------------------------------------+
                    ↑↓
+-------------------------------------------+
|              业务逻辑层                    |
| +---------------+  +-------------------+  |
| | 规划系统      |  | 智能体系统         |  |
| | (Planning)    |  | (Agent)           |  |
| +---------------+  +-------------------+  |
|                                           |
| +---------------+  +-------------------+  |
| | 工具系统      |  | 提示词系统         |  |
| | (Tool)        |  | (Prompt)          |  |
| +---------------+  +-------------------+  |
|                                           |
| +---------------+  +-------------------+  |
| | 记录系统      |  | 动态配置系统       |  |
| | (Recorder)    |  | (Dynamic Config)   |  |
| +---------------+  +-------------------+  |
+-------------------------------------------+
                    ↑↓
+-------------------------------------------+
|              数据持久层                    |
|        (H2 Database / JPA)                |
+-------------------------------------------+
                    ↑↓
+-------------------------------------------+
|              外部集成层                    |
| (AI Models, MCP Services, External Tools) |
+-------------------------------------------+
```

## 核心组件详解

### 1. 智能体系统 (Agent System)

智能体系统是整个框架的核心，负责执行具体的任务和推理过程。

#### 1.1 基础架构

智能体系统基于抽象类 `BaseAgent` 构建，提供了以下核心功能：

- **状态管理**：通过 `AgentState` 枚举管理智能体的生命周期状态（未启动、进行中、已完成、失败等）
- **步骤控制**：限制最大执行步骤数，防止无限循环
- **对话历史**：维护智能体的对话历史，用于上下文理解
- **卡住检测**：检测并处理智能体卡住的情况
- **环境数据**：维护和传递环境数据，用于智能体之间的通信

```java
public abstract class BaseAgent {
    // 智能体状态
    private AgentState state = AgentState.NOT_STARTED;
    
    // LLM 服务
    protected LlmService llmService;
    
    // 最大步骤数和当前步骤
    private int maxSteps;
    private int currentStep = 0;
    
    // 环境数据
    private Map<String, Object> envData = new HashMap<>();
    
    // 核心抽象方法
    public abstract String getName();
    public abstract String getDescription();
    protected abstract Message getThinkMessage();
    protected abstract Message getNextStepWithEnvMessage();
    protected abstract AgentExecResult step();
    public abstract List<ToolCallback> getToolCallList();
    
    // 运行方法
    public String run() {
        // 执行智能体逻辑
    }
}
```

#### 1.2 智能体实现

项目中实现了多种类型的智能体，主要包括：

- **ReActAgent**：实现推理和行动交替执行的智能体，基于 ReAct 模式
  ```java
  public class ReActAgent extends BaseAgent {
      @Override
      protected AgentExecResult step() {
          // 实现推理-行动循环
      }
  }
  ```

#### 1.3 智能体状态流转

智能体状态流转遵循以下模式：

```
NOT_STARTED → IN_PROGRESS → [FINISHED | FAILED | STUCK]
```

### 2. 规划系统 (Planning System)

规划系统负责任务的分解、规划和协调，是实现 Plan-Act 模式的关键组件。

#### 2.1 核心组件

规划系统由以下核心组件组成：

- **PlanningFactory**：负责创建和配置规划相关组件
  ```java
  @Service
  public class PlanningFactory {
      // 创建规划协调器
      public PlanningCoordinator createPlanningCoordinator(String planId) {
          // 创建计划创建器、执行器和终结器
          PlanCreator planCreator = new PlanCreator(...);
          PlanExecutor planExecutor = new PlanExecutor(...);
          PlanFinalizer planFinalizer = new PlanFinalizer(...);
          
          // 创建并返回协调器
          return new PlanningCoordinator(planCreator, planExecutor, planFinalizer);
      }
      
      // 创建工具回调映射
      public Map<String, ToolCallBackContext> toolCallbackMap(String planId) {
          // 配置各种工具回调
      }
  }
  ```

- **PlanCreator**：负责创建执行计划，将用户任务分解为可执行的子任务
  ```java
  public class PlanCreator {
      // 创建计划
      public Plan createPlan(String userInput) {
          // 使用 LLM 分解任务并创建计划
      }
  }
  ```

- **PlanExecutor**：负责执行计划，协调各个智能体完成任务
  ```java
  public class PlanExecutor {
      // 执行计划
      public PlanExecutionResult executePlan(Plan plan) {
          // 按顺序或并行执行计划中的任务
      }
  }
  ```

- **PlanFinalizer**：负责完成计划并处理结果
  ```java
  public class PlanFinalizer {
      // 完成计划
      public String finalizePlan(PlanExecutionResult result) {
          // 汇总结果并生成最终输出
      }
  }
  ```

- **PlanningCoordinator**：协调上述三个组件，完成整个规划-执行-完成流程
  ```java
  public class PlanningCoordinator {
      // 协调整个流程
      public String coordinate(String userInput) {
          // 创建计划
          Plan plan = planCreator.createPlan(userInput);
          
          // 执行计划
          PlanExecutionResult result = planExecutor.executePlan(plan);
          
          // 完成计划
          return planFinalizer.finalizePlan(result);
      }
  }
  ```

#### 2.2 计划模型

计划模型包含以下关键元素：

- **Plan**：整体计划，包含多个步骤
- **PlanStep**：计划中的单个步骤，包含执行该步骤的智能体信息和输入
- **PlanExecutionResult**：计划执行结果，包含每个步骤的执行结果

#### 2.3 Plan-Act 模式实现

Plan-Act 模式的实现流程：

1. **Plan 阶段**：使用 LLM 分析用户输入，创建执行计划
2. **Act 阶段**：按照计划执行各个步骤，每个步骤可能涉及不同的智能体
3. **结果整合**：汇总各个步骤的执行结果，生成最终输出

### 3. 工具系统 (Tool System)

工具系统提供了丰富的工具集，使智能体能够与外部世界交互。

#### 3.1 工具抽象

工具系统基于 `ToolCallBiFunctionDef` 接口构建：

```java
public interface ToolCallBiFunctionDef extends BiFunction<Map<String, Object>, String, ToolExecuteResult> {
    String getName();
    String getDescription();
    Map<String, Object> getParameters();
    Class<?> getInputType();
    boolean isReturnDirect();
    void setPlanId(String planId);
}
```

#### 3.2 主要工具实现

项目实现了多种工具，包括：

- **BrowserUseTool**：浏览器操作工具，支持网页浏览、点击、表单填写等操作
  ```java
  public class BrowserUseTool implements ToolCallBiFunctionDef {
      @Override
      public ToolExecuteResult apply(Map<String, Object> params, String planId) {
          // 执行浏览器操作
      }
  }
  ```

- **Bash**：终端命令执行工具，支持执行系统命令
  ```java
  public class Bash implements ToolCallBiFunctionDef {
      @Override
      public ToolExecuteResult apply(Map<String, Object> params, String planId) {
          // 执行终端命令
      }
  }
  ```

- **DocLoaderTool**：文档加载工具，支持加载和解析各种文档
- **TextFileOperator**：文本文件操作工具，支持读写文本文件
- **GoogleSearch**：搜索工具，支持网络搜索
- **PythonExecute**：Python 代码执行工具，支持执行 Python 代码
- **FormInputTool**：表单输入工具，支持填写表单
- **McpTool**：MCP 工具，支持调用 MCP 服务

#### 3.3 工具注册与调用

工具的注册与调用流程：

1. **工具注册**：在 `PlanningFactory` 中注册各种工具
   ```java
   toolDefinitions.add(BrowserUseTool.getInstance(chromeDriverService));
   toolDefinitions.add(new TerminateTool(planId));
   toolDefinitions.add(new Bash(manusProperties));
   // 注册更多工具...
   ```

2. **工具回调创建**：为每个工具创建 `FunctionToolCallback`
   ```java
   FunctionToolCallback<?, ToolExecuteResult> functionToolcallback = FunctionToolCallback
       .builder(toolDefinition.getName(), toolDefinition)
       .description(toolDefinition.getDescription())
       .inputSchema(toolDefinition.getParameters())
       .inputType(toolDefinition.getInputType())
       .toolMetadata(ToolMetadata.builder().returnDirect(toolDefinition.isReturnDirect()).build())
       .build();
   ```

3. **工具调用**：智能体通过 LLM 决定调用哪个工具，并提供参数
   ```java
   ToolCallback toolCallback = getToolCallList().get(toolName);
   ToolExecuteResult result = toolCallback.call(params, planId);
   ```

### 4. 提示词系统 (Prompt System)

提示词系统负责管理和加载提示词模板，为智能体提供上下文和指导。

#### 4.1 核心组件

提示词系统的核心是 `PromptLoader` 类：

```java
public class PromptLoader {
    // 从文件加载提示词模板
    public String loadPromptTemplate(String templatePath) {
        // 加载模板文件
    }
    
    // 创建系统消息
    public Message createSystemMessage(String templatePath, Map<String, Object> variables) {
        // 加载模板并替换变量
        String template = loadPromptTemplate(templatePath);
        String content = replaceVariables(template, variables);
        return new SystemMessage(content);
    }
    
    // 替换变量
    private String replaceVariables(String template, Map<String, Object> variables) {
        // 替换模板中的变量
    }
}
```

#### 4.2 提示词模板

提示词模板存储在 `src/main/resources/prompts` 目录下，按功能分类：

- **agent/**：智能体相关的提示词模板
  - `step-execution.txt`：执行步骤的提示词
  - 其他智能体特定的提示词

- **planning/**：规划相关的提示词模板
  - `plan-creation.txt`：计划创建的提示词
  - `plan-finalization.txt`：计划完成的提示词

#### 4.3 提示词变量替换

提示词系统支持变量替换，使提示词能够根据上下文动态生成：

```java
Map<String, Object> variables = new HashMap<>();
variables.put("osName", System.getProperty("os.name"));
variables.put("currentDateTime", java.time.LocalDate.now().toString());
// 添加更多变量...

Message systemMessage = promptLoader.createSystemMessage("agent/step-execution.txt", variables);
```

### 5. 记录系统 (Recorder System)

记录系统负责记录计划执行的过程和结果，支持持久化存储。

#### 5.1 核心组件

记录系统的核心是 `PlanExecutionRecorder` 类：

```java
public class PlanExecutionRecorder {
    // 记录计划执行
    public void recordPlanExecution(String planId, String userInput) {
        // 创建并保存计划执行记录
    }
    
    // 记录智能体执行
    public void recordAgentExecution(AgentExecutionRecord agentRecord) {
        // 保存智能体执行记录
    }
    
    // 记录步骤执行
    public void recordStepExecution(String planId, String agentName, int stepNumber, String input, String output) {
        // 保存步骤执行记录
    }
}
```

#### 5.2 实体模型

记录系统使用以下实体模型：

- **PlanExecutionRecord**：计划执行记录，包含计划 ID、用户输入、开始时间、结束时间等
- **AgentExecutionRecord**：智能体执行记录，包含智能体名称、状态、步骤数等
- **StepExecutionRecord**：步骤执行记录，包含步骤编号、输入、输出等

#### 5.3 持久化存储

记录系统使用 JPA 和 H2 数据库进行持久化存储：

```java
@Repository
public interface PlanExecutionRepository extends JpaRepository<PlanExecutionRecord, String> {
    // 查询方法
}

@Repository
public interface AgentExecutionRepository extends JpaRepository<AgentExecutionRecord, Long> {
    // 查询方法
}

@Repository
public interface StepExecutionRepository extends JpaRepository<StepExecutionRecord, Long> {
    // 查询方法
}
```

### 6. 动态配置系统 (Dynamic Configuration)

动态配置系统支持动态加载和配置智能体和 MCP 服务，无需修改代码。

#### 6.1 智能体动态配置

智能体动态配置由 `DynamicAgentLoader` 和 `AgentService` 管理：

```java
public class DynamicAgentLoader {
    // 加载所有智能体
    public List<DynamicAgentEntity> getAllAgents() {
        // 从数据库加载智能体配置
    }
    
    // 创建智能体
    public BaseAgent createAgent(DynamicAgentEntity agentEntity, String planId) {
        // 根据配置创建智能体
    }
}
```

#### 6.2 MCP 服务动态配置

MCP 服务动态配置由 `McpService` 和 `McpStateHolderService` 管理：

```java
public class McpService {
    // 获取函数回调
    public List<McpServiceEntity> getFunctionCallbacks(String planId) {
        // 获取 MCP 服务配置
    }
}
```

#### 6.3 配置实体

动态配置系统使用以下实体：

- **DynamicAgentEntity**：智能体配置实体，包含智能体名称、描述、提示词等
- **McpServiceEntity**：MCP 服务配置实体，包含服务名称、URL、工具列表等

## 系统流程

### 1. 用户请求处理流程

```
用户请求 → 控制器 → PlanningCoordinator → PlanCreator → PlanExecutor → 智能体执行 → PlanFinalizer → 响应
```

### 2. 智能体执行流程

```
初始化 → 获取思考消息 → 获取下一步消息 → 执行步骤 → 处理工具调用 → 更新状态 → 重复或完成
```

### 3. 工具调用流程

```
智能体决策 → 选择工具 → 准备参数 → 调用工具 → 处理结果 → 返回智能体
```

## 扩展机制

### 1. 添加新智能体

要添加新的智能体，需要：

1. 创建新的智能体类，继承 `BaseAgent`
2. 实现抽象方法
3. 在 `DynamicAgentLoader` 中注册新智能体
4. 或通过 Web 界面动态配置

### 2. 添加新工具

要添加新的工具，需要：

1. 创建新的工具类，实现 `ToolCallBiFunctionDef` 接口
2. 在 `PlanningFactory` 中注册新工具
3. 创建相应的工具回调

### 3. 集成新的 AI 模型

要集成新的 AI 模型，需要：

1. 在 `application.yml` 中配置新模型
2. 或实现自定义的 `LlmService` 适配器

## 总结

Spring AI Alibaba JManus 的架构设计遵循了模块化、可扩展的原则，通过分层和组件化实现了灵活且强大的多智能体系统。核心的智能体系统、规划系统、工具系统、提示词系统、记录系统和动态配置系统相互协作，共同支撑了 Plan-Act 模式和无限上下文处理等高级功能。

该架构不仅支持现有功能的实现，还提供了良好的扩展机制，使开发者能够轻松添加新的智能体、工具和模型，以满足不断变化的业务需求。 