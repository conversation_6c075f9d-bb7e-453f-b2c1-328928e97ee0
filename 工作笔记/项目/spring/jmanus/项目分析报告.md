# Spring AI Alibaba JManus 项目分析报告

## 项目概述

Spring AI Alibaba JManus 是 [OpenManus](https://github.com/FoundationAgents/OpenManus) 多智能体框架的 Java 实现版本，基于 Spring AI 构建。该项目旨在为开发者提供一个强大、可靠且易于配置的多智能体系统框架，支持企业级的可靠性和可扩展性。

### 核心特点

- **原生多智能体架构**：内置协作框架，支持用户自定义智能体能力和专业角色
- **无限上下文处理**：通过智能多智能体协调，克服单一模型上下文限制，实现无限内容处理
- **Plan-Act 模式**：完整实现计划-行动范式，智能地分离规划和执行过程
- **MCP 集成**：原生支持 Model Context Protocol，实现与外部服务和工具的无缝集成

## 新手入门指南

如果您是第一次接触 Spring AI Alibaba JManus 项目，以下是推荐的学习路径：

### 1. 环境搭建与运行

首先，按照以下步骤搭建环境并运行项目：

1. **安装必要环境**：
   - 安装 JDK 17 或更高版本
   - 全局安装 NPX：`npm install -g npx`
   - 获取 [DashScope API Key](https://help.aliyun.com/zh/model-studio/getting-started/first-api-call-to-qwen)（阿里云百炼API）

2. **设置环境变量**：
   ```shell
   export DASHSCOPE_API_KEY=your_api_key_here
   ```

3. **运行项目**：
   ```shell
   # Unix-like 系统 (macOS, Linux)
   ./mvnw spring-boot:run
   
   # Windows 系统
   ./mvnw.cmd spring-boot:run
   ```

4. **访问界面**：
   浏览器打开 `http://localhost:18080`

### 2. 理解核心概念

在运行项目后，建议了解以下核心概念：

1. **多智能体框架**：理解 OpenManus 的基本架构和多智能体协作模式
2. **Plan-Act 模式**：了解计划与执行分离的工作流程
3. **MCP (Model Context Protocol)**：了解如何通过 MCP 集成外部服务和工具

### 3. 探索代码结构

按照以下顺序探索代码结构：

1. **主应用程序入口**：
   - `src/main/java/com/alibaba/cloud/ai/example/manus/OpenManusSpringBootApplication.java`

2. **智能体系统**：
   - `src/main/java/com/alibaba/cloud/ai/example/manus/agent/BaseAgent.java`
   - `src/main/java/com/alibaba/cloud/ai/example/manus/agent/ReActAgent.java`

3. **规划系统**：
   - `src/main/java/com/alibaba/cloud/ai/example/manus/planning/PlanningFactory.java`
   - 探索 `planning` 包下的其他组件

4. **工具系统**：
   - 探索 `src/main/java/com/alibaba/cloud/ai/example/manus/tool` 目录下的各种工具实现

5. **配置文件**：
   - `src/main/resources/application.yml`

### 4. 尝试简单修改

熟悉代码结构后，可以尝试：

1. **修改配置参数**：调整 `application.yml` 中的参数
2. **添加新的提示词模板**：在 `src/main/resources/prompts` 目录下添加新的提示词模板
3. **尝试使用不同的 AI 模型**：修改配置使用其他支持的 AI 模型

### 5. 学习资源

- **项目文档**：阅读 `README.md` 和 `README-zh.md`
- **示例代码**：研究 `src/main/java/com/alibaba/cloud/ai/example` 目录下的示例
- **前端界面**：了解 `ui-vue3` 目录下的前端实现

### 6. 进阶学习

掌握基础后，可以进一步：

1. **研究动态配置系统**：了解如何动态加载和配置智能体
2. **探索 MCP 集成**：深入研究 MCP 的实现和使用方法
3. **尝试扩展功能**：参考 RoadMap 中的规划，尝试实现或改进某个功能

## 技术栈

- **后端**：
  - Java 17+
  - Spring Boot 3.4.5
  - Spring AI 1.0.0
  - JPA/Hibernate
  - H2 数据库
  - Apache HttpClient 5

- **前端**：
  - Vue 3
  - TypeScript
  - Vite

- **AI 模型集成**：
  - 默认使用 DashScope API (阿里云百炼)
  - 支持其他 AI 模型平台配置

## 项目架构

项目采用了模块化的架构设计，主要包含以下核心组件：

### 1. 智能体系统 (Agent System)

基于 `BaseAgent` 抽象类实现的智能体系统，支持：
- 状态管理（空闲、运行中、已完成）
- 对话跟踪
- 步骤限制和监控
- 线程安全执行
- 卡住状态检测和处理

主要智能体实现包括：
- `ReActAgent`：实现推理和行动交替执行的智能体

### 2. 规划系统 (Planning System)

由 `PlanningFactory` 管理的规划系统，包含：
- `PlanCreator`：负责创建执行计划
- `PlanExecutor`：负责执行计划
- `PlanFinalizer`：负责完成计划并处理结果
- `PlanningCoordinator`：协调以上三个组件

### 3. 工具系统 (Tool System)

丰富的工具集成，包括：
- 浏览器操作工具 (`BrowserUseTool`)
- 终端命令执行工具 (`Bash`)
- 文档加载工具 (`DocLoaderTool`)
- 文本文件操作工具 (`TextFileOperator`)
- 搜索工具 (`GoogleSearch`)
- Python 代码执行工具 (`PythonExecute`)
- 表单输入工具 (`FormInputTool`)
- MCP 工具集成

### 4. 提示词系统 (Prompt System)

通过 `PromptLoader` 管理的提示词系统，支持从文件加载提示词模板并进行变量替换。

### 5. 记录系统 (Recorder System)

`PlanExecutionRecorder` 负责记录计划执行的过程和结果，支持持久化存储。

### 6. 动态配置系统 (Dynamic Configuration)

支持动态加载和配置智能体和 MCP 服务。

## 功能特点

1. **完整实现 OpenManus 多智能体框架**：使用 Spring AI 和 Java 全面实现 OpenManus 架构
2. **通过网页界面配置智能体**：通过直观的网页管理界面轻松配置智能体，无需修改代码
3. **MCP (Model Context Protocol) 接入智能体**：无缝集成 Model Context Protocol，使智能体能够与各种模型和服务交互
4. **支持 PLAN-ACT 模式**：实现强大的 PLAN-ACT 模式，支持复杂的推理和执行工作流

## 应用场景

- **客户体验**：自动化多层次客户支持，智能升级和解决问题
- **数据智能**：复杂 ETL 管道，AI 驱动的数据处理和质量保证
- **研究与分析**：自动化信息发现、综合和报告生成
- **业务自动化**：跨多样化企业系统的端到端工作流程编排
- **教育技术**：具有个性化内容生成的交互式学习环境
- **质量保证**：具有智能验证和报告的全面自动化测试工作流程

## 未来规划

根据项目的 RoadMap，未来计划实现的功能包括：

1. **浏览器资源优化**：确保浏览器在 Java 进程结束后可以正常关闭
2. **浏览器的视觉识别**：浏览器能够截图，并通过截图理解和点击图标
3. **前端 JSX 支持**：支持 JSX、文件夹的前端生成
4. **Shell 工具的多 OS 验证与优化**：确保能够在 Mac、Windows、Linux 上执行各种命令
5. **剪贴板实现**：使用类似管道的方式，高效地在不同位置间复制内容
6. **执行器支持 case when 逻辑**：支持流程条件选择和分支
7. **支持 Excel、Word、PowerPoint 等办公工具**：扩展对办公软件的支持
8. **无限上下文**：实现真正的无限上下文支持

## 运行环境要求

- **Java 17+**（推荐 OpenJDK）
- **DashScope API Key**（或其他 AI 模型提供商）
- **NPX**（全局安装）

## 结论

Spring AI Alibaba JManus 是一个功能强大、架构清晰的多智能体框架实现，它通过 Spring AI 和 Java 生态系统提供了企业级的可靠性和可扩展性。该项目不仅实现了 OpenManus 的核心功能，还添加了许多实用的扩展和工具，使其能够应用于各种复杂的业务场景。

随着项目的不断发展和完善，特别是在无限上下文、多模型支持等方面的进一步优化，Spring AI Alibaba JManus 有望成为企业级 AI 智能体系统的重要解决方案。 