# Spring AI Alibaba JManus 用户交互系统详解

## 用户交互系统概述

Spring AI Alibaba JManus 提供了丰富的用户交互机制，使用户能够与多智能体系统进行有效沟通。用户交互系统是连接用户与智能体系统的桥梁，负责接收用户输入、展示执行过程和结果，以及收集用户反馈。

整个用户交互系统主要包含以下几个部分：

1. **Web 用户界面**：基于 Vue 3 开发的前端界面
2. **交互控制器**：处理用户请求的后端控制器
3. **表单交互工具**：支持动态表单输入的工具
4. **执行过程可视化**：展示智能体执行过程的组件
5. **反馈收集机制**：收集用户反馈的组件

## 1. Web 用户界面

JManus 的 Web 用户界面基于 Vue 3 和 TypeScript 开发，提供了直观、友好的交互体验。

### 1.1 界面结构

Web 界面主要包含以下几个部分：

- **对话区域**：用户输入和系统响应的主要区域
- **智能体配置面板**：配置和管理智能体的面板
- **执行过程展示区**：展示智能体执行过程的区域
- **工具配置面板**：配置和管理工具的面板
- **设置面板**：系统设置面板

### 1.2 核心组件

Web 界面的核心组件包括：

- **ChatView**：主要对话界面组件
- **AgentConfigPanel**：智能体配置面板组件
- **ExecutionViewer**：执行过程查看器组件
- **ToolConfigPanel**：工具配置面板组件
- **SettingsPanel**：设置面板组件

### 1.3 交互流程

用户通过 Web 界面与系统交互的典型流程如下：

1. 用户在对话输入框中输入请求
2. 系统接收请求并创建执行计划
3. 系统执行计划，并实时展示执行过程
4. 系统返回最终结果
5. 用户可以查看详细的执行记录，或提供反馈

## 2. 交互控制器

交互控制器是处理用户请求的后端组件，负责接收用户输入、调用相应的业务逻辑，并返回结果。

### 2.1 主要控制器

JManus 中与用户交互相关的主要控制器包括：

- **PlanningController**：处理规划相关请求的控制器
- **AgentController**：处理智能体相关请求的控制器
- **ToolController**：处理工具相关请求的控制器

### 2.2 PlanningController

`PlanningController` 是处理用户规划请求的主要控制器，其核心实现如下：

```java
@RestController
@RequestMapping("/api/planning")
public class PlanningController {

    private final PlanningService planningService;

    public PlanningController(PlanningService planningService) {
        this.planningService = planningService;
    }

    // 创建并执行计划
    @PostMapping("/execute")
    public ResponseEntity<PlanExecutionResponse> executePlan(@RequestBody PlanExecutionRequest request) {
        String planId = UUID.randomUUID().toString();
        String result = planningService.executePlan(planId, request.getUserInput());
        return ResponseEntity.ok(new PlanExecutionResponse(planId, result));
    }

    // 获取执行记录
    @GetMapping("/{planId}/records")
    public ResponseEntity<List<ExecutionRecordDTO>> getExecutionRecords(@PathVariable String planId) {
        List<ExecutionRecordDTO> records = planningService.getExecutionRecords(planId);
        return ResponseEntity.ok(records);
    }

    // 取消执行
    @PostMapping("/{planId}/cancel")
    public ResponseEntity<Void> cancelExecution(@PathVariable String planId) {
        planningService.cancelExecution(planId);
        return ResponseEntity.ok().build();
    }
}
```

### 2.3 AgentController

`AgentController` 负责处理智能体相关的请求，包括获取、创建、更新和删除智能体配置：

```java
@RestController
@RequestMapping("/api/agents")
public class AgentController {

    private final AgentService agentService;

    public AgentController(AgentService agentService) {
        this.agentService = agentService;
    }

    // 获取所有智能体
    @GetMapping
    public ResponseEntity<List<AgentDTO>> getAllAgents() {
        List<AgentDTO> agents = agentService.getAllAgents();
        return ResponseEntity.ok(agents);
    }

    // 创建智能体
    @PostMapping
    public ResponseEntity<AgentDTO> createAgent(@RequestBody AgentCreateRequest request) {
        AgentDTO agent = agentService.createAgent(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(agent);
    }

    // 更新智能体
    @PutMapping("/{agentId}")
    public ResponseEntity<AgentDTO> updateAgent(
            @PathVariable String agentId,
            @RequestBody AgentUpdateRequest request) {
        AgentDTO agent = agentService.updateAgent(agentId, request);
        return ResponseEntity.ok(agent);
    }

    // 删除智能体
    @DeleteMapping("/{agentId}")
    public ResponseEntity<Void> deleteAgent(@PathVariable String agentId) {
        agentService.deleteAgent(agentId);
        return ResponseEntity.noContent().build();
    }
}
```

## 3. 表单交互工具

JManus 提供了表单交互工具 `FormInputTool`，使智能体能够向用户请求额外的输入信息。

### 3.1 FormInputTool 实现

`FormInputTool` 的核心实现如下：

```java
public class FormInputTool implements ToolCallBiFunctionDef {

    @Override
    public String getName() {
        return "form_input";
    }

    @Override
    public String getDescription() {
        return "Request additional input from the user through a form interface";
    }

    @Override
    public Map<String, Object> getParameters() {
        Map<String, Object> params = new HashMap<>();
        
        Map<String, Object> formFields = new HashMap<>();
        formFields.put("type", "object");
        formFields.put("description", "The form fields to request from the user");
        
        Map<String, Object> message = new HashMap<>();
        message.put("type", "string");
        message.put("description", "Message to display to the user explaining what information is needed");
        
        params.put("form_fields", formFields);
        params.put("message", message);
        
        return params;
    }

    @Override
    public ToolExecuteResult apply(Map<String, Object> params, String planId) {
        // 获取表单字段和消息
        Map<String, Object> formFields = (Map<String, Object>) params.get("form_fields");
        String message = (String) params.get("message");
        
        // 创建表单请求
        FormRequest formRequest = new FormRequest(planId, formFields, message);
        
        // 保存表单请求
        FormRequestRepository.saveRequest(planId, formRequest);
        
        // 等待用户填写表单
        FormResponse formResponse = FormRequestRepository.waitForResponse(planId);
        
        // 返回用户填写的表单数据
        return new ToolExecuteResult(
            true,
            "User provided form data: " + formResponse.getData(),
            formResponse.getData()
        );
    }
}
```

### 3.2 表单交互流程

表单交互的典型流程如下：

1. 智能体在执行过程中需要额外信息
2. 智能体调用 `FormInputTool` 创建表单请求
3. 系统向用户展示表单
4. 用户填写表单并提交
5. 系统将用户输入传回智能体
6. 智能体继续执行

### 3.3 表单控制器

处理表单请求和响应的控制器实现如下：

```java
@RestController
@RequestMapping("/api/forms")
public class FormController {

    // 获取待处理的表单请求
    @GetMapping("/pending/{planId}")
    public ResponseEntity<FormRequest> getPendingForm(@PathVariable String planId) {
        FormRequest request = FormRequestRepository.getRequest(planId);
        if (request == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(request);
    }

    // 提交表单响应
    @PostMapping("/submit/{planId}")
    public ResponseEntity<Void> submitForm(
            @PathVariable String planId,
            @RequestBody Map<String, Object> formData) {
        FormResponse response = new FormResponse(planId, formData);
        FormRequestRepository.saveResponse(planId, response);
        return ResponseEntity.ok().build();
    }
}
```

## 4. 执行过程可视化

JManus 提供了丰富的执行过程可视化功能，使用户能够实时了解智能体的执行状态和进度。

### 4.1 执行记录模型

执行记录包含以下几个层次：

- **计划执行记录**：整个计划的执行记录
- **智能体执行记录**：每个智能体的执行记录
- **步骤执行记录**：每个步骤的执行记录

### 4.2 执行过程展示组件

前端实现了多个组件来展示执行过程：

- **ExecutionTimeline**：展示执行时间线
- **AgentExecutionCard**：展示智能体执行卡片
- **StepExecutionList**：展示步骤执行列表
- **ToolCallView**：展示工具调用详情

### 4.3 实时更新机制

JManus 使用 WebSocket 或 Server-Sent Events (SSE) 实现执行过程的实时更新：

```java
@Controller
public class ExecutionUpdateController {

    private final SseEmitter.Timeout timeout = SseEmitter.event()
            .timeout(Long.MAX_VALUE);

    private final Map<String, List<SseEmitter>> emitters = new ConcurrentHashMap<>();

    // 订阅执行更新
    @GetMapping("/api/execution/{planId}/updates")
    public SseEmitter subscribeToUpdates(@PathVariable String planId) {
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        
        // 将发射器添加到计划的发射器列表中
        emitters.computeIfAbsent(planId, k -> new CopyOnWriteArrayList<>())
                .add(emitter);
        
        // 设置完成回调
        emitter.onCompletion(() -> {
            List<SseEmitter> planEmitters = emitters.get(planId);
            if (planEmitters != null) {
                planEmitters.remove(emitter);
            }
        });
        
        // 设置超时回调
        emitter.onTimeout(emitter::complete);
        
        return emitter;
    }

    // 发送执行更新
    public void sendUpdate(String planId, ExecutionUpdate update) {
        List<SseEmitter> planEmitters = emitters.get(planId);
        if (planEmitters != null) {
            List<SseEmitter> deadEmitters = new ArrayList<>();
            
            for (SseEmitter emitter : planEmitters) {
                try {
                    emitter.send(update, MediaType.APPLICATION_JSON);
                } catch (Exception e) {
                    deadEmitters.add(emitter);
                }
            }
            
            // 移除死亡的发射器
            planEmitters.removeAll(deadEmitters);
        }
    }
}
```

## 5. 反馈收集机制

JManus 实现了反馈收集机制，使用户能够对智能体的执行结果提供反馈，帮助系统改进。

### 5.1 反馈模型

反馈模型包含以下字段：

- **planId**：关联的计划 ID
- **rating**：评分（1-5）
- **comment**：评论文本
- **feedbackType**：反馈类型（如 "RESULT_QUALITY", "EXECUTION_EFFICIENCY" 等）

### 5.2 反馈控制器

处理反馈的控制器实现如下：

```java
@RestController
@RequestMapping("/api/feedback")
public class FeedbackController {

    private final FeedbackService feedbackService;

    public FeedbackController(FeedbackService feedbackService) {
        this.feedbackService = feedbackService;
    }

    // 提交反馈
    @PostMapping
    public ResponseEntity<FeedbackDTO> submitFeedback(@RequestBody FeedbackRequest request) {
        FeedbackDTO feedback = feedbackService.saveFeedback(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(feedback);
    }

    // 获取计划的反馈
    @GetMapping("/plan/{planId}")
    public ResponseEntity<List<FeedbackDTO>> getPlanFeedback(@PathVariable String planId) {
        List<FeedbackDTO> feedbackList = feedbackService.getFeedbackByPlanId(planId);
        return ResponseEntity.ok(feedbackList);
    }
}
```

### 5.3 反馈组件

前端实现了反馈组件，使用户能够方便地提供反馈：

- **FeedbackForm**：反馈表单组件
- **RatingStars**：评分星级组件
- **FeedbackTypeSelector**：反馈类型选择器组件

## 6. MCP (Model Context Protocol) 交互

JManus 支持 MCP (Model Context Protocol) 交互，使智能体能够与外部服务和工具进行交互。

### 6.1 MCP 集成

MCP 集成通过以下组件实现：

- **McpService**：管理 MCP 服务的服务类
- **McpStateHolderService**：管理 MCP 状态的服务类
- **McpTool**：封装 MCP 工具调用的工具类

### 6.2 MCP 交互流程

MCP 交互的典型流程如下：

1. 系统配置 MCP 服务
2. 智能体在执行过程中调用 MCP 工具
3. MCP 工具向外部服务发送请求
4. 外部服务执行操作并返回结果
5. MCP 工具将结果传回智能体
6. 智能体继续执行

### 6.3 MCP 配置界面

JManus 提供了 MCP 配置界面，使用户能够方便地配置 MCP 服务：

- **McpServiceList**：MCP 服务列表组件
- **McpServiceEditor**：MCP 服务编辑器组件
- **McpToolList**：MCP 工具列表组件

## 7. 用户交互最佳实践

在 JManus 中实现有效的用户交互，可以考虑以下最佳实践：

### 7.1 提示词设计

- **清晰明确**：提示词应清晰明确，避免歧义
- **引导性**：提示词应引导用户提供必要的信息
- **示例**：提供示例，帮助用户理解如何与系统交互

### 7.2 错误处理

- **友好的错误提示**：提供友好、易于理解的错误提示
- **恢复机制**：实现错误恢复机制，使用户能够从错误中恢复
- **引导性建议**：提供引导性建议，帮助用户解决问题

### 7.3 执行透明度

- **实时更新**：提供执行过程的实时更新
- **详细日志**：记录详细的执行日志，便于调试和理解
- **可视化**：使用可视化手段展示执行过程和结果

### 7.4 用户反馈

- **及时收集**：及时收集用户反馈
- **有效利用**：有效利用用户反馈改进系统
- **闭环机制**：实现反馈闭环机制，使用户能够看到反馈的效果

## 总结

Spring AI Alibaba JManus 提供了丰富的用户交互机制，包括 Web 用户界面、交互控制器、表单交互工具、执行过程可视化、反馈收集机制和 MCP 交互等。这些机制共同构成了一个完整的用户交互系统，使用户能够与多智能体系统进行有效沟通。

通过合理设计提示词、处理错误、提高执行透明度和收集用户反馈，JManus 能够提供良好的用户体验，帮助用户更好地利用多智能体系统解决复杂问题。 