# Spring AI Alibaba JManus 任务处理流程详解

## 任务处理概述

在 Spring AI Alibaba JManus 中，一个用户任务从提交到完成，会经历一系列处理步骤，涉及多个组件的协作。本文将详细介绍整个任务处理流程，包括接口调用、内部处理和结果返回等环节。

## 完整流程图

以下是一个任务在 JManus 中的完整处理流程：

```
+------------------+    1. 提交任务    +------------------+
|                  |------------------>|                  |
|      用户        |                   |   Web 控制器     |
|                  |<------------------|                  |
+------------------+    8. 返回结果    +------------------+
                                |
                                | 2. 创建计划
                                v
                        +------------------+
                        |                  |
                        |   规划协调器     |
                        |                  |
                        +------------------+
                                |
                                | 3. 分解任务
                                v
                        +------------------+
                        |                  |
                        |    计划创建器    |
                        |                  |
                        +------------------+
                                |
                                | 4. 执行计划
                                v
                        +------------------+
                        |                  |
                        |    计划执行器    |
                        |                  |
                        +------------------+
                                |
                                | 5. 执行步骤
                                v
                        +------------------+
                        |                  |
                        |     智能体       |<------+
                        |                  |       |
                        +------------------+       |
                                |                  |
                                | 6. 工具调用      | 循环执行
                                v                  |
                        +------------------+       |
                        |                  |       |
                        |      工具        |-------+
                        |                  |
                        +------------------+
                                |
                                | 7. 整合结果
                                v
                        +------------------+
                        |                  |
                        |   计划终结器     |
                        |                  |
                        +------------------+
```

## 详细流程解析

### 1. 任务提交阶段

#### 1.1 用户提交任务

用户通过前端界面或 API 提交任务，典型的 API 请求如下：

```http
POST /api/planning/execute
Content-Type: application/json

{
  "userInput": "帮我分析最近一个月的销售数据并生成报告",
  "options": {
    "maxSteps": 20,
    "timeout": 300000
  }
}
```

#### 1.2 控制器接收请求

`PlanningController` 接收用户请求，并生成唯一的计划 ID：

```java
@PostMapping("/execute")
public ResponseEntity<PlanExecutionResponse> executePlan(@RequestBody PlanExecutionRequest request) {
    // 生成唯一的计划 ID
    String planId = UUID.randomUUID().toString();
    
    // 异步执行计划
    CompletableFuture.runAsync(() -> {
        planningService.executePlan(planId, request.getUserInput(), request.getOptions());
    });
    
    // 立即返回计划 ID，让用户可以通过 ID 跟踪执行进度
    return ResponseEntity.ok(new PlanExecutionResponse(planId, null));
}
```

### 2. 规划协调阶段

#### 2.1 创建规划协调器

`PlanningService` 调用 `PlanningFactory` 创建规划协调器：

```java
public void executePlan(String planId, String userInput, PlanOptions options) {
    try {
        // 创建规划协调器
        PlanningCoordinator coordinator = planningFactory.createPlanningCoordinator(planId);
        
        // 记录计划开始执行
        planExecutionRecorder.recordPlanExecution(planId, userInput);
        
        // 调用协调器执行计划
        String result = coordinator.coordinate(userInput, options);
        
        // 更新计划执行记录
        planExecutionRecorder.updatePlanExecutionResult(planId, result);
        
        // 发送执行完成事件
        eventPublisher.publishEvent(new PlanExecutionCompletedEvent(planId, result));
    } 
    catch (Exception e) {
        // 处理异常
        planExecutionRecorder.updatePlanExecutionError(planId, e.getMessage());
        eventPublisher.publishEvent(new PlanExecutionFailedEvent(planId, e));
    }
}
```

#### 2.2 协调器初始化

`PlanningCoordinator` 初始化各个组件：

```java
public PlanningCoordinator(PlanCreator planCreator, PlanExecutor planExecutor, PlanFinalizer planFinalizer) {
    this.planCreator = planCreator;
    this.planExecutor = planExecutor;
    this.planFinalizer = planFinalizer;
}
```

### 3. 计划创建阶段

#### 3.1 分析用户输入

`PlanCreator` 使用 LLM 分析用户输入，确定解决方案：

```java
public Plan createPlan(String userInput) {
    // 构建提示词
    Message systemMessage = promptLoader.createSystemMessage("planning/plan-creation.txt", Collections.emptyMap());
    
    // 构建用户消息
    Map<String, Object> variables = new HashMap<>();
    variables.put("userInput", userInput);
    Message userMessage = promptLoader.createUserMessage("planning/user-input.txt", variables);
    
    // 调用 LLM 分析用户输入
    List<Message> messages = Arrays.asList(systemMessage, userMessage);
    Message response = llmService.callLlm(messages);
    
    // 解析 LLM 响应，创建计划
    Plan plan = parsePlanFromLlmResponse(response.getContent());
    
    return plan;
}
```

#### 3.2 创建执行计划

`PlanCreator` 将用户任务分解为多个步骤，并为每个步骤分配合适的智能体：

```java
private Plan parsePlanFromLlmResponse(String llmResponse) {
    // 解析 LLM 响应
    JsonObject jsonResponse = JsonParser.parseString(llmResponse).getAsJsonObject();
    
    // 创建计划对象
    Plan plan = new Plan();
    
    // 设置计划标题和描述
    plan.setTitle(jsonResponse.get("title").getAsString());
    plan.setDescription(jsonResponse.get("description").getAsString());
    
    // 解析步骤
    JsonArray stepsArray = jsonResponse.getAsJsonArray("steps");
    List<PlanStep> steps = new ArrayList<>();
    
    for (int i = 0; i < stepsArray.size(); i++) {
        JsonObject stepObject = stepsArray.get(i).getAsJsonObject();
        
        PlanStep step = new PlanStep();
        step.setStepNumber(i + 1);
        step.setDescription(stepObject.get("description").getAsString());
        step.setAgentType(stepObject.get("agentType").getAsString());
        step.setInput(stepObject.get("input").getAsString());
        
        steps.add(step);
    }
    
    plan.setSteps(steps);
    
    return plan;
}
```

### 4. 计划执行阶段

#### 4.1 初始化执行

`PlanExecutor` 开始执行计划：

```java
public PlanExecutionResult executePlan(Plan plan) {
    // 创建执行结果对象
    PlanExecutionResult result = new PlanExecutionResult();
    result.setPlan(plan);
    
    // 存储每个步骤的执行结果
    Map<Integer, StepExecutionResult> stepResults = new HashMap<>();
    
    // 执行每个步骤
    for (PlanStep step : plan.getSteps()) {
        StepExecutionResult stepResult = executeStep(step);
        stepResults.put(step.getStepNumber(), stepResult);
        
        // 如果步骤执行失败，停止执行
        if (!stepResult.isSuccess()) {
            result.setSuccess(false);
            result.setErrorMessage("Step " + step.getStepNumber() + " failed: " + stepResult.getErrorMessage());
            break;
        }
    }
    
    result.setStepResults(stepResults);
    result.setSuccess(true);
    
    return result;
}
```

#### 4.2 执行单个步骤

`PlanExecutor` 为每个步骤创建对应的智能体，并执行：

```java
private StepExecutionResult executeStep(PlanStep step) {
    // 创建步骤执行结果
    StepExecutionResult result = new StepExecutionResult();
    
    try {
        // 根据步骤类型创建智能体
        BaseAgent agent = createAgent(step.getAgentType(), step.getInput());
        
        // 设置计划 ID
        agent.setPlanId(plan.getPlanId());
        
        // 设置智能体状态为进行中
        agent.setState(AgentState.IN_PROGRESS);
        
        // 执行智能体
        String stepOutput = agent.run();
        
        // 设置步骤执行结果
        result.setSuccess(true);
        result.setOutput(stepOutput);
    } 
    catch (Exception e) {
        // 处理异常
        result.setSuccess(false);
        result.setErrorMessage(e.getMessage());
    }
    
    return result;
}
```

### 5. 智能体执行阶段

#### 5.1 智能体初始化

`BaseAgent` 的子类（如 `ReActAgent`）初始化并准备执行：

```java
public String run() {
    currentStep = 0;
    if (state != AgentState.IN_PROGRESS) {
        throw new IllegalStateException("Cannot run agent from state: " + state);
    }

    // 创建智能体执行记录
    AgentExecutionRecord agentRecord = new AgentExecutionRecord(getPlanId(), getName(), getDescription());
    agentRecord.setMaxSteps(maxSteps);
    agentRecord.setStatus(state.toString());
    
    // 记录执行
    if (planId != null && planExecutionRecorder != null) {
        planExecutionRecorder.recordAgentExecution(agentRecord);
    }

    // 执行步骤直到完成或达到最大步骤数
    while (state == AgentState.IN_PROGRESS && currentStep < maxSteps) {
        currentStep++;
        
        // 执行单个步骤
        AgentExecResult stepResult = step();
        
        // 更新状态
        state = stepResult.getState();
        
        // 如果完成或失败，返回结果
        if (state == AgentState.FINISHED || state == AgentState.FAILED) {
            return stepResult.getResult();
        }
    }

    // 处理卡住状态
    if (state == AgentState.IN_PROGRESS) {
        handleStuckState(agentRecord);
    }

    return "Agent execution completed or timed out";
}
```

#### 5.2 执行单个步骤

`ReActAgent` 实现 `step()` 方法，执行推理-行动循环：

```java
@Override
protected AgentExecResult step() {
    // 获取思考消息
    Message thinkMessage = getThinkMessage();
    
    // 获取下一步消息
    Message nextStepMessage = getNextStepWithEnvMessage();
    
    // 构建消息列表
    List<Message> messages = new ArrayList<>();
    messages.add(thinkMessage);
    messages.add(nextStepMessage);
    
    // 调用 LLM 获取响应
    Message response = llmService.callLlm(messages, getToolCallList());
    
    // 处理响应
    if (response instanceof AssistantMessage assistantMessage) {
        List<ToolCall> toolCalls = assistantMessage.getToolCalls();
        
        // 如果包含工具调用
        if (toolCalls != null && !toolCalls.isEmpty()) {
            // 处理工具调用
            List<ToolCallResult> toolCallResults = new ArrayList<>();
            
            for (ToolCall toolCall : toolCalls) {
                // 获取工具名称和参数
                String toolName = toolCall.getName();
                Map<String, Object> toolParams = toolCall.getParameters();
                
                // 获取工具回调
                ToolCallback toolCallback = getToolCallList().stream()
                    .filter(tc -> tc.getName().equals(toolName))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("Tool not found: " + toolName));
                
                // 调用工具
                Object toolResult = toolCallback.call(toolParams);
                
                // 添加工具调用结果
                toolCallResults.add(new ToolCallResult(toolCall, toolResult));
            }
            
            // 构建工具调用结果消息
            Message toolResultMessage = new ToolCallResultMessage(toolCallResults);
            
            // 添加到环境数据
            Map<String, Object> envData = getEnvData();
            envData.put("lastToolCallResults", toolCallResults);
            setEnvData(envData);
            
            // 继续执行
            return new AgentExecResult("Tool calls executed", AgentState.IN_PROGRESS);
        }
        else {
            // 没有工具调用，任务完成
            return new AgentExecResult(assistantMessage.getContent(), AgentState.FINISHED);
        }
    }
    
    // 处理异常情况
    return new AgentExecResult("Invalid response from LLM", AgentState.FAILED);
}
```

### 6. 工具调用阶段

#### 6.1 工具调用准备

智能体通过 LLM 决定调用哪个工具，并准备参数：

```java
// 在 ReActAgent 的 step() 方法中
ToolCall toolCall = toolCalls.get(0);
String toolName = toolCall.getName();
Map<String, Object> toolParams = toolCall.getParameters();

// 获取工具回调
ToolCallback toolCallback = getToolCallList().stream()
    .filter(tc -> tc.getName().equals(toolName))
    .findFirst()
    .orElseThrow(() -> new IllegalArgumentException("Tool not found: " + toolName));
```

#### 6.2 执行工具调用

调用工具并获取结果：

```java
// 调用工具
Object toolResult = toolCallback.call(toolParams);

// 处理工具调用结果
ToolCallResult toolCallResult = new ToolCallResult(toolCall, toolResult);
```

#### 6.3 处理工具调用结果

将工具调用结果添加到环境数据中，供后续步骤使用：

```java
// 添加到环境数据
Map<String, Object> envData = getEnvData();
envData.put("lastToolCallResults", toolCallResults);
setEnvData(envData);
```

### 7. 结果整合阶段

#### 7.1 收集步骤结果

`PlanExecutor` 收集所有步骤的执行结果：

```java
// 在 PlanExecutor 的 executePlan() 方法中
Map<Integer, StepExecutionResult> stepResults = new HashMap<>();

for (PlanStep step : plan.getSteps()) {
    StepExecutionResult stepResult = executeStep(step);
    stepResults.put(step.getStepNumber(), stepResult);
    
    // 如果步骤执行失败，停止执行
    if (!stepResult.isSuccess()) {
        result.setSuccess(false);
        result.setErrorMessage("Step " + step.getStepNumber() + " failed: " + stepResult.getErrorMessage());
        break;
    }
}

result.setStepResults(stepResults);
```

#### 7.2 生成最终结果

`PlanFinalizer` 整合所有步骤的结果，生成最终输出：

```java
public String finalizePlan(PlanExecutionResult executionResult) {
    // 构建提示词
    Message systemMessage = promptLoader.createSystemMessage("planning/plan-finalization.txt", Collections.emptyMap());
    
    // 构建用户消息
    Map<String, Object> variables = new HashMap<>();
    variables.put("plan", executionResult.getPlan());
    variables.put("stepResults", executionResult.getStepResults());
    Message userMessage = promptLoader.createUserMessage("planning/finalization-input.txt", variables);
    
    // 调用 LLM 整合结果
    List<Message> messages = Arrays.asList(systemMessage, userMessage);
    Message response = llmService.callLlm(messages);
    
    // 返回最终结果
    return response.getContent();
}
```

### 8. 结果返回阶段

#### 8.1 更新执行记录

`PlanningService` 更新计划执行记录：

```java
// 在 PlanningService 的 executePlan() 方法中
// 更新计划执行记录
planExecutionRecorder.updatePlanExecutionResult(planId, result);
```

#### 8.2 发送完成事件

`PlanningService` 发送计划执行完成事件：

```java
// 在 PlanningService 的 executePlan() 方法中
// 发送执行完成事件
eventPublisher.publishEvent(new PlanExecutionCompletedEvent(planId, result));
```

#### 8.3 客户端获取结果

客户端通过轮询或 WebSocket/SSE 获取执行结果：

```javascript
// 前端代码示例
async function getExecutionResult(planId) {
  try {
    const response = await fetch(`/api/planning/${planId}/result`);
    if (response.ok) {
      const result = await response.json();
      if (result.status === 'COMPLETED') {
        return result.output;
      } else if (result.status === 'FAILED') {
        throw new Error(result.errorMessage);
      } else {
        // 继续轮询
        await new Promise(resolve => setTimeout(resolve, 1000));
        return getExecutionResult(planId);
      }
    } else {
      throw new Error('Failed to fetch execution result');
    }
  } catch (error) {
    console.error('Error getting execution result:', error);
    throw error;
  }
}
```

## 关键接口定义

### 1. 计划执行请求接口

```java
public class PlanExecutionRequest {
    private String userInput;
    private PlanOptions options;
    
    // getters and setters
}

public class PlanOptions {
    private int maxSteps = 20;
    private long timeout = 300000; // 5 minutes
    
    // getters and setters
}
```

### 2. 计划执行响应接口

```java
public class PlanExecutionResponse {
    private String planId;
    private String result;
    
    // getters and setters
}
```

### 3. 计划模型接口

```java
public class Plan {
    private String planId;
    private String title;
    private String description;
    private List<PlanStep> steps;
    
    // getters and setters
}

public class PlanStep {
    private int stepNumber;
    private String description;
    private String agentType;
    private String input;
    
    // getters and setters
}
```

### 4. 执行结果接口

```java
public class PlanExecutionResult {
    private Plan plan;
    private boolean success;
    private String errorMessage;
    private Map<Integer, StepExecutionResult> stepResults;
    
    // getters and setters
}

public class StepExecutionResult {
    private boolean success;
    private String output;
    private String errorMessage;
    
    // getters and setters
}
```

## 任务处理的关键技术点

### 1. 异步执行

JManus 使用异步执行机制处理任务，避免阻塞用户请求：

```java
CompletableFuture.runAsync(() -> {
    planningService.executePlan(planId, request.getUserInput(), request.getOptions());
});
```

### 2. 事件驱动

JManus 使用事件驱动架构，通过事件通知系统状态变化：

```java
eventPublisher.publishEvent(new PlanExecutionCompletedEvent(planId, result));
```

### 3. 实时更新

JManus 使用 WebSocket 或 SSE 提供实时执行更新：

```java
@GetMapping("/api/execution/{planId}/updates")
public SseEmitter subscribeToUpdates(@PathVariable String planId) {
    SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
    // ...
    return emitter;
}
```

### 4. 状态管理

JManus 使用状态模式管理智能体的生命周期：

```java
public enum AgentState {
    NOT_STARTED,
    IN_PROGRESS,
    FINISHED,
    FAILED,
    STUCK
}
```

### 5. 错误处理

JManus 实现了全面的错误处理机制，确保系统稳定性：

```java
try {
    // 执行操作
} catch (Exception e) {
    // 记录错误
    planExecutionRecorder.updatePlanExecutionError(planId, e.getMessage());
    // 发送错误事件
    eventPublisher.publishEvent(new PlanExecutionFailedEvent(planId, e));
}
```

## 总结

Spring AI Alibaba JManus 的任务处理流程是一个完整的端到端过程，涉及多个组件的协作。从用户提交任务开始，系统会创建执行计划，分解任务，协调智能体执行各个步骤，最终整合结果并返回给用户。

整个流程设计充分考虑了异步执行、实时更新、错误处理等关键技术点，确保系统能够稳定、高效地处理各种复杂任务。通过理解这个流程，开发者可以更好地使用和扩展 JManus 系统，实现更复杂的多智能体应用。 