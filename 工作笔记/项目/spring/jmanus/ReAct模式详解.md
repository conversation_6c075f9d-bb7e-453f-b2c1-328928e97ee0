# ReAct 模式详解

## 什么是 ReAct 模式

ReAct（Reasoning + Acting）是一种结合推理（Reasoning）和行动（Acting）的智能体交互模式，最早由谷歌研究院在论文 ["ReAct: Synergizing Reasoning and Acting in Language Models"](https://arxiv.org/abs/2210.03629) 中提出。这种模式旨在让语言模型通过交替进行推理和行动来解决复杂任务，使其能够更有效地与环境交互并解决问题。

### ReAct 模式的核心理念

ReAct 模式的核心理念是：

1. **推理（Reasoning）**：模型对当前情况进行分析和思考，形成解决问题的思路
2. **行动（Acting）**：基于推理结果采取具体行动，与环境交互
3. **交替进行**：推理和行动不是一次性完成，而是交替进行，形成一个反馈循环

这种模式的优势在于：

- **自我纠错**：通过行动获取的反馈可以修正错误的推理
- **探索性解决问题**：可以通过尝试不同行动来收集信息，而不是一次性给出答案
- **透明性**：整个推理和行动过程是可见的，便于理解和调试
- **复杂任务分解**：将复杂任务分解为一系列推理-行动步骤

## ReAct 模式的工作流程

ReAct 模式的典型工作流程如下：

```
┌─────────────────┐
│  初始问题/任务  │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  思考（推理）   │◄─────┐
└────────┬────────┘      │
         │               │
         ▼               │
┌─────────────────┐      │
│  行动           │      │
└────────┬────────┘      │
         │               │
         ▼               │
┌─────────────────┐      │
│  观察结果       ├──────┘
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  最终答案/结果  │
└─────────────────┘
```

1. **初始输入**：系统接收用户的问题或任务
2. **思考（推理）阶段**：
   - 分析当前情况和目标
   - 制定解决问题的策略
   - 决定下一步需要采取的行动
3. **行动阶段**：
   - 执行具体的操作（如搜索信息、调用工具等）
   - 与环境交互获取新信息
4. **观察阶段**：
   - 获取行动的结果
   - 更新对问题的理解
5. **循环迭代**：
   - 基于新的观察结果进行下一轮思考
   - 重复推理-行动-观察循环，直到问题解决
6. **最终输出**：提供最终的解决方案或答案

## ReAct 模式在 Spring AI Alibaba JManus 中的实现

在 Spring AI Alibaba JManus 中，ReAct 模式通过 `ReActAgent` 类实现，该类继承自 `BaseAgent` 抽象类。

### ReActAgent 的核心实现

`ReActAgent` 类的核心实现如下：

```java
public class ReActAgent extends BaseAgent {
    
    // 构造函数
    public ReActAgent(LlmService llmService, PlanExecutionRecorder planExecutionRecorder, 
                      ManusProperties manusProperties, Map<String, Object> initialAgentSetting, 
                      PromptLoader promptLoader) {
        super(llmService, planExecutionRecorder, manusProperties, initialAgentSetting, promptLoader);
    }
    
    @Override
    public String getName() {
        return "ReActAgent";
    }
    
    @Override
    public String getDescription() {
        return "Agent that implements alternating execution of reasoning and acting";
    }
    
    @Override
    protected AgentExecResult step() {
        // 1. 获取思考消息（推理阶段）
        Message thinkMessage = getThinkMessage();
        
        // 2. 获取下一步消息（包含环境数据）
        Message nextStepMessage = getNextStepWithEnvMessage();
        
        // 3. 构建消息列表
        List<Message> messages = new ArrayList<>();
        messages.add(thinkMessage);
        messages.add(nextStepMessage);
        
        // 4. 调用 LLM 获取响应（推理阶段）
        Message response = llmService.callLlm(messages, getToolCallList());
        
        // 5. 处理响应（行动阶段）
        // 检查是否包含工具调用
        if (response instanceof AssistantMessage assistantMessage && 
            assistantMessage.getToolCalls() != null && 
            !assistantMessage.getToolCalls().isEmpty()) {
            
            // 执行工具调用（行动）
            // ...
            
            // 观察工具调用结果
            // ...
            
            // 更新状态
            return new AgentExecResult(result, AgentState.IN_PROGRESS);
        } 
        else {
            // 任务完成
            return new AgentExecResult(((AssistantMessage) response).getContent(), AgentState.FINISHED);
        }
    }
    
    @Override
    protected Message getNextStepWithEnvMessage() {
        // 构建包含环境数据的提示词
        Map<String, Object> variables = new HashMap<>(getEnvData());
        // 添加其他必要变量
        
        // 创建用户消息
        return promptLoader.createUserMessage("agent/react-step.txt", variables);
    }
    
    @Override
    public List<ToolCallback> getToolCallList() {
        // 返回该智能体可用的工具列表
        // ...
    }
}
```

### ReActAgent 的工作流程

在 JManus 中，ReActAgent 的工作流程如下：

1. **初始化**：
   - 接收用户输入和初始环境数据
   - 设置可用的工具和最大步骤数

2. **推理阶段**：
   - 通过 `getThinkMessage()` 获取思考提示词
   - 通过 `getNextStepWithEnvMessage()` 获取包含环境数据的提示词
   - 调用 LLM 进行推理，决定下一步行动

3. **行动阶段**：
   - 解析 LLM 的响应，提取工具调用信息
   - 执行相应的工具调用
   - 收集工具调用的结果

4. **观察阶段**：
   - 将工具调用结果作为新的观察添加到环境数据中
   - 更新智能体状态

5. **循环迭代**：
   - 重复执行 `step()` 方法，进行下一轮推理-行动-观察循环
   - 直到任务完成或达到最大步骤数

6. **结果输出**：
   - 返回最终的执行结果

## ReAct 模式的提示词设计

ReAct 模式的有效实现很大程度上依赖于提示词的设计。在 JManus 中，ReAct 智能体的提示词模板通常包含以下部分：

### 系统提示词（思考提示词）

```
你是一个能够思考和行动的智能体。你需要：

1. 思考（Reasoning）：分析当前情况，理解问题，并制定解决方案
2. 行动（Acting）：使用提供的工具执行具体操作
3. 观察（Observing）：分析行动结果，更新你的理解

解决问题时，请遵循以下步骤：
1. 分析当前任务和环境
2. 思考可能的解决方案
3. 决定下一步行动
4. 执行行动并观察结果
5. 基于结果更新你的理解
6. 重复上述过程直到问题解决

可用工具：
{{tools}}

当你需要使用工具时，请使用以下格式：
```

### 用户提示词（步骤提示词）

```
当前任务：{{task}}

当前环境：
{{environment}}

之前的行动和观察：
{{history}}

现在，请思考并决定下一步行动。
```

## ReAct 模式的应用场景

ReAct 模式在 JManus 中适用于以下场景：

1. **信息搜索和综合**：需要多次查询不同信息源并综合结果
2. **多步骤问题解决**：需要分步骤解决的复杂问题
3. **交互式任务**：需要与用户或环境持续交互的任务
4. **探索性任务**：事先不清楚解决路径，需要边探索边解决的任务
5. **工具使用**：需要使用多种工具协同工作的任务

## ReAct 模式与其他模式的对比

| 特性 | ReAct 模式 | 传统问答模式 | Chain-of-Thought 模式 |
|------|------------|--------------|----------------------|
| 交互方式 | 交替推理和行动 | 一次性问答 | 连续思考步骤 |
| 环境交互 | 支持 | 不支持/有限 | 不支持/有限 |
| 自我纠错 | 强 | 弱 | 中 |
| 适用任务 | 复杂、交互式任务 | 简单问答 | 需要推理的问题 |
| 工具使用 | 原生支持 | 有限支持 | 有限支持 |
| 透明度 | 高 | 低 | 中 |

## ReAct 模式的优化技巧

在 JManus 中使用 ReAct 模式时，可以考虑以下优化技巧：

1. **提示词优化**：
   - 明确指定思考和行动的格式
   - 提供清晰的工具使用说明
   - 包含示例来引导模型按照预期格式输出

2. **工具设计**：
   - 设计功能单一、接口清晰的工具
   - 为工具提供详细的描述和使用示例
   - 确保工具返回结构化且易于理解的结果

3. **状态管理**：
   - 有效管理和传递环境状态
   - 保留关键历史信息，避免状态过于膨胀
   - 实现有效的卡住检测和恢复机制

4. **步骤控制**：
   - 设置合理的最大步骤数
   - 实现提前终止机制，避免无效循环
   - 添加进度跟踪和报告功能

## 总结

ReAct 模式是一种强大的智能体交互模式，通过交替进行推理和行动，使语言模型能够更有效地解决复杂任务。在 Spring AI Alibaba JManus 中，ReAct 模式通过 `ReActAgent` 类实现，该类提供了完整的推理-行动-观察循环机制。

通过合理设计提示词、工具和状态管理，ReAct 模式可以有效应对各种复杂任务，包括信息搜索、多步骤问题解决、交互式任务和工具使用场景。这种模式不仅提高了智能体的问题解决能力，还增强了其透明性和可解释性，使开发者能够更好地理解和调试智能体的行为。 