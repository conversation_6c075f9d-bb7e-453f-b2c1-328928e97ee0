## mcp 

### 基础服务

- mcp 市场 https://smithery.ai/

- mcp Service  [MCP 环境安装 | CherryStudio](https://docs.cherry-ai.com/advanced-basic/mcp/install)

- cherryStudio 依赖文件   [uv.exe](..\..\..\assets\file\uv.exe)  [uvx.exe](..\..\..\assets\file\uvx.exe)  [bun.exe](..\..\..\assets\file\bun.exe) 

- cline 依赖 uv https://docs.astral.sh/uv/getting-started/installation/ 依赖npx 使用nvm 安装node

- 百度mcp [控制台 | 百度地图开放平台](https://lbsyun.baidu.com/apiconsole/center)  key:yK1FmNpU8lwu1QXakgwzdIRx5PtuBUH6

  - sse 接入

  ```json
  {
    "mcpServers": {
      "baidu-maps": {
        "url": "https://mcp.map.baidu.com/sse?ak=您的AK"
      }
    }
  }
  
  ```

   - Python接入
  ```json
  {
  "mcpServers": {
    "baidu-maps": {
      "command": "uvx",
      "args": ["mcp-server-baidu-maps"],
      "env": {
        "BAIDU_MAPS_API_KEY": "<YOUR_API_KEY>"
      }
    }
  }
  }
  ```
  
   - node接入
  ```json
     {
        "mcpServers": {
            "baidu-map": {
                "command": "npx",
                "args": [
                    "-y",
                    "@baidumap/mcp-server-baidu-map"
                ],
                "env": {
                    "BAIDU_MAP_API_KEY": "{您的AK}"
                }
            }
        }
    }
  ```

### 注意点

1. windows运行 注意配置

   command 写全路径

   ```json
   {
     "mcpServers": {
       "fetch": {
         "disabled": true,
         "timeout": 60,
         "command": "C:\\Users\\<USER>\\.local\\bin\\uvx.exe",
         "args": [
           "mcp-server-fetch"
         ],
         "transportType": "stdio"
       },
       "baidu-maps": {
         "disabled": true,
         "timeout": 60,
         "command": "C:\\Users\\<USER>\\.local\\bin\\uvx.exe",
         "args": [
           "mcp-server-baidu-maps"
         ],
         "env": {
           "BAIDU_MAPS_API_KEY": "yK1FmNpU8lwu1QXakgwzdIRx5PtuBUH6"
         },
         "transportType": "stdio"
       },
       "filesystem": {
         "disabled": false,
         "timeout": 60,
         "command": "cmd",
         "args": [
           "/c",
           "npx",
           "-y",
           "@modelcontextprotocol/server-filesystem",
   
         ],
         "transportType": "stdio"
       }
     }
   }
```

   
