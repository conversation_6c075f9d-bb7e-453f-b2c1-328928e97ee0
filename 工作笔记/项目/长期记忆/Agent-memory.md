# Agent-memory

[TOC]

## 1. 整体流程图
```mermaid
graph TD
    A[信息过滤] --> B[观察获取]
    B --> C[记忆总结]
    C --> D[重复检测]
    D --> E[存储记忆]
    
    B1[获取观察] --> B
    B2[获取带时间观察] --> B
    B3[加载今日记忆] --> B
    B4[加载总结记忆] --> B
```

## 2. 各阶段

### 2.1 信息过滤 (info_filter)

#### 2.1.1 功能概述
```mermaid
graph TD
    A[输入消息] --> B[过滤非用户消息]
    B --> C[截断长消息]
    C --> D[构建提示词]
    D --> E[调用AI模型]
    E --> F[解析评分]
    F --> G[保留重要信息]
```

```yaml
info_filter_system:
  cn: |
    任务：对所给{batch_size}个句子中所含有的关于{user_name}的信息打分，分数为0,1,2或3。
    注意：其中0表示不包含用户信息，1表示句子中只包含用户假设的信息或者用户虚构的内容比如用户创作的小说或剧本，2表示包含用户的一般信息，时效性信息或者需要猜测才能得到的用户信息，3表示明确含有或者可以确定推断出关于用户的重要信息，或者用户要求记录。
    {user_name}的重要信息可以包含：
    1. 用户基本信息，用户画像信息，用户兴趣偏好信息
    2. 用户重大事件转折点
    3. 用户情绪状态（如开心、难过、焦虑、生气等）
    4. 用户身体健康状况（如疾病、症状、身体状态等）

    健康信息判定标准：
    - 分数0：与用户无关的问题（如"人类平均寿命是多少","帮我画个猪","今天天气怎么样"）
    - 分数1：假设性的健康问题（如"如果得了感冒该怎么办"）
    - 分数2：
      * 任何与健康相关的咨询（如"感冒吃什么药"，"鼻塞能吃点啥"）
      * 对特定疾病的关注（询问某种疾病或症状）
    - 分数3：
      * 明确表达自身状况（如"我的咽炎很严重"）
      * 描述症状（如"我最近咳嗽得厉害"，"鼻子不通气"）
      * 就医相关信息（如"医生说我有咽炎"）
      * 要求记录的健康信息
      * 询问治疗方案（如"感冒该吃什么药"）

    情绪状态判定标准：
    - 分数0：与情绪无关的一般性问题
    - 分数1：假设性的情绪问题（如"如果我伤心了该怎么办"）
    - 分数2：
      * 间接表达情绪（如"心情不好该吃点啥"）
      * 询问情绪管理方法
    - 分数3：
      * 直接表达当前情绪状态（如"我现在很难过"）
      * 描述情绪变化（如"最近心情不太好"）
      * 要求记录的情绪信息

    个人信息判定标准：
    - 分数0：与个人信息无关的问题
    - 分数1：假设性的个人信息（如"如果我叫张三"）
    - 分数2：
      * 间接提及个人信息
      * 可能需要推断的个人信息
    - 分数3：
      * 明确的个人信息（如姓名、年龄、职业等）
      * 要求记录的个人信息（如"以后叫我佳佳"）
      * 个人偏好表达（如"我喜欢被称呼为小明"）

    特别注意：对于用户情绪和健康相关信息，即使是简单询问也应给予较高权重，因为这往往反映了用户当前的状态和需求。
    对每个句子都做一次信息打分，一共输出{batch_size}个分数，不需要写最终结果。
    请一定要按如下格式依次输出，最后的结果一定要加<>:
    思考：思考的依据和过程，30字以内。
    结果：<句子序号> <分数:0或1或2或3>
```

```
输入：
1 用户：我刚刚入职了阿里巴巴。
2 用户：露天睡觉蚊子多，咋搞。
3 用户：创造力和外倾性有关？
输出：
<1> <3> # 重要工作信息
<2> <2> # 间接表达的生活状态
<3> <0> # 不包含用户信息
```

### 2.2 获取观察记忆

```mermaid
graph TD
    A[输入消息] --> B[过滤消息]
    B --> C[构建观察提示词]
    C --> D[调用AI模型]
    D --> E[解析观察结果]
    E --> F[创建观察节点]
    F --> G[存储观察数据]

    subgraph 消息过滤
    B1[过滤非时间相关消息] --> B
    end

    subgraph 观察生成
    D1[生成观察内容] --> D
    D2[推断时间信息] --> D
    D3[提取关键词] --> D
    end

    subgraph 节点创建
    F1[设置元数据] --> F
    F2[设置时间信息] --> F
    F3[设置关键词] --> F
    end
```

```yaml
get_observation_system:
  cn: |
    任务：从下面的{num_obs}句{user_name}句子中依次提取出关于{user_name}的重要信息，与相应的关键词。如果没有重要信息则回答"无"，最多提取{num_obs}条信息。
    {user_name}的重要信息可以包含：
    1. 用户基本信息：年龄、性别、职业、教育背景、称呼偏好等
    2. 用户画像信息：
       - 健康状况：疾病、症状、过敏史、体质特点等
       - 饮食习惯：食物偏好、忌口、饮食规律、饮食禁忌等
       - 生活作息：运动习惯、睡眠情况、工作时间等
       - 皮肤状况：肤质特点、护理需求、皮肤问题等
    3. 用户兴趣偏好：爱好、娱乐方式等
    4. 用户性格特征：性格倾向、行为模式等
    5. 用户价值观：人生态度、价值取向等
    6. 用户人际关系：家庭、朋友、同事等
    7. 用户重大事件：生活转折点、重要决定等
    8. 用户情绪状态：当前或近期的情绪变化等

    特别注意：
    1. 健康信息判定：
       a) 直接表述（应记录）：
          - 明确的健康状况（如"我有咽炎"）
          - 症状描述（如"最近咳嗽"，"鼻子不通气"）
          - 过敏史（如"对花粉过敏"）
          - 体质特点（如"容易感冒"）
          - 就医记录（如"医生说我..."）

       b) 间接表述（应记录）：
          - 健康相关咨询（如"感冒吃什么药"，"鼻塞能吃点啥"）
          - 特定症状咨询（如"慢性咽炎吃什么"）
          - 身体部位问题（如"头疼该吃什么药"）
          - 皮肤护理咨询（如"脸上发油怎么保养"）
          - 对特定疾病的关注（询问某种疾病或症状）

    2. 情绪状态判定：
       a) 直接表述（应记录）：
          - 明确的情绪表达（如"我现在很难过"）
          - 情绪状态描述（如"最近心情不太好"）
          - 要求记录的情绪信息

       b) 间接表述（应记录）：
          - 情绪相关咨询（如"心情不好该吃点啥"）
          - 情绪管理需求（如"总是很焦虑怎么办"）

    3. 个人信息判定：
       a) 直接表述（应记录）：
          - 明确的个人信息（如姓名、年龄、职业等）
          - 称呼偏好（如"以后叫我佳佳"）
          - 个人特征描述（如"我是理工男"）

       b) 间接表述（应记录）：
          - 暗示性的个人信息（如通过工作地点推断公司）
          - 生活习惯暗示（如通过日常活动推断作息）

    4. 判定原则：
       - 所有与用户健康、情绪、个人信息相关的咨询都应该被记录
       - 即使是简单询问也可能反映用户当前的状态和需求
       - 对特定话题的持续关注说明该信息对用户很重要

    如果句子中只包含{user_name}假设的信息或者{user_name}虚构的内容比如{user_name}创作的小说或剧本，回答"无"。
    对每个句子都做一次信息提取，最后一共输出{num_obs}条信息。
    请一步步思考，并一定要按如下格式依次输出，最后的结果一定要加<>:
    思考：思考的依据和过程，50字以内。
    信息：<句子序号> <> <明确的重要信息或"无"> <关键词>
```

```
1 用户：我今天去看了医生，医生说我有慢性咽炎。
2 用户：最近工作压力很大，经常失眠。
3 用户：帮我查一下明天的天气。

<1> <今天> <用户被诊断患有慢性咽炎> <医生,慢性咽炎,诊断>
<2> <最近> <用户工作压力大导致失眠> <工作压力,失眠>
<3> <无> <无> <无>
```

### 2.3 获取带时间观察记忆

```yaml
time_string_format:
  cn: |
    {year}年{month}{day}日{weekday}{hour}点
  en: |
    {month} {day}, {year}, {weekday}, at {hour}

get_observation_with_time_system:
  cn: |
    任务：从下面的{num_obs}句{user_name}句子中依次提取出关于{user_name}的重要信息，相应的关键词与时间信息。如果没有重要信息则回答"无"，最多提取{num_obs}条信息。
    每一句{user_name}句子的格式是：<序号> <对话时间> {user_name}：<句子>

    {user_name}的重要信息可以包含：
    1. 用户基本信息：年龄、性别、职业、教育背景、称呼偏好等
    2. 用户画像信息：
       - 健康状况：疾病、症状、过敏史、体质特点等
       - 饮食习惯：食物偏好、忌口、饮食规律、饮食禁忌等
       - 生活作息：运动习惯、睡眠情况、工作时间等
       - 皮肤状况：肤质特点、护理需求、皮肤问题等
    3. 用户兴趣偏好：爱好、娱乐方式等
    4. 用户性格特征：性格倾向、行为模式等
    5. 用户价值观：人生态度、价值取向等
    6. 用户人际关系：家庭、朋友、同事等
    7. 用户重大事件：生活转折点、重要决定等
    8. 用户情绪状态：当前或近期的情绪变化等

    特别注意：
    1. 健康信息判定：
       a) 直接表述（应记录）：
          - 明确的健康状况（如"我有咽炎"）
          - 症状描述（如"最近咳嗽"，"鼻子不通气"）
          - 过敏史（如"对花粉过敏"）
          - 体质特点（如"容易感冒"）
          - 就医记录（如"医生说我..."）

       b) 间接表述（应记录）：
          - 健康相关咨询（如"感冒吃什么药"，"鼻塞能吃点啥"）
          - 特定症状咨询（如"慢性咽炎吃什么"）
          - 身体部位问题（如"头疼该吃什么药"）
          - 皮肤护理咨询（如"脸上发油怎么保养"）
          - 对特定疾病的关注（询问某种疾病或症状）

    2. 情绪状态判定：
       a) 直接表述（应记录）：
          - 明确的情绪表达（如"我现在很难过"）
          - 情绪状态描述（如"最近心情不太好"）
          - 要求记录的情绪信息

       b) 间接表述（应记录）：
          - 情绪相关咨询（如"心情不好该吃点啥"）
          - 情绪管理需求（如"总是很焦虑怎么办"）

    3. 个人信息判定：
       a) 直接表述（应记录）：
          - 明确的个人信息（如姓名、年龄、职业等）
          - 称呼偏好（如"以后叫我付佳"）
          - 个人特征描述（如"我是理工男"）

       b) 间接表述（应记录）：
          - 暗示性的个人信息（如通过工作地点推断公司）
          - 生活习惯暗示（如通过日常活动推断作息）

    4. 时间信息判定：
       a) 明确时间：
          - 具体日期（如"2023年5月1日"）
          - 周期性日期（如生日、结婚纪念日等）
          - 相对时间（如"下周一"、"明天"等）

       b) 模糊时间：
          - 近期表述（如"最近"、"这几天"等）
          - 过去表述（如"上个月"、"去年"等）
          - 将来表述（如"下个月"、"明年"等）

    5. 判定原则：
       - 所有与用户健康、情绪、个人信息相关的咨询都应该被记录
       - 即使是简单询问也可能反映用户当前的状态和需求
       - 对特定话题的持续关注说明该信息对用户很重要
       - 时间信息应结合对话时间进行推断和补充

    如果句子中只包含{user_name}假设的信息或者{user_name}虚构的内容比如{user_name}创作的小说或剧本，回答"无"。
    如果{user_name}信息涉及时间，则结合对话时间推断{user_name}信息的时间信息，没有则不输出。
    对每个句子都做一次信息提取，最后一共输出{num_obs}条信息。
    请一步步思考，并一定要按如下格式依次输出，最后的结果一定要加<>:
    思考：思考的依据和过程，50字以内。
    信息：<句子序号> <时间信息或不输出> <明确的重要信息或"无"> <关键词>

```

```
1 2024-03-15 10:30 用户：我今天下午3点要去医院复诊。
2 2024-03-15 11:00 用户：上周体检报告显示血压有点高。
3 2024-03-15 11:30 用户：帮我查一下明天的天气

<1> <今天下午3点> <用户需要去医院复诊> <医院,复诊,预约>
<2> <上周> <用户体检发现血压偏高> <体检,血压,健康>
<3> <明天> <无> <无>
```

### 2.4  获取今日记忆

```mermaid
graph TD
    A[开始加载] --> B[并行加载任务]
    B --> C1[加载未反映记忆]
    B --> C2[加载未更新记忆]
    B --> C3[加载洞察记忆]
    B --> C4[加载今日记忆]
    
    C1 --> D[存储到内存管理器]
    C2 --> D
    C3 --> D
    C4 --> D
    
    D --> E[等待所有任务完成]
    E --> F[结束加载]

    subgraph 并行加载
    C1
    C2
    C3
    C4
    end
```

### 2.5 记忆总结

```mermaid
graph TD
    A[开始总结] --> B[获取观察节点]
    B --> C[获取现有总结]
    C --> D[分类观察内容]
    D --> E[生成新总结]
    E --> F[存储总结节点]
    F --> G[更新状态]
    G --> H[结束总结]

    subgraph 观察分类
    D1[基本信息]
    D2[健康与情绪]
    D3[生活习惯]
    end

    D --> D1
    D --> D2
    D --> D3
```

```yaml
summary_memory_system:
  cn: |
      任务：基于之前提取的{user_name}的观察信息以及现有的用户总结信息，生成一份用户画像摘要。请注意信息时效性，并妥善处理可能存在的信息冲突。

      信息分类参考（仅作为内容提取参考，不需要按小类输出）：
      1. 基本信息
        - 个人属性（年龄、性别等）
        - 重要日期（生日、纪念日等）

      2. 健康与情绪状态
        - 身体状况（疾病、过敏、体检等）
        - 情绪状态（心情、压力、困扰等）
        - 睡眠质量

      3. 生活习惯与偏好
        - 饮食偏好（口味、喜好）
        - 饮食禁忌（过敏、忌口）
        - 作息规律
        - 生活方式

      4. 社交关系
        - 家庭关系（家人、婚恋状况）
        - 社交关系（朋友、同事）
        - 人际互动特点

      5. 工作与兴趣
        - 职业信息（工作单位、职位、地点）
        - 兴趣爱好（运动、娱乐、特长）
        - 个人发展（学习、规划）

      6. 生活轨迹
        - 重要经历
        - 近期活动
        - 消费行为（投资、重要消费）

      信息处理原则：
      1. 时效性处理：
         - 对于现有总结中有失效时间的信息，需要检查是否已过期（检查到期时间是否超过当前时间）
         - 已过期的信息直接删除不纳入总结
         - 未过期的信息需要在描述中标注预计失效时间
         - 永久有效的信息（如生日）无需标注时间 且不用标注是否有效
      2. 矛盾处理：当新旧信息存在冲突时，以最新信息为准
      3. 关联整合：相关信息应当合并，形成连贯的描述
      4. 重要性：保留对用户画像最有价值的信息
      5. 人文关怀：特别关注健康、情绪、饮食等与生活品质相关的信息
      6. 如果存在用户近期出现X问题 但是没有失效时间，直接删除不纳入总结
      7. 如果用户历史经历，如在某某日有家庭聚餐，已经过了很久，直接删除不纳入总结

      请按以下格式输出用户画像摘要，只输出有信息的部分，没有相关信息的部分请省略。
      对于临时性信息，请在描述后用括号标注失效时间：

      基本信息：<个人基本属性和重要日期的描述>
      健康与情绪状态：<身体状况、情绪变化、睡眠质量等描述>（如果是临时状态，需标注失效时间）
      生活习惯与偏好：<饮食习惯、作息规律等描述>（如果是临时习惯，需标注失效时间）
      社交关系：<家庭、社交圈等描述>
      工作与兴趣：<工作情况、兴趣爱好等描述>
      生活轨迹：<重要经历、近期活动等描述>
```

```yaml
请完成两个任务：

1. 判断以下观察内容是否属于以下三个类别之一：
   - 基本信息（个人属性、重要日期等）
   - 健康与情绪状态（身体状况、情绪状态、睡眠质量等）
   - 生活习惯与偏好（饮食偏好、作息规律等）

2. 判断该观察信息的有效期：
   - 当前时间：{current_time_str}
   - 分析观察内容的时效性
   - 预测这个信息的具体失效时间
   - 如果是永久有效的信息（如生日），请使用"permanent"作为失效时间
   - 如果是推断类型，失效时间需要短一点

时效性参考示例：
- 基本信息类：姓名、称呼、生日等使用"permanent"
- 健康状态类：
  * 感冒、发烧等短期症状：建议1周左右
  * 慢性病、长期症状：建议6月-12个月
  * 情绪状态：一般3-7天
- 生活习惯类：
  * 饮食偏好：建议2-3周
  * 作息规律：建议2周左右
  * 兴趣爱好：建议1个月左右

请使用以下JSON格式回复：
{{
    "category": <数字0-3，如果不属于这三类则填0>,
    "expiry_time": <具体失效时间，如"2024-03-20"，永久有效则填"permanent">,
    "reason": <简要说明为什么给出这个有效期判断>
}}
```



```mermaid
graph TD
    A[开始] --> B[获取观察记忆]
    B --> C[记忆分类处理]
    C --> D[存储分类数据]
    D --> E[记忆合并处理]
    E --> F[结束]

    subgraph 记忆分类处理
    C1[基本信息类]
    C2[健康情绪类]
    C3[生活习惯类]
    C4[设置过期时间]
    end

    subgraph 存储分类数据
    D1[基本信息存储]
    D2[健康情绪存储]
    D3[生活习惯存储]
    D4[过期时间标记]
    end

    subgraph 记忆合并处理
    E1[召回当天过期记忆]
    E2[构建合并Prompt]
    E3[执行记忆合并]
    E4[更新存储状态]
    end

    C --> C1
    C --> C2
    C --> C3
    C --> C4

    D --> D1
    D --> D2
    D --> D3
    D --> D4

    E --> E1
    E --> E2
    E --> E3
    E --> E4

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#f9f,stroke:#333,stroke-width:2px
```

#### 2.5.1. 记忆分类处理
```mermaid
graph TD
    A[观察记忆] --> B[分类判断]
    B --> C1[基本信息]
    B --> C2[健康情绪]
    B --> C3[生活习惯]
    C1 --> D[设置过期时间]
    C2 --> D
    C3 --> D
    D --> E[返回分类结果]
```

#### 2.5.2 存储分类数据
```mermaid
graph TD
    A[分类结果] --> B[构建存储节点]
    B --> C[设置元数据]
    C --> D[存储到数据库]
    D --> E[更新状态]
```

#### 2.5.3 记忆合并处理
```mermaid
graph TD
    A[开始合并] --> B[查询过期记忆]
    B --> C[构建合并Prompt]
    C --> D[执行合并]
    D --> E[更新存储]
```
### 2.6 记忆更新

#### 2.6.1 原始观察记忆

```python
observation = {
    "content": "用户今天提到他最近睡眠质量不好，经常凌晨2点才睡，早上8点起床。他喜欢吃辣的食物，但最近因为胃不舒服减少了辣度。",
    "timestamp": "2024-03-20 15:30:00"
}
```

#### 2.6.2 分类处理
```python
# 分类结果
classification_result = {
    "health_emotion": {
        "content": "睡眠质量不好，经常凌晨2点才睡，早上8点起床",
        "category": "health_emotion",
        "expiry_time": "2024-03-27",  # 7天后过期
        "importance": "high"
    },
    "habits": {
        "content": "喜欢吃辣的食物，但最近因为胃不舒服减少了辣度",
        "category": "habits",
        "expiry_time": "2024-04-19",  # 30天后过期
        "importance": "medium"
    }
}
```

#### 2.6.3 存储格式
```python
# 存储节点示例
memory_nodes = [
    {
        "id": "mem_001",
        "user_name": "user123",
        "content": "睡眠质量不好，经常凌晨2点才睡，早上8点起床",
        "category": "health_emotion",
        "metadata": {
            "timestamp": "2024-03-20 15:30:00",
            "expiry_time": "2024-03-27",
            "keywords": ["睡眠", "作息", "健康"],
            "status": "active"
        }
    },
    {
        "id": "mem_002",
        "user_name": "user123",
        "content": "喜欢吃辣的食物，但最近因为胃不舒服减少了辣度",
        "category": "habits",
        "metadata": {
            "timestamp": "2024-03-20 15:30:00",
            "expiry_time": "2024-04-19",
            "keywords": ["饮食", "偏好", "健康"],
            "status": "active"
        }
    }
]
```

#### 2.6.4 过期记忆召回
```python
# 假设今天是2024-03-27，召回过期记忆
expired_memories = [
    {
        "id": "mem_001",
        "content": "睡眠质量不好，经常凌晨2点才睡，早上8点起床",
        "category": "health_emotion",
        "metadata": {
            "timestamp": "2024-03-20 15:30:00",
            "expiry_time": "2024-03-27",
            "status": "expired"
        }
    }
]
```

#### 2.6.5 构建合并Prompt
```python
merge_prompt = """
请分析并合并以下记忆信息：

1. 过期记忆：
- 睡眠质量不好，经常凌晨2点才睡，早上8点起床（2024-03-20记录）

2. 相关记忆：
- 喜欢吃辣的食物，但最近因为胃不舒服减少了辣度（2024-03-20记录）
- 最近工作压力大，经常加班（2024-03-25记录）

请根据这些信息：
1. 判断睡眠问题是否已经解决
2. 分析作息时间与工作压力的关系
3. 评估饮食调整的效果
4. 给出综合性的用户状态总结
"""
```

#### 2.6.6 合并结果
```python
merged_result = {
    "summary": "用户近期存在睡眠问题，可能与工作压力大有关。作息时间不规律（凌晨2点睡，8点起），同时因胃部不适调整了饮食习惯（减少辣度）。建议关注工作压力对作息的影响，并继续保持健康的饮食习惯。",
    "action_items": [
        "建议用户调整作息时间，尽量在23点前入睡",
        "继续观察胃部不适情况，必要时就医",
        "关注工作压力对健康的影响"
    ],
    "next_check_time": "2024-04-03"  # 7天后再次检查
}
```

#### 2.6.7 更新存储
```python
# 更新后的存储节点
updated_nodes = [
    {
        "id": "mem_001",
        "status": "merged",
        "merged_content": "用户近期存在睡眠问题，可能与工作压力大有关。作息时间不规律（凌晨2点睡，8点起）。",
        "action_items": ["建议用户调整作息时间，尽量在23点前入睡"],
        "next_check_time": "2024-04-03"
    },
    {
        "id": "mem_002",
        "status": "active",
        "content": "喜欢吃辣的食物，但最近因为胃不舒服减少了辣度",
        "expiry_time": "2024-04-19"
    }
]
```