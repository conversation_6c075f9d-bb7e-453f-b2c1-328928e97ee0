## ChatGPT memory

[TOC]

## 1. 流程

```mermaid
graph TD
    A[记忆系统] --> B[记忆类型]
    A --> C[记忆控制]
    A --> D[记忆管理]

    subgraph 记忆类型
    B1[保存的记忆 Saved Memories]
    B2[聊天历史 Chat History]
    end

    subgraph 记忆控制
    C1[启用/禁用记忆]
    C2[临时聊天模式]
    C3[记忆引用设置]
    end

    subgraph 记忆管理
    D1[添加记忆]
    D2[删除记忆]
    D3[查看记忆]
    D4[存储限制]
    end



    B --> B1
    B --> B2

    C --> C1
    C --> C2
    C --> C3

    D --> D1
    D --> D2
    D --> D3
    D --> D4


```

## 2.记忆类型

### 2.1 保存记忆

 存储个人信息数据



<img src="E:\笔记\note\assets\ChatGPT-memory\image-20250415112101400.png" alt="image-20250415112101400" style="zoom:70%;" />

### 2.2 对话历史数据

![QQ_1744687889525](E:\笔记\note\assets\ChatGPT-memory\QQ_1744687889525.png)

![QQ_1744688198891](E:\笔记\note\assets\ChatGPT-memory\QQ_1744688198891.png)

![QQ_1744688049978](E:\笔记\note\assets\ChatGPT-memory\QQ_1744688049978.png)

![QQ_1744688080394](E:\笔记\note\assets\ChatGPT-memory\QQ_1744688080394.png)

![QQ_1744783004975](E:\笔记\note\assets\ChatGPT-memory\QQ_1744783004975.png)

![QQ_1744783037937](E:\笔记\note\assets\ChatGPT-memory\QQ_1744783037937.png)

![QQ_1744783196312](E:\笔记\note\assets\ChatGPT-memory\QQ_1744783196312.png)
