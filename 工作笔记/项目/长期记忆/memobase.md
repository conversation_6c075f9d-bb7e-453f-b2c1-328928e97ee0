# memobase

[TOC]

## 1.项目架构

### 1.1总体流程

#### 1. **Event（事件）**：
   - 事件是用户的输入或交互。例如，用户在 2025 年 1 月 19 日说 “Hi, I'm Gus. What a wonderful day!”，在 2025 年 1 月 20 日提到了工作相关的内容，情绪从 “tired” 变为 “happy”。
   - 这些事件通过 “append” 操作被添加到系统中。
#### 2. **Profile（个人资料）：**
   - 个人资料是系统对用户信息的存储和处理。
   - 它包括三个主要操作：
     - **Insert（插入）**：将新的事件直接插入到个人资料中。
     - **Extract（提取）**：从事件中提取信息，如总结、标签和个人资料补丁。
     - **Merge & Organize（合并与组织）**：将提取的信息与现有个人资料合并和组织。

![img](https://mintlify.s3.us-west-1.amazonaws.com/memobase/images/starter.png)

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Buffer
    participant Memory
    participant LLM
    participant Profile

    Client->>API: 发送新记忆数据
    API->>Buffer: 插入到缓冲区
    Buffer->>Buffer: 检查缓冲区状态
    
    alt 缓冲区未满
        Buffer->>Buffer: 继续存储
    else 缓冲区已满
        Buffer->>Memory: 触发记忆处理
        Memory->>LLM: 调用extract_profile.py
        LLM->>Memory: 提取记忆特征
        Memory->>LLM: 调用organize_profile.py
        LLM->>Memory: 组织记忆结构
        Memory->>LLM: 调用merge_profile.py
        LLM->>Memory: 合并相似记忆
        Memory->>Profile: 更新用户配置
    end
    Profile->>Client: 返回处理结果
```

```python
extract

EXAMPLES = [
    (
        """
        [2025/01/01] 用户: 你好，我叫张三。我是Memobase的软件工程师。
        """,
        AIUserProfiles(
            **{
                "facts": [
                    {
                        "topic": "基本信息",
                        "sub_topic": "姓名",
                        "memo": "张三",
                    },
                    {
                        "topic": "工作",
                        "sub_topic": "职位",
                        "memo": "用户是软件工程师",
                    },
                    {
                        "topic": "工作",
                        "sub_topic": "公司",
                        "memo": "用户在Memobase工作",
                    },
                ]
            }
        ),
    ),
    (
        """
        [2025/01/01] 用户: 我最喜欢的电影是《盗梦空间》和《星际穿越》。
        [2025/01/01] 助手: 这些是很好的电影，你觉得《信条》怎么样？
        [2025/01/02] 用户: 我看过《信条》，事实上这是我最喜欢的。
        """,
        AIUserProfiles(
            **{
                "facts": [
                    {
                        "topic": "兴趣",
                        "sub_topic": "电影",
                        "memo": "《盗梦空间》、《星际穿越》和《信条》；最喜欢的电影是《信条》",
                    },
                    {
                        "topic": "兴趣",
                        "sub_topic": "电影导演",
                        "memo": "用户似乎是克里斯托弗·诺兰的忠实粉丝",
                    },
                ]
            }
        ),
    ),
]
```

```python
organize

FACT_RETRIEVAL_PROMPT = """你将为用户组织记忆。
这些记忆属于同一个给定的主题。
你会得到当前混乱/过多的记忆及其对应的子主题。
你需要将这些记忆重新组织成不超过 {max_subtopics} 个子主题：
- 如果某些记忆与主题无关，你可以丢弃它们
- 如果某些记忆与同一主题相关，你可以将它们合并到一个子主题中
- 如果必要，你可以创建新的子主题
- 最终结果应该不超过 {max_subtopics} 个子主题

## 你应该注意的主题
以下是一些你可以参考的子主题：
{user_profile_topics}
首先尝试将记忆合并到上述子主题中，如果必要，你可以创建新的子主题。

## 格式要求
### 输入
你将收到一个带有子主题的记忆列表。记忆的格式为：
主题: 主题名称
- 子主题{tab}记忆内容
- ...
主主题是主题名称，后面的行是子主题：子主题是记忆的子主题，记忆内容是记忆的具体内容。

### 输出
你需要将记忆重新组织成不超过 {max_subtopics} 个子主题：
- 新子主题{tab}记忆内容
例如：
- 姓名{tab}张三
- 职位{tab}软件工程师

每一行都是用户的新记忆，包含：
1. 新子主题：记忆的新子主题
2. 记忆内容：记忆的具体内容
这些元素应该用`{tab}`分隔，每行应该以"- "开头，并用`\n`分隔。

## 示例
这里有一些示例：
{examples}

请按照上述格式返回新的子主题和记忆内容。

请记住以下几点：
- 最终结果应该不超过 {max_subtopics} 个子主题
- 如果某些记忆与主题无关，你可以丢弃它们
- 将最重要的子主题放在前面

请注意，你需要检测记忆的语言，并用相同的语言重新组织记忆。
"""
```

```python
merge

ADD_KWARGS = {
    "prompt_id": "zh_merge_profile",
}
EXAMPLES = [
    {
        "input": """## 用户主题
基本信息, 年龄
## 旧备忘录
用户39岁
## 新备忘录
用户40岁
""",
        "response": {
            "action": "UPDATE",
            "memo": "用户40岁",
        },
    },
    {
        "input": """## 用户主题
个性, 情绪反应
## 旧备忘录
下雨天用户有时会哭泣
## 新备忘录
下雨天用户会想起了家乡
""",
        "response": {
            "action": "UPDATE",
            "memo": "下雨天用户会想起家乡，可能是其下雨天哭泣的原因之一",
        },
    },
    {
        "input": """## 用户主题
基本信息, 生日
## 旧备忘录
1999/04/30
## 新备忘录
用户没有提及生日
""",
        "response": {
            "action": "UPDATE",
            "memo": "1999/04/30",
        },
    },
    {
        "input": """## 更新说明
总是保持最新的目标并删除旧的目标。

## 用户主题
工作, 目标

## 旧备忘录
想成为一名软件工程师
## 新备忘录
想创办一家初创公司
""",
        "response": {
            "action": "UPDATE",
            "memo": "想创办一家初创公司",
        },
    },
]

MERGE_FACTS_PROMPT = """你是一个智能备忘录管理器，负责控制用户的记忆/形象。
你将收到两条关于用户同一主题/方面的备忘录，一条是旧的，一条是新的。
你应更新旧的备忘录，以包含新的备忘录中的信息。
并以输出格式返回你的结果：
- UPDATE{tab}MEMO
以'- '开头，接下来是'UPDATE'，然后是'{tab}'，最后是最终的MEMO备忘录(5句话以内)。

以下是如何生成最终的备忘录的指导原则：
## 替换旧备忘录
如果新备忘录与旧备忘录完全冲突，你应该用新的备忘录替换旧的备忘录。
<example>
{example_replace}
</example>

## 合并备忘录
如果旧备忘录中包含新备忘录中没有的信息，你应该将旧备忘录和新备忘录合并。
你需要总结新旧备忘录的内容，以便在最终备忘录中包含充分的信息。
<example>
{example_merge}
</example>

## 保持旧备忘录
如果新备忘录中没有新的信息或者不包含任何有效信息，你应该保持旧的备忘录不变。
<example>
{example_keep}
</example>

## 特殊情况
用户可能会在'## 更新说明'部分给出更新备忘录的指令，你需要理解这些指令并按照指令更新备忘录。
<example>
{example_special}
</example>


理解备忘录，你可以从新备忘录和旧备忘录中推断信息以决定正确的操作。
遵循以下说明：
- 不要返回上面提供的自定义少量提示中的任何内容。
- 严格遵守正确的格式。
- 最终的备忘录不能超过5句话, 不能超过100个字
- 保持备忘录的简洁性
"""


def get_input(topic, subtopic, old_memo, new_memo, update_instruction=None):
    header = ""
    if update_instruction:
        header = f"""## 更新说明
{update_instruction}"""
    return f"""{header}
## 用户主题
{topic}, {subtopic}
## 旧备忘录
{old_memo}
## 新备忘录
{new_memo}
"""
```



