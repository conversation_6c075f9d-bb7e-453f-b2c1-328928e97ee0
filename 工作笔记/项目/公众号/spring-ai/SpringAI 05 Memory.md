## 一、聊天记忆的概念与重要性

>大型语言模型（LLM）本质上是无状态的，这意味着它们不会保留有关之前交互的信息。当您希望在多个交互中维护上下文或状态时，这可能是一个限制。

为了解决这个问题，Spring AI提供了聊天记忆（Chat Memory）抽象，允许您在与LLM的多次交互中存储和检索信息。

### 1.1 聊天记忆的好处

1. **上下文连贯性**：AI能够参考之前的对话内容，提供连贯的回复，而不是每次都从零开始。
2. **个性化体验**：记住用户的偏好、背景和历史交互，提供更加个性化的服务。
3. **减少重复信息**：用户无需在每次交互中重复提供相同的信息。
4. **复杂任务处理**：支持跨多个交互的复杂任务完成，如分步骤指导或信息收集。

### 1.2 应用场景

聊天记忆功能在多种场景中都有重要应用：

- **客户服务机器人**：记住客户的问题和已提供的信息，避免让客户重复描述问题。
- **虚拟助手**：记住用户的偏好设置、日常习惯和常用指令，提供个性化服务。
- **多轮预订系统**：在预订酒店、机票等过程中，记住用户已选择的日期、偏好等信息。
- **技术支持**：记住用户的系统配置和已尝试的解决方案，提供递进式的故障排除。

### 1.3 聊天记忆与聊天历史的区别

在深入了解Spring AI的聊天记忆功能之前，我们需要明确两个概念：

- **聊天记忆（Chat Memory）**：大语言模型用来保持上下文感知的信息，这些信息会影响模型的响应。
- **聊天历史（Chat History）**：用户和模型之间交换的所有消息的完整记录。

Spring AI的`ChatMemory`抽象专门用于管理聊天记忆，而不是完整的聊天历史。如果你需要存储完整的聊天历史，可能需要考虑使用Spring Data 等其他持久化方式。

---

## 二、记忆类型

Spring AI提供了不同类型的记忆实现，以适应不同的使用场景：

### 2.1 消息窗口记忆（MessageWindowChatMemory）

`MessageWindowChatMemory`维护一个固定大小的消息窗口，当消息数量超过最大值时，会移除较旧的消息（同时保留系统消息）。默认窗口大小为20条消息。

```java
MessageWindowChatMemory memory = MessageWindowChatMemory.builder()
    .maxMessages(10)  // 设置最大消息数为10
    .build();
```

这是Spring AI默认使用的记忆类型。

---

## 三、记忆存储方式

Spring AI提供了`ChatMemoryRepository`抽象用于存储聊天记忆。以下是Spring AI提供的内置存储库：

### 3.1 内存存储（InMemoryChatMemoryRepository）

`InMemoryChatMemoryRepository`使用`ConcurrentHashMap`在内存中存储消息，适用于开发和测试环境。

```java
ChatMemoryRepository repository = new InMemoryChatMemoryRepository();
```

### 3.2 JDBC存储（JdbcChatMemoryRepository）

`JdbcChatMemoryRepository`使用JDBC将消息存储在关系数据库中，支持多种数据库，适用于需要持久化存储聊天记忆的应用。

```java
// 添加依赖
// spring-ai-starter-model-chat-memory-repository-jdbc

// 使用自动配置
@Autowired
JdbcChatMemoryRepository chatMemoryRepository;

// 创建聊天记忆
ChatMemory chatMemory = MessageWindowChatMemory.builder()
    .chatMemoryRepository(chatMemoryRepository)
    .maxMessages(10)
    .build();
```

#### 3.2.1 自定义MySQL存储库实现

如果需要为MySQL数据库创建自定义存储库，可以扩展`JdbcChatMemoryRepository`类：

```java
public class MysqlChatMemoryRepository extends JdbcChatMemoryRepository {
    // MySQL特定的查询语句
    private static final String MYSQL_QUERY_ADD = "INSERT INTO ai_chat_memory (conversation_id, content, type, timestamp) VALUES (?, ?, ?, ?)";
    private static final String MYSQL_QUERY_GET = "SELECT content, type FROM ai_chat_memory WHERE conversation_id = ? ORDER BY timestamp";
    
    // 构造函数和构建器方法...
    
    @Override
    protected String hasTableSql(String tableName) {
        return String.format("SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = '%s'", tableName);
    }
    
    @Override
    protected String createTableSql(String tableName) {
        return String.format("CREATE TABLE %s (id BIGINT AUTO_INCREMENT PRIMARY KEY, conversation_id VARCHAR(256) NOT NULL, content LONGTEXT NOT NULL, type VARCHAR(100) NOT NULL, timestamp TIMESTAMP NOT NULL)", tableName);
    }
    
    // 其他必要的方法重写...
}
```

### 3.3 Cassandra存储

`CassandraChatMemoryRepository`使用Apache Cassandra存储消息，适用于需要高可用性、持久性和可扩展性的应用。

#### 3.3.1 特点与优势

- 时间序列架构：保留所有过去的聊天窗口记录
- 支持TTL：可以设置消息的生存时间
- 高可用性：Cassandra的分布式特性提供了高可用性

### 3.4 Neo4j存储

`Neo4jChatMemoryRepository`使用Neo4j将聊天消息作为节点和关系存储在图数据库中，适用于想要利用图形功能进行聊天记忆持久化的应用。

---

## 四、在应用中使用聊天记忆

### 4.1 在ChatClient中使用聊天记忆

Spring AI提供了几种内置的Advisor用于配置`ChatClient`的记忆行为：

#### 4.1.1 MessageChatMemoryAdvisor

将对话历史作为消息集合包含在提示中：

```java
ChatMemory chatMemory = MessageWindowChatMemory.builder().build();
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultAdvisors(MessageChatMemoryAdvisor.builder(chatMemory).build())
    .build();
```

#### 4.1.2 使用示例

```java
// 定义会话ID
String conversationId = "user123";

// 第一次对话
String response1 = chatClient.prompt()
    .user("你好，我叫小明")
    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
    .call()
    .content();

// 下一次对话（使用相同的会话ID）
String response2 = chatClient.prompt()
    .user("你还记得我的名字吗？")
    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
    .call()
    .content();  // AI会回答：你是小明
```

### 4.2 在ChatModel中使用聊天记忆

如果直接使用`ChatModel`，可以显式管理记忆：

```java
// 创建记忆实例
ChatMemory chatMemory = MessageWindowChatMemory.builder().build();
String conversationId = "user123";

// 第一次交互
UserMessage userMessage1 = new UserMessage("你好，我叫小明");
chatMemory.add(conversationId, userMessage1);
ChatResponse response1 = chatModel.call(new Prompt(chatMemory.get(conversationId)));
chatMemory.add(conversationId, response1.getResult().getOutput());

// 第二次交互
UserMessage userMessage2 = new UserMessage("你还记得我的名字吗？");
chatMemory.add(conversationId, userMessage2);
ChatResponse response2 = chatModel.call(new Prompt(chatMemory.get(conversationId)));
```

---

## 五、注意事项

在实现聊天记忆功能时，请考虑以下最佳实践：

### 5.1 记忆大小管理

根据您的用例选择适当的记忆大小。过大的记忆可能导致token消耗过多，而过小的记忆可能导致上下文丢失。

### 5.2 存储选择

在生产环境中使用持久化存储（如JDBC、Cassandra或Neo4j）而不是内存存储，以确保数据不会因服务重启而丢失。

### 5.3 清理机制

为旧对话实现适当的清理机制，避免存储空间无限增长。可以考虑设置TTL或定期清理不活跃的会话。

### 5.4 安全性考虑

确保适当的数据保护和隐私措施，特别是在存储用户敏感信息时。

---