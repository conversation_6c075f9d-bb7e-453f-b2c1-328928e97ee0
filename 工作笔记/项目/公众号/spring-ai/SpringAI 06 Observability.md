# Spring AI Alibaba 可观测性实践指南

>在构建企业级AI应用时，可观测性(Observability)是确保系统可靠性和可维护性的关键要素。

Spring AI 提供了全面的可观测性支持，让开发者能够深入了解AI组件的运行状态、性能指标以及潜在问题。

## 什么是可观测性？

可观测性(Observability)是指通过系统输出（如日志、指标、跟踪等）来推断系统内部状态的能力。在AI应用中，可观测性尤为重要，它能帮助开发者:

- 监控模型调用性能和延迟
- 追踪请求流程和依赖关系
- 分析模型输入输出内容
- 快速定位和解决问题

Spring AI Alibaba基于Spring生态系统，为各种AI组件提供了全面的可观测性支持，包括`ChatClient`(含ChatModel、Advisor、ToolCall等)、`EmbeddingModel`、`ImageModel`和`VectorStore`等。

## 为什么需要添加可观测性？

在AI应用开发和运维过程中，添加可观测性功能至关重要，主要原因包括：

1. **模型调用透明度**：AI模型通常是黑盒，可观测性帮助开发者了解输入输出和处理过程，提高系统透明度。

2. **性能监控与优化**：通过收集模型调用的性能指标（如响应时间、Token使用量），可以识别性能瓶颈并进行优化。

3. **成本控制**：AI模型调用通常按Token计费，可观测性可以帮助监控和控制API调用成本。

4. **问题诊断与排查**：当系统出现异常时，可观测性数据可以帮助快速定位问题根源，减少排障时间。

5. **安全与合规**：记录模型输入输出，有助于审计和确保系统符合安全与合规要求。

6. **用户体验优化**：通过监控响应时间等指标，可以持续优化用户体验。

## 观察哪些数据？

Spring AI Alibaba的可观测性框架主要收集以下几类数据：

### 1. 基础调用信息
- **操作类型**：如chat、embedding、image generation等
- **模型标识**：使用的具体模型名称和版本
- **请求ID**：唯一标识每次调用的ID
- **时间戳**：调用开始和结束时间

### 2. 性能指标
- **响应时间**：模型调用的总耗时
- **Token使用量**：输入Token数、输出Token数和总Token数
- **请求大小**：输入数据的大小
- **响应大小**：输出数据的大小

### 3. 内容数据（可选）
- **提示词内容**：发送给模型的提示词
- **响应内容**：模型返回的完整响应
- **错误信息**：调用失败时的详细错误信息

### 4. 工具调用数据
- **工具名称**：被调用的工具名称
- **参数内容**：传递给工具的参数
- **返回结果**：工具执行的结果
- **执行状态**：成功、失败或超时等状态

## Spring AI Alibaba 可观测性实现

### 1. 添加核心依赖

要启用Spring AI Alibaba的可观测性功能，只需添加以下核心依赖：

```xml
<dependencies>
    <!-- Spring Boot Actuator - 提供可观测性基础设施 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <!-- Spring AI Alibaba 核心依赖 -->
    <dependency>
        <groupId>com.alibaba.cloud.ai</groupId>
        <artifactId>spring-ai-alibaba-starter-dashscope</artifactId>
    </dependency>

    <!-- 可观测性追踪依赖（按需添加） -->
    <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-bridge-brave</artifactId>
    </dependency>
    
    <!-- Zipkin集成所需依赖 -->
    <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-brave</artifactId>
        <version>3.4.3</version>
    </dependency>
</dependencies>
```

### 2. 配置可观测性

在`application.yml`中配置可观测性相关参数：

```yaml
spring:
  ai:
    dashscope:
      observations:
        log-completion: true  # 记录模型完成内容
        log-prompt: true      # 记录提示词内容
    
    # 聊天配置项
    chat:
      client:
        observations:
          log-prompt: true           # 默认为false
          log-completion: true       # 默认为false
          include-error-logging: true
  
  # 工具配置项
  tools:
    observability:
      include-content: true   # 默认为false
      
  # Zipkin配置
  zipkin:
    base-url: http://localhost:9411  # Zipkin服务器地址
    enabled: true
    sender:
      type: web  # 使用HTTP发送跟踪数据

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 暴露所有端点
  tracing:
    sampling:
      probability: 1.0  # 采样率100%
```

### 3. 实现AI服务

以下是一个简单的ChatClient控制器示例：

```java
@RestController
@RequestMapping("/observability/chat")
public class ChatModelController {
  private final ChatClient chatClient;
  
  public ChatModelController(ChatClient.Builder builder) {
    this.chatClient = builder.build();
  }
  
  @GetMapping
  public Flux<String> chat(@RequestParam(defaultValue = "hi") String prompt) {
    return chatClient.prompt(prompt).stream().content();
  }
}
```

## 整合Zipkin进行分布式追踪

Zipkin是一个分布式追踪系统，可以帮助收集解决微服务架构中的延迟问题所需的时序数据。Spring AI Alibaba可以与Zipkin无缝集成，实现AI组件调用的全链路追踪。

### 1. 启动Zipkin服务器

使用Docker快速启动Zipkin服务器：

```bash
docker run -d -p 9411:9411 openzipkin/zipkin
```

或者使用Docker Compose：

```yaml
services:
  zipkin:
    image: 'openzipkin/zipkin:latest'
    ports:
      - '9411:9411'
```

### 2. 配置应用程序连接Zipkin

在应用程序的配置文件中添加Zipkin相关配置：

```yaml
spring:
  zipkin:
    base-url: http://localhost:9411
    enabled: true
  sleuth:
    sampler:
      probability: 1.0  # 采样率，1.0表示100%采样
```

### 3. Zipkin中的观测数据

在Zipkin UI中（默认地址为http://localhost:9411），可以查看以下关键信息：

- **服务依赖关系**：可视化展示AI服务与其他服务的调用关系
- **请求追踪**：完整的请求处理链路，包括AI模型调用
- **时间分布**：各阶段耗时分析，帮助识别性能瓶颈
- **异常标记**：失败的请求会被特别标记，便于快速定位问题

### 4. 追踪数据示例

在Zipkin中，一个典型的Spring AI调用追踪可能包含以下Span：

- `http:/observability/chat` - 接收HTTP请求的控制器
- `gen_ai.client.operation` - ChatClient的操作
- `gen_ai.model.call` - 底层模型API调用
- `tool.call` - 工具调用（如果有）

每个Span都包含详细的标签信息，如：

```
gen_ai.request.model: qwen-plus
gen_ai.usage.input_tokens: 9
gen_ai.usage.output_tokens: 7
gen_ai.response.finish_reasons: ["STOP"]
```

通过这些信息，开发者可以全面了解AI请求的处理过程、性能特征和资源消耗。

### 5. Zipkin观测的优势

- **端到端可视化**：从用户请求到AI模型调用的完整链路可视化
- **性能分析**：精确定位延迟问题和性能瓶颈
- **异常定位**：快速识别和定位失败请求
- **系统拓扑**：自动发现和展示系统组件之间的依赖关系
- **历史数据**：保存历史追踪数据，便于趋势分析和对比

## 可观测性输出

### ChatClient 可观测性

ChatClient的可观测性输出主要包括：

```
name='gen_ai.client.operation', 
contextualName='chat qwen-plus', 
lowCardinalityKeyValues=[
  gen_ai.operation.name='chat', 
  gen_ai.request.model='qwen-plus', 
  gen_ai.response.model='none', 
  gen_ai.system='dashscope'
], 
highCardinalityKeyValues=[
  gen_ai.request.temperature='0.7', 
  gen_ai.response.finish_reasons='["STOP"]', 
  gen_ai.response.id='9582b50a-4056-9b7e-b2ca-e52368406b5e', 
  gen_ai.usage.input_tokens='9', 
  gen_ai.usage.output_tokens='7', 
  gen_ai.usage.total_tokens='16'
]
```

这些数据可以帮助开发者了解：
- 使用了哪个模型(qwen-plus)
- 模型参数设置(temperature=0.7)
- 响应结束原因(STOP)
- Token使用情况(输入9个，输出7个，共16个)

### ToolCalling 可观测性

工具调用的可观测性输出包括工具名称、参数、执行结果等关键信息，帮助开发者追踪工具调用的执行过程和性能表现。

## 扩展Spring AI的可观测性

Spring AI提供了`ObservationHandler<ChatModelObservationContext>`机制，允许开发者扩展和自定义可观测性信息：

```java
public class CustomerObservationHandler implements ObservationHandler<ChatModelObservationContext> {
    @Override
    public void onStart(ChatModelObservationContext context) {
        System.out.println("执行CustomerObservationHandler onStart函数! ChatModelObservationContext: " + context.toString());
    }

    @Override
    public void onStop(ChatModelObservationContext context) {
        System.out.println("执行CustomerObservationHandler onStop函数! ChatModelObservationContext: " + context.toString());
    }

    @Override
    public boolean supportsContext(Observation.Context context) {
        return true;
    }
}
```

在控制器中使用自定义观测处理器：

```java
@RestController
@RequestMapping("/custom/observation/chat")
public class ChatModelController {
    @GetMapping
    public String chat(@RequestParam(defaultValue = "hi") String message) {
       ObservationRegistry registry = ObservationRegistry.create();
       registry.observationConfig().observationHandler(new CustomerObservationHandler());

       return DashScopeChatModel.builder()
             .dashScopeApi(DashScopeApi.builder().apiKey(System.getenv("AI_DASHSCOPE_API_KEY")).build())
             .observationRegistry(registry)
             .build()
             .call(message);
    }
}
```

## 实践建议

1. **选择性开启内容记录**：`log-prompt`和`log-completion`会记录完整的提示词和响应内容，可能包含敏感信息，建议在生产环境谨慎使用。

2. **关注关键指标**：重点监控Token使用量和响应时间，这些直接关系到成本和用户体验。

3. **集成告警系统**：将异常情况（如高延迟、错误率上升）接入告警系统，实现及时响应。

4. **数据可视化**：利用Grafana等工具将观测数据可视化，便于分析趋势和模式。

5. **定期审计**：定期审查观测数据，识别优化机会和潜在风险。

6. **合理配置采样率**：在高流量系统中，可以适当降低采样率，减少对系统性能的影响。

## 总结

Spring AI Alibaba的可观测性功能为企业级AI应用提供了全面的监控和诊断能力。通过与Zipkin等工具的集成，开发者可以实现AI组件调用的全链路追踪，更好地了解AI组件的行为，控制成本，提高系统可靠性，并为用户提供更优质的服务体验。

在实际项目中，建议根据业务需求和系统规模，选择适当的可观测性策略，平衡监控的全面性和系统性能的影响。

## 参考资料

- [Spring AI Observability文档](https://docs.spring.io/spring-ai/reference/observability/index.html)
- [Spring AI Alibaba可观测性最佳实践](https://java2ai.com/docs/*******/practices/observability/observability/)
- [Zipkin官方文档](https://zipkin.io/) 