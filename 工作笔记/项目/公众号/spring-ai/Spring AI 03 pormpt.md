# Spring AI 中的提示词工程技术

## 提示词(Prompt)的定义与机制

提示词(Prompt)是向大型语言模型(LLM)提供的输入文本，作为模型生成输出的先验条件和指导框架。从计算语言学和自然语言处理的角度看，提示词可被视为一种特殊的上下文条件，它激活模型预训练期间获得的特定知识表征和推理路径。提示词不仅仅是简单的输入，而是一种复杂的语言接口，通过它人类可以与基于Transformer架构的语言模型进行有效交互。

在技术层面，提示词通过影响模型的注意力机制和token预测概率分布，引导模型生成符合特定意图的输出。当提示词被输入到模型时，它首先被标记化(tokenized)并映射到模型的嵌入空间，然后通过自注意力层和前馈神经网络层传播，最终影响模型的输出层预测。

## 提示词工程的技术重要性

提示词工程(Prompt Engineering)是一门系统化设计、优化和评估提示词的技术学科，它在现代AI系统开发中具有以下关键技术价值：

1. **参数效率优化**：提示词工程允许开发者在不修改模型参数的情况下，显著提升模型在特定任务上的性能。这种"软提示"(soft prompting)方法比完整的模型微调更加资源高效，尤其对于参数规模达到数十亿或数千亿的大型模型而言。

2. **上下文学习能力增强**：精心设计的提示词可以激活模型的上下文学习(in-context learning)能力，使模型能够从少量示例中快速适应新任务，实现所谓的"少样本学习"(few-shot learning)甚至"零样本学习"(zero-shot learning)。

3. **推理路径引导**：通过结构化提示词，可以引导模型沿着特定的推理路径进行思考，如"思维链"(Chain-of-Thought)提示技术，这显著提高了模型在复杂推理任务上的准确性。

4. **输出格式控制**：提示词工程使开发者能够精确控制模型输出的格式、结构和语义约束，这对于构建可靠的AI系统至关重要，特别是在需要与其他软件组件集成时。

5. **多模态交互框架**：在多模态AI系统中，提示词工程提供了一种统一的框架，用于协调文本、图像、音频等不同模态之间的交互和信息传递。

在Spring AI框架中，提示词工程被系统化为一套结构化的API和模式，使Java开发者能够以声明式和类型安全的方式实现复杂的提示词策略，同时保持代码的可维护性和可测试性。

这篇文章演示了如何使用 Spring AI 来实现提示词工程技术的实际应用。

本文中的示例和模式基于全面的提示词工程指南，该指南涵盖了有效提示词工程的理论、原则和模式。

这篇文章展示了如何使用 Spring AI 流畅的 ChatClient API 将这些概念转化为可运行的 Java 代码。

为方便起见，示例的结构遵循了原始指南中概述的相同模式和技术。

本文中使用的演示源代码可从以下地址获取： [spring-ai-examples/prompt-engineering-patterns](https://github.com/spring-projects/spring-ai-examples/tree/main/prompt-engineering/prompt-engineering-patterns)

## 1. 配置

配置部分概述了如何使用 Spring AI 设置和调整您的大型语言模型 (LLM)。它涵盖了为您的用例选择合适的 LLM 提供商，以及配置控制模型输出质量、风格和格式的重要生成参数。

### LLM 提供商选择

对于提示词工程，您将首先选择一个模型。Spring AI 支持多个 LLM 提供商（例如 OpenAI、Anthropic、Google Vertex AI、AWS Bedrock、Ollama 等），这允许您无需更改应用程序代码即可切换提供商——只需更新您的配置即可。只需添加选定的启动器依赖项 `spring-ai-starter-model-<MODEL-PROVIDER-NAME>`。例如，以下是如何启用 Anthropic Claude API：

```xml
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-model-anthropic</artifactId>
</dependency>
```

以及一些连接属性：
```
spring.ai.anthropic.api-key=${ANTHROPIC_API_KEY}
```

您可以像这样指定特定的 LLM 模型名称：

```java
.options(ChatOptions.builder()
        .model("claude-3-7-sonnet-latest")  // 使用 Anthropic 的 Claude 模型
        .build())
```

有关启用和配置首选 AI 模型的详细信息，请参阅参考文档。

### LLM 输出配置

在我们深入探讨提示词工程技术之前，了解如何配置 LLM 的输出行为至关重要。Spring AI 提供了几个配置选项，允许您通过 ChatOptions 构建器控制生成的各个方面。

所有配置都可以按以下示例所示通过编程方式应用，或者在启动时通过 Spring 应用程序属性应用。

#### Temperature（温度）

Temperature 参数控制模型响应的随机性或"创造力"。

* **较低的值 (0.0-0.3)**：更具确定性、更集中的响应。适用于事实类问题、分类或对一致性要求严格的任务。
* **中等值 (0.4-0.7)**：在确定性和创造力之间取得平衡。适用于一般用例。
* **较高值 (0.8-1.0)**：更具创造力、多样化且可能带来惊喜的响应。适用于创意写作、头脑风暴或生成多样化选项。

```java
.options(ChatOptions.builder()
        .temperature(0.1)  // 非常确定性的输出
        .build())
```

理解 Temperature 参数对于提示词工程至关重要，因为不同的技术会受益于不同的 Temperature 设置。

#### 输出长度 (MaxTokens)

`maxTokens` 参数限制了模型在其响应中可以生成的 token（词片段）数量。

* **较低值 (5-25)**：适用于单个词、短语或分类标签。
* **中等值 (50-500)**：适用于段落或简短解释。
* **较高值 (1000+)**：适用于长篇内容、故事或复杂解释。

```java
.options(ChatOptions.builder()
        .maxTokens(250)  // 中等长度响应
        .build())
```

设置适当的输出长度很重要，以确保您获得完整的响应而不会出现不必要的冗余。

#### 采样控制 (Top-K 和 Top-P)

这些参数允许您对生成过程中的 token 选择进行细粒度控制。

* **Top-K**：将 token 选择限制在 K 个最有可能的下一个 token 中。较高的值（例如 40-50）会引入更多多样性。
* **Top-P (核采样)**：从累积概率超过 P 的最小 token 集合中动态选择。常见的值如 0.8-0.95。

```java
.options(ChatOptions.builder()
        .topK(40)      // 仅考虑概率最高的 40 个 token
        .topP(0.8)     // 从覆盖 80% 概率质量的 token 中采样
        .build())
```

这些采样控制与 Temperature 协同工作，以塑造响应特征。

#### 结构化响应格式

除了纯文本响应（使用 `.content()`）外，Spring AI 还支持使用 `.entity()` 方法将 LLM 响应直接映射到 Java 对象。

```java
enum Sentiment {
    POSITIVE, NEUTRAL, NEGATIVE
}

Sentiment result = chatClient.prompt("...")
        .call()
        .entity(Sentiment.class);
```

当与指示模型返回结构化数据的系统提示词结合使用时，此功能尤其强大。

#### 模型特定选项

虽然可移植的 `ChatOptions` 为不同的 LLM 提供商提供了一致的接口，但 Spring AI 也提供了模型特定选项类，这些类暴露了提供商特定的功能和配置。这些模型特定选项允许您利用每个 LLM 提供商的独特能力。

```java
// 使用 OpenAI 特定选项
OpenAiChatOptions openAiOptions = OpenAiChatOptions.builder()
        .model("gpt-4o")
        .temperature(0.2)
        .frequencyPenalty(0.5)      // OpenAI 特定参数
        .presencePenalty(0.3)       // OpenAI 特定参数
        .responseFormat(new ResponseFormat("json_object"))  // OpenAI 特定 JSON 模式
        .seed(42)                   // OpenAI 特定确定性生成
        .build();

String result = chatClient.prompt("...")
        .options(openAiOptions)
        .call()
        .content();

// 使用 Anthropic 特定选项
AnthropicChatOptions anthropicOptions = AnthropicChatOptions.builder()
        .model("claude-3-7-sonnet-latest")
        .temperature(0.2)
        .topK(40)                   // Anthropic 特定参数
        .build();
```

## 2. 核心提示词工程技术

提示词工程是一门通过精心设计的输入来引导大型语言模型生成所需输出的艺术和科学。以下是几种核心提示词工程技术，以及如何使用 Spring AI 实现它们。

### 2.1 Zero-Shot Prompting（零样本提示）

Zero-Shot Prompting 是最直接的提示方法，不需要提供任何示例。模型直接基于指令执行任务，依靠其预训练知识。

```java
public void zeroShotPrompting(ChatClient chatClient) {
    String result = chatClient
            .prompt()
            .user(u -> u.text("将以下句子翻译成法语：'人工智能正在改变世界。'"))
            .call()
            .content();
    
    // 输出: "L'intelligence artificielle est en train de changer le monde."
}
```

Zero-Shot Prompting 适用于简单任务或当模型对任务有充分理解时。它的优点是简单直接，但对于复杂或模糊任务可能效果不佳。

### 2.2 One-Shot & Few-Shot Prompting（单样本和少样本提示）

这些技术通过在提示中包含一个或多个示例来指导模型。示例帮助模型理解任务的具体格式和期望输出。

#### One-Shot Prompting（单样本提示）

```java
public void oneShotPrompting(ChatClient chatClient) {
    String result = chatClient
            .prompt()
            .user(u -> u.text("""
                    将英文句子翻译成法语：
                    
                    英文: The weather is beautiful today.
                    法语: Le temps est magnifique aujourd'hui.
                    
                    英文: Artificial intelligence is changing the world.
                    法语:
                    """))
            .call()
            .content();
    
    // 输出: "L'intelligence artificielle est en train de changer le monde."
}
```

#### Few-Shot Prompting（少样本提示）

```java
public void fewShotPrompting(ChatClient chatClient) {
    String result = chatClient
            .prompt()
            .user(u -> u.text("""
                    将以下文本分类为正面、负面或中性：
                    
                    文本: "这家餐厅的食物很美味。"
                    情感: 正面
                    
                    文本: "服务太慢了，我等了一个小时。"
                    情感: 负面
                    
                    文本: "价格合理，但环境一般。"
                    情感: 中性
                    
                    文本: "我对这个产品的质量感到失望。"
                    情感:
                    """))
            .call()
            .content();
    
    // 输出: "负面"
}
```

Few-Shot Prompting 特别适合需要模型遵循特定格式或需要额外上下文的任务。通过提供多个示例，模型可以更好地理解模式和期望。

### 2.3 Chain-of-Thought Prompting（思维链提示）

Chain-of-Thought Prompting 鼓励模型展示其推理过程，通过引导模型一步一步地思考问题，从而提高复杂推理任务的准确性。

```java
public void chainOfThoughtPrompting(ChatClient chatClient) {
    String result = chatClient
            .prompt()
            .user(u -> u.text("""
                    问题: 如果一件衬衫原价80元，先打8折，然后又打9折，最终价格是多少？
                    
                    让我们一步步思考：
                    1. 原价是80元
                    2. 打8折后的价格是 80 × 0.8 = 64元
                    3. 再打9折后的价格是 64 × 0.9 = 57.6元
                    4. 所以最终价格是57.6元
                    
                    问题: 如果一本书原价120元，先打7折，然后又打85折，最终价格是多少？
                    
                    让我们一步步思考：
                    """))
            .call()
            .content();
}
```

这种技术特别适合需要多步推理的数学问题、逻辑推理或决策任务。通过展示思考过程，模型不仅提供了答案，还提供了如何得出答案的解释。

### 2.4 Role Prompting（角色提示）

Role Prompting 通过为模型分配特定角色来引导其响应风格和内容。这种技术可以显著改变模型的输出风格和专业性。

```java
public void rolePrompting(ChatClient chatClient) {
    String result = chatClient
            .prompt()
            .system(s -> s.text("你是一位经验丰富的Java开发专家，专注于Spring框架。请以简洁、专业的方式回答问题，并提供代码示例。"))
            .user(u -> u.text("如何在Spring Boot应用中实现基本的认证功能？"))
            .call()
            .content();
}
```

角色提示可以帮助模型采用特定的语气、风格或专业知识水平，使其输出更符合特定场景的需求。

### 2.5 Self-Consistency（自我一致性）

Self-Consistency 技术通过生成多个独立的推理路径，然后选择最一致的答案来提高复杂推理任务的准确性。

```java
public void selfConsistencyPrompting(ChatClient chatClient) {
    // 生成多个推理路径
    List<String> responses = new ArrayList<>();
    
    for (int i = 0; i < 3; i++) {
        String response = chatClient
                .prompt()
                .user(u -> u.text("""
                        问题: 小明有5个苹果，他给了小红2个，又从小华那里得到3个，然后他又吃了1个。小明现在有多少个苹果？
                        
                        让我们一步步思考得出答案。
                        """))
                .options(ChatOptions.builder().temperature(0.7).build()) // 使用较高的温度以获得多样化的推理路径
                .call()
                .content();
        
        responses.add(response);
    }
    
    // 在实际应用中，您可能需要解析这些响应并找出最一致的答案
    // 这里我们只是演示概念
}
```

这种方法特别适用于复杂的推理任务，其中单一推理路径可能导致错误。通过考虑多个可能的推理路径，可以提高最终答案的可靠性。

## 3. 代码提示示例

代码提示是提示词工程中一个强大的应用领域，尤其适用于代码解释、翻译和生成任务。

### 解释代码示例

```java
public void pt_code_prompting_explaining_code(ChatClient chatClient) {
    String code = """
            #!/bin/bash
            echo "Enter the folder name: "
            read folder_name
            if [ ! -d "$folder_name" ]; then
            echo "Folder does not exist."
            exit 1
            fi
            files=( "$folder_name"/* )
            for file in "${files[@]}"; do
            new_file_name="draft_$(basename "$file")"
            mv "$file" "$new_file_name"
            done
            echo "Files renamed successfully."
            """;

    String explanation = chatClient
            .prompt()
            .user(u -> u.text("""
                    Explain to me the below Bash code:
```
​                    {code}
                    ```
                    """).param("code", code))
            .call()
            .content();
}
                    ```

### 代码翻译示例

```java
public void pt_code_prompting_translating_code(ChatClient chatClient) {
    String bashCode = """
            #!/bin/bash
            echo "Enter the folder name: "
            read folder_name
            if [ ! -d "$folder_name" ]; then
            echo "Folder does not exist."
            exit 1
            fi
            files=( "$folder_name"/* )
            for file in "${files[@]}"; do
            new_file_name="draft_$(basename "$file")"
            mv "$file" "$new_file_name"
            done
            echo "Files renamed successfully."
            """;

    String pythonCode = chatClient
            .prompt()
            .user(u -> u.text("""
                    Translate the below Bash code to a Python snippet:                        
                    {code}                        
                    """).param("code", bashCode))
            .call()
            .content();
}
```

代码提示对于自动化代码文档、原型设计、学习编程概念以及在编程语言之间进行翻译特别有价值。通过将其与少样本提示或思维链等技术结合使用，可以进一步增强其有效性。

**参考文献：** Chen, M., 等。(2021)。《Evaluating Large Language Models Trained on Code》。arXiv:2107.03374。[https://arxiv.org/abs/2107.03374](https://arxiv.org/abs/2107.03374)

## 结论

Spring AI 提供了一个优雅的 Java API，用于实现所有主要的提示词工程技术。通过将这些技术与 Spring 强大的实体映射和流畅的 API 相结合，开发者可以构建具有简洁、可维护代码的复杂 AI 驱动应用程序。

最有效的方法通常涉及结合多种技术——例如，将系统提示与少样本示例结合使用，或将思维链与角色提示结合使用。Spring AI 灵活的 API 使这些组合易于实现。

对于生产应用程序，请记住：

1. 测试不同参数（temperature, top-k, top-p）下的提示词
2. 考虑在关键决策中使用自我一致性
3. 利用 Spring AI 的实体映射实现类型安全的响应
4. 使用上下文提示提供应用程序特定知识

通过这些技术和 Spring AI 强大的抽象能力，您可以创建提供一致、高质量结果的强大 AI 驱动应用程序。

## 参考文献

1. Brown, T. B., 等。(2020)。《Language Models are Few-Shot Learners》。arXiv:2005.14165。
2. Wei, J., 等。(2022)。《Chain-of-Thought Prompting Elicits Reasoning in Large Language Models》。arXiv:2201.11903。
3. Wang, X., 等。(2022)。《Self-Consistency Improves Chain of Thought Reasoning in Language Models》。arXiv:2203.11171。
4. Yao, S., 等。(2023)。《Tree of Thoughts: Deliberate Problem Solving with Large Language Models》。arXiv:2305.10601。
5. Zhou, Y., 等。(2022)。《Large Language Models Are Human-Level Prompt Engineers》。arXiv:2211.01910。
6. Zheng, Z., 等。(2023)。《Take a Step Back: Evoking Reasoning via Abstraction in Large Language Models》。arXiv:2310.06117。
7. Liu, P., 等。(2021)。《What Makes Good In-Context Examples for GPT-3?》。arXiv:2101.06804。
8. Shanahan, M., 等。(2023)。《与大型语言模型的角色扮演》。arXiv:2305.16367。
9. Chen, M., 等。(2021)。《评估基于代码训练的大型语言模型》。arXiv:2107.03374。
10. Spring AI 文档
11. ChatClient API 参考
12. Google 提示工程指南 x