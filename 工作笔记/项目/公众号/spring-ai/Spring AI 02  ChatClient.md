# Spring AI 之 Chat Client：高级对话交互API

Spring AI中的Chat Client提供了一套完整的API，用于简化与AI模型的交互过程，支持同步和反应式编程模型，帮助开发者轻松构建智能对话应用。

## 一、Chat Client核心概念

> ChatClient是Spring AI中的高级组件，它封装了与LLM和相关组件交互的复杂性，提供了简洁的接口，使开发者能够快速构建AI驱动的对话系统。

<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-8 -8 396.8125 406" style="max-width: 396.8125px;" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-svg-1750170647969-i0c76lmqg"><style>#mermaid-svg-1750170647969-i0c76lmqg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:rgba(204, 204, 204, 0.87);}#mermaid-svg-1750170647969-i0c76lmqg .error-icon{fill:#bf616a;}#mermaid-svg-1750170647969-i0c76lmqg .error-text{fill:#bf616a;stroke:#bf616a;}#mermaid-svg-1750170647969-i0c76lmqg .edge-thickness-normal{stroke-width:2px;}#mermaid-svg-1750170647969-i0c76lmqg .edge-thickness-thick{stroke-width:3.5px;}#mermaid-svg-1750170647969-i0c76lmqg .edge-pattern-solid{stroke-dasharray:0;}#mermaid-svg-1750170647969-i0c76lmqg .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-svg-1750170647969-i0c76lmqg .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-svg-1750170647969-i0c76lmqg .marker{fill:rgba(204, 204, 204, 0.87);stroke:rgba(204, 204, 204, 0.87);}#mermaid-svg-1750170647969-i0c76lmqg .marker.cross{stroke:rgba(204, 204, 204, 0.87);}#mermaid-svg-1750170647969-i0c76lmqg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-svg-1750170647969-i0c76lmqg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:rgba(204, 204, 204, 0.87);}#mermaid-svg-1750170647969-i0c76lmqg .cluster-label text{fill:#ffffff;}#mermaid-svg-1750170647969-i0c76lmqg .cluster-label span,#mermaid-svg-1750170647969-i0c76lmqg p{color:#ffffff;}#mermaid-svg-1750170647969-i0c76lmqg .label text,#mermaid-svg-1750170647969-i0c76lmqg span,#mermaid-svg-1750170647969-i0c76lmqg p{fill:rgba(204, 204, 204, 0.87);color:rgba(204, 204, 204, 0.87);}#mermaid-svg-1750170647969-i0c76lmqg .node rect,#mermaid-svg-1750170647969-i0c76lmqg .node circle,#mermaid-svg-1750170647969-i0c76lmqg .node ellipse,#mermaid-svg-1750170647969-i0c76lmqg .node polygon,#mermaid-svg-1750170647969-i0c76lmqg .node path{fill:#1a1a1a;stroke:#2a2a2a;stroke-width:1px;}#mermaid-svg-1750170647969-i0c76lmqg .flowchart-label text{text-anchor:middle;}#mermaid-svg-1750170647969-i0c76lmqg .node .label{text-align:center;}#mermaid-svg-1750170647969-i0c76lmqg .node.clickable{cursor:pointer;}#mermaid-svg-1750170647969-i0c76lmqg .arrowheadPath{fill:#e5e5e5;}#mermaid-svg-1750170647969-i0c76lmqg .edgePath .path{stroke:rgba(204, 204, 204, 0.87);stroke-width:2.0px;}#mermaid-svg-1750170647969-i0c76lmqg .flowchart-link{stroke:rgba(204, 204, 204, 0.87);fill:none;}#mermaid-svg-1750170647969-i0c76lmqg .edgeLabel{background-color:#1a1a1a99;text-align:center;}#mermaid-svg-1750170647969-i0c76lmqg .edgeLabel rect{opacity:0.5;background-color:#1a1a1a99;fill:#1a1a1a99;}#mermaid-svg-1750170647969-i0c76lmqg .labelBkg{background-color:rgba(26, 26, 26, 0.5);}#mermaid-svg-1750170647969-i0c76lmqg .cluster rect{fill:rgba(64, 64, 64, 0.47);stroke:#30373a;stroke-width:1px;}#mermaid-svg-1750170647969-i0c76lmqg .cluster text{fill:#ffffff;}#mermaid-svg-1750170647969-i0c76lmqg .cluster span,#mermaid-svg-1750170647969-i0c76lmqg p{color:#ffffff;}#mermaid-svg-1750170647969-i0c76lmqg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:#88c0d0;border:1px solid #30373a;border-radius:2px;pointer-events:none;z-index:100;}#mermaid-svg-1750170647969-i0c76lmqg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:rgba(204, 204, 204, 0.87);}#mermaid-svg-1750170647969-i0c76lmqg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="6" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1750170647969-i0c76lmqg_flowchart-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1750170647969-i0c76lmqg_flowchart-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1750170647969-i0c76lmqg_flowchart-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart" id="mermaid-svg-1750170647969-i0c76lmqg_flowchart-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1750170647969-i0c76lmqg_flowchart-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart" id="mermaid-svg-1750170647969-i0c76lmqg_flowchart-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"/><g class="edgePaths"><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-B" id="L-A-B-0" d="M89.789,88L102.127,76.5C114.464,65,139.138,42,155.642,30.5C172.146,19,180.479,19,184.646,19L188.813,19"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-C" id="L-A-C-0" d="M138.813,107L142.979,107C147.146,107,155.479,107,165.146,107C174.813,107,185.813,107,191.313,107L196.813,107"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-E" id="L-C-E-0" d="M232.511,88L239.895,76.5C247.278,65,262.045,42,274.929,30.5C287.813,19,298.813,19,304.313,19L309.813,19"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-F" id="L-C-F-0" d="M243.813,107L249.313,107C254.813,107,265.813,107,278.146,107C290.479,107,304.146,107,310.979,107L317.813,107"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-G" id="L-C-G-0" d="M232.511,126L239.895,137.5C247.278,149,262.045,172,276.262,183.5C290.479,195,304.146,195,310.979,195L317.813,195"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-D" id="L-A-D-0" d="M76.201,126L90.803,166.833C105.405,207.667,134.609,289.333,154.711,330.167C174.813,371,185.813,371,191.313,371L196.813,371"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-D LE-H" id="L-D-H-0" d="M232.511,352L239.895,340.5C247.278,329,262.045,306,273.596,294.5C285.146,283,293.479,283,297.646,283L301.813,283"/><path style="fill:none;" class="edge-thickness-normal edge-pattern-solid flowchart-link LS-D LE-I" id="L-D-I-0" d="M243.813,371L249.313,371C254.813,371,265.813,371,275.479,371C285.146,371,293.479,371,297.646,371L301.813,371"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: inline-block; white-space: nowrap;" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(69.40625, 107)" id="flowchart-A-401" class="node default default flowchart-label"><rect height="38" width="138.8125" y="-19" x="-69.40625" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-61.90625, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="123.8125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">Chat API核心概念</span></div></foreignObject></g></g><g transform="translate(220.3125, 19)" id="flowchart-B-402" class="node default default flowchart-label"><rect height="38" width="63" y="-19" x="-31.5" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-24, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="48"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">客户端</span></div></foreignObject></g></g><g transform="translate(220.3125, 107)" id="flowchart-C-404" class="node default default flowchart-label"><rect height="38" width="47" y="-19" x="-23.5" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-16, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="32"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">请求</span></div></foreignObject></g></g><g transform="translate(341.3125, 19)" id="flowchart-E-406" class="node default default flowchart-label"><rect height="38" width="63" y="-19" x="-31.5" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-24, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="48"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">提示词</span></div></foreignObject></g></g><g transform="translate(341.3125, 107)" id="flowchart-F-408" class="node default default flowchart-label"><rect height="38" width="47" y="-19" x="-23.5" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-16, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="32"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">消息</span></div></foreignObject></g></g><g transform="translate(341.3125, 195)" id="flowchart-G-410" class="node default default flowchart-label"><rect height="38" width="47" y="-19" x="-23.5" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-16, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="32"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">选项</span></div></foreignObject></g></g><g transform="translate(220.3125, 371)" id="flowchart-D-412" class="node default default flowchart-label"><rect height="38" width="47" y="-19" x="-23.5" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-16, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="32"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">响应</span></div></foreignObject></g></g><g transform="translate(341.3125, 283)" id="flowchart-H-414" class="node default default flowchart-label"><rect height="38" width="79" y="-19" x="-39.5" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-32, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="64"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">响应对象</span></div></foreignObject></g></g><g transform="translate(341.3125, 371)" id="flowchart-I-416" class="node default default flowchart-label"><rect height="38" width="79" y="-19" x="-39.5" ry="0" rx="0" style="fill:black;stroke:#666;" class="basic label-container"/><g transform="translate(-32, -11.5)" style="color:white;" class="label"><rect/><foreignObject height="23" width="64"><div xmlns="http://www.w3.org/1999/xhtml" style="color: white; display: inline-block; white-space: nowrap;"><span style="color:white;" class="nodeLabel">生成结果</span></div></foreignObject></g></g></g></g></g></svg>

与底层的`ChatModel`、`Message`、`ChatMemory`等原子API相比，ChatClient更像是应用程序中的服务层，它为开发者提供了直接的"AI服务"，并隐藏了诸多底层细节：

- **提示词管理**：定制和组装模型的输入（Prompt）
- **响应处理**：格式化解析模型的输出（Structured Output）
- **参数选项**：调整模型交互参数（ChatOptions）
- **高级功能**：整合聊天记忆、函数调用、RAG等能力

## 二、创建ChatClient实例

> ChatClient实例是通过Builder模式创建的，Spring AI提供了两种方式：使用自动配置的Builder或编程方式创建。

### 2.1 使用自动配置的ChatClient.Builder

Spring Boot会自动配置`ChatClient.Builder`的Bean，你可以直接注入并使用它：

```java
@RestController
public class ChatController {
    private final ChatClient chatClient;

    public ChatController(ChatClient.Builder builder) {
        this.chatClient = builder.build();
    }

    @GetMapping("/chat")
    public String chat(String input) {
        return this.chatClient.prompt()
            .user(input)
            .call()
            .content();
    }
}
```

在这个示例中，我们设置了用户消息内容，通过`call()`方法向AI模型发送请求，再通过`content()`方法以字符串形式获取AI模型的响应。

### 2.2 编程方式创建ChatClient

如果你需要使用多个不同的聊天模型，可以通过编程方式创建多个ChatClient实例：

1. 首先禁用自动配置：设置属性`spring.ai.chat.client.enabled=false`
2. 然后通过编程方式创建ChatClient：

```java
// 通常通过自动装配获取ChatModel实例
ChatModel myChatModel = ...

// 使用Builder创建ChatClient
ChatClient.Builder builder = ChatClient.builder(myChatModel);
ChatClient chatClient = builder.build();

// 或者直接使用快捷方式
ChatClient chatClient = ChatClient.create(myChatModel);
```

## 三、ChatClient流畅API

> ChatClient提供了流畅的API风格，让开发者能够以链式调用的方式构建与AI模型的交互逻辑。

ChatClient的流畅API通过重载的`prompt`方法提供了三种不同的方式来创建提示：

1. **prompt()**：无参数方法，允许从头开始构建提示，可以后续添加用户消息、系统消息等。
   ```java
   chatClient.prompt().user("你好").call().content();
   ```

2. **prompt(Prompt prompt)**：接受一个`Prompt`对象参数，允许传入使用非流畅API创建的`Prompt`实例。
   ```java
   Prompt myPrompt = new Prompt(List.of(new UserMessage("你好")));
   chatClient.prompt(myPrompt).call().content();
   ```

3. **prompt(String content)**：便捷方法，直接接受用户文本内容作为参数。
   ```java
   chatClient.prompt("你好").call().content();
   ```

这种流畅的API设计使得与AI模型的交互变得直观且易于阅读，特别适合链式构建复杂的提示和处理响应。

## 四、处理ChatClient响应

> ChatClient API提供了多种方法来处理和格式化AI模型的响应，以适应不同的应用场景。

### 4.1 获取原始ChatResponse

`ChatResponse`包含了完整的响应数据和元数据（如token使用情况）：

```java
ChatResponse chatResponse = chatClient.prompt()
    .user("讲一个笑话")
    .call()
    .chatResponse();
```

通过`chatResponse()`方法可以获取完整的响应对象，从而访问更丰富的信息。

### 4.2 返回实体类（Entity）

Spring AI可以自动将AI模型的文本响应转换为Java对象，这在需要结构化数据时非常有用：

```java
// 定义实体类
record ActorFilms(String actor, List<String> movies) {}

// 将AI响应直接转换为实体类
ActorFilms actorFilms = chatClient.prompt()
    .user("生成一个随机演员的电影作品列表")
    .call()
    .entity(ActorFilms.class);
```

`entity()`方法会自动将AI模型的输出映射到指定的类型上，简化了解析工作。

`entity()`方法还提供了一个重载版本，允许传入自定义参数来指导实体转换过程：

```java
record Product(String name, String description, Double price) {}

Product product = chatClient.prompt()
    .user("创建一个价格在100元以内的产品")
    .call()
    .entity(Product.class, EntityPreference.create()
        .withFormat(EntityFormat.JSON)
        .build());
```

通过`EntityPreference`对象，你可以指定实体转换的格式、解析策略和其他选项。

### 4.3 流式响应

对于需要实时反馈的场景，可以使用流式API：

```java
Flux<String> responseStream = chatClient.prompt()
    .user("给我讲一个长故事")
    .stream()
    .content();
```

流式API让用户可以看到响应的逐步生成过程，提供类似ChatGPT那样的实时交互体验。

## 五、ChatClient返回值

> ChatClient提供了两个核心方法来获取响应：`call()`和`stream()`，前者用于同步响应，后者用于流式响应。

### 5.1 call()方法

`call()`方法返回一个`ResponseWithChatClient`对象，它提供了多种处理响应的方式：

```java
// 获取文本内容
String content = chatClient.prompt()
    .user("什么是Spring AI？")
    .call()
    .content();

// 获取完整的ChatResponse
ChatResponse response = chatClient.prompt()
    .user("什么是Spring AI？")
    .call()
    .chatResponse();

// 转换为实体类
ProductInfo info = chatClient.prompt()
    .user("介绍一款产品")
    .call()
    .entity(ProductInfo.class);
```

### 5.2 stream()方法

`stream()`方法返回一个`StreamWithChatClient`对象，支持流式处理：

```java
// 获取文本内容的流
Flux<String> contentFlux = chatClient.prompt()
    .user("写一篇长文章")
    .stream()
    .content();

// 获取ChatResponse的流
Flux<ChatResponse> responseFlux = chatClient.prompt()
    .user("写一篇长文章")
    .stream()
    .chatResponses();
```

## 六、定制ChatClient默认值

> ChatClient支持设置各种默认值，帮助开发者减少重复配置，提升开发效率。

### 6.1 设置默认系统消息

系统消息可以为AI模型提供角色定位和行为指南：

```java
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultSystem("你是一位专业的Java开发助手，擅长使用Spring框架")
    .build();
```

### 6.2 其他默认设置

除了系统消息，还可以设置其他默认参数：

```java
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultUser("默认用户消息")
    .defaultOptions(options -> options.withTemperature(0.7f))
    .defaultResponseFormat(new StructuredResponseFormat<>(MyResponseType.class))
    .build();
```

## 七、使用示例






> 参考资料：
>
> [Spring AI Alibaba - Chat Client](https://java2ai.com/docs/1.0.0-M6.1/tutorials/chat-client/?spm=4347728f.6bbdfafa.0.0.69ee175cIDqUaS)
>
> [Spring AI Chat Client API](https://www.spring-doc.cn/spring-ai/1.0.0-M8/api_chatclient.en.html) 