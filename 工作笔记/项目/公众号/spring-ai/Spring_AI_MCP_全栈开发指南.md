# Spring AI MCP全栈开发指南：构建高效智能体的标准化数据连接方案

> 在AI智能体开发中，如何让模型安全高效地访问各类数据源和工具？Spring AI的MCP（Model Context Protocol）提供了完整的客户端-服务端解决方案，通过标准化协议实现AI模型与外部系统的无缝集成。本文深入解析MCP的核心组件和开发流程，帮助开发者快速构建强大的智能体应用。

## 引言：为什么需要MCP？

在构建AI应用时，我们经常面临这样的挑战：

- 模型需要访问**实时数据**（如天气、股票价格）
- 模型需要操作**外部系统**（如数据库、文件系统）
- 模型需要调用**专有API**（如企业内部服务）

传统的Function Calling虽然能解决部分问题，但缺乏标准化的协议和安全隔离机制。**MCP（Model Context Protocol）**作为一个开放标准，提供了更加完整、安全和灵活的解决方案。

## 一、MCP核心架构与组件

[模型上下文协议（Model Context Protocol，MCP）](https://modelcontextprotocol.io)是一个开放协议，它规范了应用程序如何向大型语言模型（LLM）提供上下文。MCP提供了一种统一的方式将AI模型连接到不同的数据源和工具，定义了标准化的集成方式。

![Spring AI MCP架构图](mcp-architecture.svg)

Spring AI的MCP实现采用了客户端-服务端架构，主要包含五个核心组件：

### 1.1 MCP客户端（Client）

MCP客户端是Spring AI应用与MCP服务器之间的桥梁，负责：

- 发现并管理与MCP服务器的连接（与服务器保持1:1连接）
- 将服务器提供的工具转换为Spring AI可用的ToolCallback
- 处理工具调用请求并返回结果
- 支持同步和异步操作模式

### 1.2 MCP服务器（Server）

MCP服务器是功能提供方，负责：

- 暴露特定功能（工具、资源、提示模板）
- 处理来自客户端的请求
- 执行实际的业务逻辑
- 返回执行结果

### 1.3 MCP工具（Tools）

MCP工具是服务器提供的功能单元，可以是：

- 信息检索工具（如数据库查询、文件读取）
- 操作执行工具（如发送邮件、更新记录）
- 资源访问工具（如读取配置、获取模板）

### 1.4 本地数据源（Local Data Sources）

MCP服务器可以安全访问的本地资源：

- 计算机文件系统
- 本地数据库
- 系统服务和应用

### 1.5 远程服务（Remote Services）

MCP服务器可以通过互联网连接的外部系统：

- REST API
- 云服务
- 第三方平台

## 二、MCP客户端开发指南

### 2.1 客户端Starter简介

Spring AI提供了两种MCP客户端Starter：

```xml
<!-- 标准MCP客户端 - 支持STDIO和SSE传输 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-mcp-client</artifactId>
</dependency>

<!-- WebFlux MCP客户端 - 基于响应式编程 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-mcp-client-webflux</artifactId>
</dependency>
```

### 2.2 客户端核心功能

1. **多客户端管理**：支持同时连接多个MCP服务器
2. **自动初始化**：自动处理客户端连接和初始化
3. **多传输层支持**：
   - **STDIO**：进程内通信，适合本地部署
   - **SSE**：基于HTTP的服务器发送事件，适合远程部署
4. **工具集成**：自动将MCP工具转换为Spring AI的ToolCallback
5. **生命周期管理**：自动清理资源

### 2.3 传输层详解

Spring AI MCP支持两种不同的传输层实现：基于stdio的实现和基于SSE的实现。

#### 2.3.1 stdio传输层

stdio（标准输入输出）传输层是MCP最基本的传输实现方式，通过进程间通信（IPC）实现：

- **进程创建**：MCP客户端会启动一个子进程来运行MCP服务器
- **通信机制**：使用标准输入（stdin）发送请求，标准输出（stdout）接收响应
- **优点**：简单可靠，无需网络配置，进程隔离安全性好
- **缺点**：仅支持单机部署，不支持跨网络访问

#### 2.3.2 SSE传输层

SSE（Server-Sent Events）传输层是基于HTTP的单向通信机制：

- **连接建立**：客户端通过HTTP建立与服务器的持久连接
- **通信机制**：服务器可以主动向客户端推送消息，支持自动重连
- **优点**：支持分布式部署，可跨网络访问，支持多客户端连接
- **缺点**：需要额外的网络配置，相比stdio实现略微复杂

### 2.4 客户端配置示例

```yaml
spring:
  ai:
    mcp:
      client:
        # 通用配置
        enabled: true
        name: my-mcp-client
        version: 1.0.0
        request-timeout: 30s
        type: SYNC  # 或ASYNC

        # STDIO传输配置
        stdio:
          servers-configuration: classpath:/mcp-servers.json
          # 或直接配置
          connections:
            filesystem:
              command: npx
              args:
                - -y
                - "@modelcontextprotocol/server-filesystem"
                - "/path/to/files"
              env:
                API_KEY: ${API_KEY}

        # SSE传输配置
        sse:
          connections:
            weather:
              url: http://localhost:8080
              sse-endpoint: /sse
```

#### 2.4.1 Claude Desktop格式配置

MCP支持使用Claude Desktop格式的JSON配置文件：

```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Desktop",
        "/Users/<USER>/Downloads"
      ]
    }
  }
}
```

### 2.5 客户端自定义

Spring AI提供了强大的客户端自定义能力，通过实现`McpSyncClientCustomizer`或`McpAsyncClientCustomizer`接口：

```java
@Component
public class CustomMcpClientCustomizer implements McpSyncClientCustomizer {
    @Override
    public void customize(String serverName, McpClient.SyncSpec spec) {
        // 自定义请求超时
        spec.requestTimeout(Duration.ofSeconds(30));

        // 设置根目录访问权限
        spec.roots(roots);

        // 设置采样处理器（LLM采样功能）
        spec.sampling((CreateMessageRequest req) -> {
            // 处理采样请求，允许服务器请求LLM生成内容
            return result;
        });

        // 工具变更通知
        spec.toolsChangeConsumer((List<McpSchema.Tool> tools) -> {
            // 处理工具变更
        });

        // 资源变更通知
        spec.resourcesChangeConsumer((List<McpSchema.Resource> resources) -> {
            // 处理资源变更
        });

        // 提示模板变更通知
        spec.promptsChangeConsumer((List<McpSchema.Prompt> prompts) -> {
            // 处理提示模板变更
        });

        // 日志处理器
        spec.loggingConsumer((McpSchema.LoggingMessageNotification log) -> {
            // 处理服务器日志消息
        });
    }
}
```

## 三、MCP服务器开发指南

### 3.1 服务器Starter简介

Spring AI提供了三种MCP服务器Starter：

```xml
<!-- 标准MCP服务器 - 仅STDIO传输 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-mcp-server-spring-boot-starter</artifactId>
</dependency>

<!-- WebMVC MCP服务器 - 基于Spring MVC -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-mcp-server-webmvc</artifactId>
</dependency>

<!-- WebFlux MCP服务器 - 基于响应式编程 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-mcp-server-webflux</artifactId>
</dependency>
```

### 3.2 服务器核心功能

1. **工具暴露**：将Spring Bean转换为MCP工具
2. **资源管理**：提供标准化的资源访问机制
3. **提示模板**：支持提示模板的版本管理和变更通知
4. **完成（Completion）**：提供代码补全等功能
5. **多传输层**：支持STDIO和SSE传输

### 3.3 服务器配置示例

```yaml
spring:
  ai:
    mcp:
      server:
        # 通用配置
        enabled: true
        name: weather-mcp-server
        version: 1.0.0
        type: SYNC  # 或ASYNC
        stdio: false  # 是否启用STDIO传输
        
        # 功能配置
        capabilities:
          tool: true
          resource: true
          prompt: true
          completion: true
        
        # 变更通知
        tool-change-notification: true
        resource-change-notification: true
        prompt-change-notification: true
        
        # Web传输配置
        sse-endpoint: /sse
        sse-message-endpoint: /mcp/message
        base-url: /api/v1
```

### 3.4 服务器工具实现

使用`@Tool`注解将方法转换为MCP工具：

```java
@Service
public class WeatherService {

    @Tool(description = "获取指定城市的天气信息")
    public String getWeather(
        @ToolParameter(description = "城市名称，如北京、上海") String city) {
        
        // 调用天气API获取数据
        WeatherData data = weatherApi.getWeatherByCity(city);
        
        // 返回格式化结果
        return String.format("%s当前温度%s°C，%s，湿度%s%%",
            city, data.getTemperature(), data.getCondition(), data.getHumidity());
    }
    
    @Tool(description = "获取未来几天的天气预报")
    public String getWeatherForecast(
        @ToolParameter(description = "城市名称") String city,
        @ToolParameter(description = "预报天数，1-7", required = false) Integer days) {
        
        // 默认预报3天
        if (days == null) days = 3;
        
        // 获取天气预报
        List<ForecastData> forecast = weatherApi.getForecast(city, days);
        
        // 格式化结果
        StringBuilder result = new StringBuilder();
        result.append(city).append("未来").append(days).append("天天气预报：\n");
        
        for (ForecastData day : forecast) {
            result.append(day.getDate()).append("：")
                  .append(day.getCondition()).append("，")
                  .append(day.getTemperatureMin()).append("°C至")
                  .append(day.getTemperatureMax()).append("°C\n");
        }
        
        return result.toString();
    }
}
```

### 3.5 服务器资源实现

MCP服务器可以提供资源访问功能，允许客户端读取服务器上的资源：

```java
@Bean
public List<McpServerFeatures.SyncResourceSpecification> systemResources() {
    // 定义资源
    var systemInfoResource = new McpSchema.Resource(
        "system-info", "系统信息资源", "获取当前系统状态信息");

    // 创建资源规范
    var resourceSpec = new McpServerFeatures.SyncResourceSpecification(
        systemInfoResource,
        (exchange, request) -> {
            // 获取系统信息
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("cpu", Runtime.getRuntime().availableProcessors());
            systemInfo.put("memory", Runtime.getRuntime().totalMemory());
            systemInfo.put("freeMemory", Runtime.getRuntime().freeMemory());
            systemInfo.put("timestamp", System.currentTimeMillis());

            // 转换为JSON
            String jsonContent = new ObjectMapper().writeValueAsString(systemInfo);

            // 返回资源内容
            return new McpSchema.ReadResourceResult(
                List.of(new McpSchema.TextResourceContents(
                    request.uri(), "application/json", jsonContent)));
        });

    return List.of(resourceSpec);
}
```

### 3.6 服务器提示模板实现

MCP服务器可以提供提示模板功能，允许客户端使用预定义的提示模板：

```java
@Bean
public List<McpServerFeatures.SyncPromptSpecification> myPrompts() {
    // 定义提示模板
    var prompt = new McpSchema.Prompt(
        "greeting",
        "友好问候提示模板",
        List.of(new McpSchema.PromptArgument("name", "要问候的名字", true))
    );

    // 创建提示模板规范
    var promptSpec = new McpServerFeatures.SyncPromptSpecification(
        prompt,
        (exchange, getPromptRequest) -> {
            // 获取参数
            String nameArg = (String) getPromptRequest.arguments().get("name");
            if (nameArg == null) {
                nameArg = "朋友";
            }

            // 创建用户消息
            var userMessage = new PromptMessage(
                Role.USER,
                new TextContent("你好 " + nameArg + "！我能为你做些什么？")
            );

            // 返回提示模板结果
            return new GetPromptResult(
                "个性化问候消息",
                List.of(userMessage)
            );
        }
    );

    return List.of(promptSpec);
}
```

## 四、MCP工具集成与使用

### 4.1 工具适配器

Spring AI提供了工具适配器，将MCP工具转换为Spring AI的ToolCallback：

```java
// 获取MCP客户端
McpSyncClient mcpClient = ...

// 获取工具列表
List<McpSchema.Tool> tools = mcpClient.listTools(null).tools();

// 创建工具适配器
List<ToolCallback> callbacks = tools.stream()
    .map(tool -> new SyncMcpToolCallback(mcpClient, tool))
    .collect(Collectors.toList());

// 使用工具
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultFunctions(callbacks.toArray(new ToolCallback[0]))
    .build();

// 与AI交互
String response = chatClient.prompt("北京今天天气如何？").call().content();
```

### 4.2 工具提供者

更简便的方式是使用工具提供者：

```java
// 获取MCP客户端
McpSyncClient mcpClient = ...

// 创建工具提供者
ToolCallbackProvider provider = new SyncMcpToolCallbackProvider(mcpClient);

// 获取所有工具
ToolCallback[] tools = provider.getToolCallbacks();

// 使用工具
ChatClient chatClient = ChatClient.builder(chatModel)
    .defaultTools(tools)
    .build();
```

### 4.3 工具上下文

MCP工具支持上下文传递，可以在工具调用时传递额外信息：

```java
@Tool(description = "获取用户信息")
public UserInfo getUserInfo(String userId, ToolContext toolContext) {
    // 从上下文获取租户ID
    String tenantId = toolContext.get("tenantId");
    
    // 获取MCP交换对象
    McpSyncServerExchange exchange = McpToolUtils.getMcpExchange(toolContext);
    
    // 发送日志通知
    exchange.loggingNotification(
        McpSchema.LogLevel.INFO, 
        "Retrieving user info for " + userId + " in tenant " + tenantId);
    
    // 执行业务逻辑
    return userService.findUser(tenantId, userId);
}
```

## 五、实战案例：文件系统智能体

### 5.1 案例概述

这个案例演示如何使用Spring AI MCP访问本地文件系统，构建一个能够查询和操作文件的智能体。

### 5.2 环境准备

首先确保安装了必要的工具：

```bash
# 安装npx (Node Package eXecute)
npm install -g npx

# 设置环境变量
export AI_DASHSCOPE_API_KEY=${your-api-key-here}
```

### 5.3 项目配置

添加必要的依赖：

```xml
<dependency>
    <groupId>org.springframework.experimental</groupId>
    <artifactId>spring-ai-mcp</artifactId>
    <version>0.2.0</version>
</dependency>

<!-- 添加Spring milestone仓库 -->
<repositories>
    <repository>
        <id>spring-milestones</id>
        <name>Spring Milestones</name>
        <url>https://repo.spring.io/libs-milestone-local</url>
    </repository>
</repositories>
```

### 5.4 MCP客户端实现

```java
@SpringBootApplication
public class FileSystemMcpExample {

    public static void main(String[] args) {
        SpringApplication.run(FileSystemMcpExample.class, args);
    }

    @Bean(destroyMethod = "close")
    public McpSyncClient mcpClient() {
        // 配置文件系统MCP服务器启动参数
        var stdioParams = ServerParameters.builder("npx")
                .args("-y", "@modelcontextprotocol/server-filesystem", "/path/to/files")
                .build();

        // 创建基于stdio的MCP客户端
        var mcpClient = McpClient.sync(
            new StdioServerTransport(stdioParams),
            Duration.ofSeconds(10),
            new ObjectMapper()
        );

        // 初始化连接
        var init = mcpClient.initialize();
        System.out.println("MCP初始化完成: " + init);

        return mcpClient;
    }

    @Bean
    public List<McpFunctionCallback> functionCallbacks(McpSyncClient mcpClient) {
        // 获取MCP服务器中的工具列表并转换为Spring AI Function
        return mcpClient.listTools(null)
                .tools()
                .stream()
                .map(tool -> new McpFunctionCallback(mcpClient, tool))
                .toList();
    }

    @Bean
    public CommandLineRunner demo(
            ChatClient.Builder chatClientBuilder,
            List<McpFunctionCallback> functionCallbacks) {
        return args -> {
            // 构建集成MCP工具的ChatClient
            var chatClient = chatClientBuilder
                    .defaultFunctions(functionCallbacks.toArray(new McpFunctionCallback[0]))
                    .build();

            // 与AI交互，AI会自动调用文件系统工具
            String response = chatClient
                    .prompt("请帮我查看当前目录下有哪些文件？")
                    .call()
                    .content();

            System.out.println("AI回复: " + response);
        };
    }
}
```

### 5.5 SQLite数据库访问案例

除了文件系统，MCP还支持数据库访问：

```java
@Bean(destroyMethod = "close")
public McpSyncClient sqliteMcpClient() {
    // 使用uvx启动SQLite MCP服务器
    var stdioParams = ServerParameters.builder("uvx")
            .args("mcp-server-sqlite", "--db-path", "/path/to/database.db")
            .build();

    var mcpClient = McpClient.sync(
        new StdioServerTransport(stdioParams),
        Duration.ofSeconds(10),
        new ObjectMapper()
    );

    mcpClient.initialize();
    return mcpClient;
}
```

**使用效果**：
```
用户: 所有商品的价格总和是多少？
AI: 所有商品的价格总和是1642.8元。

用户: 告诉我价格高于平均值的商品
AI: 以下是价格高于平均值的商品：
1. Smart Watch，价格为 199.99 元
2. Wireless Earbuds，价格为 89.99 元
3. Mini Drone，价格为 299.99 元
...
```

## 六、使用Starter简化开发

### 6.1 MCP客户端Starter

Spring AI提供了两种MCP客户端Starter来简化开发：

```xml
<!-- 标准MCP客户端 - 支持STDIO和SSE传输 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-mcp-client-spring-boot-starter</artifactId>
</dependency>

<!-- WebFlux MCP客户端 - 基于响应式编程 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-mcp-client-webflux-spring-boot-starter</artifactId>
</dependency>
```

#### 6.1.1 基于stdio的客户端配置

```yaml
spring:
  ai:
    dashscope:
      api-key: ${DASH_SCOPE_API_KEY}
    mcp:
      client:
        stdio:
          servers-configuration: classpath:/mcp-servers-config.json
```

#### 6.1.2 基于SSE的客户端配置

```yaml
spring:
  ai:
    dashscope:
      api-key: ${DASH_SCOPE_API_KEY}
    mcp:
      client:
        sse:
          connections:
            server1:
              url: http://localhost:8080
```

### 6.2 MCP服务端Starter

Spring AI提供了三种MCP服务端Starter：

```xml
<!-- 标准MCP服务器 - 仅STDIO传输 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-mcp-server-spring-boot-starter</artifactId>
</dependency>

<!-- WebMVC MCP服务器 - 基于Spring MVC -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-mcp-server-webmvc-spring-boot-starter</artifactId>
</dependency>

<!-- WebFlux MCP服务器 - 基于响应式编程 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-mcp-server-webflux-spring-boot-starter</artifactId>
</dependency>
```

#### 6.2.1 stdio服务端配置

```yaml
spring:
  main:
    web-application-type: none  # 必须禁用web应用类型
  ai:
    mcp:
      server:
        stdio: true
        name: my-weather-server
        version: 0.0.1
```

#### 6.2.2 WebFlux服务端配置

```yaml
server:
  port: 8080
spring:
  ai:
    mcp:
      server:
        name: my-weather-server
        version: 0.0.1
```

## 七、最佳实践与性能优化

### 7.1 客户端最佳实践

1. **选择合适的传输层**：
   - 本地部署：使用STDIO传输
   - 远程部署：使用SSE传输
   - 高并发场景：使用WebFlux实现

2. **异步处理**：
   - 对于耗时操作，使用异步客户端
   - 设置合理的超时时间

3. **工具管理**：
   - 按功能域划分工具
   - 提供详细的工具描述

### 7.2 服务器最佳实践

1. **安全考虑**：
   - 实现访问控制
   - 验证输入参数
   - 限制资源访问范围

2. **性能优化**：
   - 使用缓存减少重复计算
   - 实现连接池管理
   - 异步处理耗时操作

3. **错误处理**：
   - 提供友好的错误信息
   - 实现重试机制
   - 记录详细日志

### 7.3 部署策略

1. **stdio模式**：
   - 适合嵌入式场景
   - 作为客户端的子进程运行
   - 进程隔离，安全性好

2. **SSE模式**：
   - 适合作为独立服务部署
   - 可以被多个客户端调用
   - 支持分布式架构

## 总结

Spring AI的MCP实现提供了一套完整的客户端-服务器解决方案，通过标准化协议实现AI模型与外部系统的无缝集成。通过本文介绍的核心组件和开发流程，开发者可以快速构建强大的智能体应用，让AI模型安全高效地访问各类数据源和工具。

MCP的关键优势在于：

1. **标准化协议**：统一的接口规范，降低集成复杂度
2. **安全隔离**：进程级别的安全隔离，保障系统安全
3. **灵活部署**：支持本地和远程部署，适应不同场景
4. **丰富功能**：工具、资源、提示模板等全方位支持
5. **易于集成**：与Spring生态无缝集成，开发体验一流

随着AI应用的普及，MCP将成为连接AI模型与现实世界的重要桥梁，为智能体开发提供坚实的技术基础。

---

*本文基于Spring AI 1.0.0官方文档编写，更多详细信息请参考[Spring AI官方文档](https://docs.spring.io/spring-ai/reference/api/mcp/mcp-overview.html)。*
