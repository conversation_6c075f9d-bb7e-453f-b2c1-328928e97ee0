# Spring AI：从0到1由浅入深学习

Spring AI是一个用于AI工程的应用框架。它的目标是将Spring生态系统的设计原则（如可移植性和模块化设计）应用到AI领域，并促进使用POJOs（Plain Old Java Objects）作为应用程序构建块的实践。



本系列文章将从0到1，由浅入深地介绍Spring AI的核心架构和主要组件，快速掌握这一强大的AI应用开发框架。

## 一、Spring AI的核心组件

### 1. 模型（Models）

> AI模型本质上是一种算法，通过从海量数据中学习模式和洞察力，这些模型能够产生预测、文本、图像等多种输出。

![](https://mmbiz.qpic.cn/sz_mmbiz_png/EwoZcpq2icK0Jickd1mlxh1OLd1egCkSrXSYk7bWn7YGEFlWibTiaoT4ccZfp7Eghmj5OPkWm20jyRT8CuutQ9ib7zg/0?wx_fmt=png)

目前Spirng AI 支持Models
- **对话模型（Chat Model）**：适用于文本生成、对话等场景，如GPT模型和通义千问等
- **嵌入模型（Embedding Model）**：将文本转换为向量表示，用于相似性搜索和语义理解
- **排序模型（Rerank Model）**：对检索结果进行精细化排序，提高结果相关性，常用于RAG系统的二次排序阶段



### 2. 提示工程（Prompt Engineering）
>提示工程是与AI模型交流的艺术，就像我们需要用合适的方式向他人提问才能得到期望的答案一样。

Spring AI中，提示模板就像是一张"填空题"卷子，我们预先设计好问题的结构，留出需要填写的空白，然后在运行时根据具体情况填入不同的内容。

```java
Spring AI中运行示例
// 创建提示模板
PromptTemplate promptTemplate = new PromptTemplate("请以{format}格式分析{subject}的主要特点和应用场景");
// 使用变量创建实际提示
Prompt prompt = promptTemplate.create(Map.of(
    "subject", "Spring AI",
    "format", "结构化表格"
));
```

### 3. 检索增强生成（RAG）

>RAG（Retrieval-Augmented Generation）检索增强生成

RAG作为Spring AI中的关键功能，它允许AI模型基于外部知识来增强回答的准确性和相关性。RAG的工作流程清晰地分为两个阶段：离线处理和在线查询
![](https://mmbiz.qpic.cn/sz_mmbiz_png/EwoZcpq2icK0Jickd1mlxh1OLd1egCkSrXUB8P1alv0rSj93G1uk6PTGQSargDotw8bQnJlQicGpkqr19bSdghYhg/0?wx_fmt=png)

RAG工作流程简述：
1. 将文档预处理并存入向量数据库
2. 用户查询时检索相关文档片段
3. 将检索结果与用户查询一起提交给模型
4. 生成基于检索内容的准确响应

### 4. 函数调用（Function Calling）

>在深入了解Spring AI中的函数调用机制前，我们需要理解为什么这项功能如此重要。

大语言模型（LLMs）在训练完成后就被"冻结"了，这导致两个主要限制：一是知识会随时间陈旧，二是无法访问或修改外部数据。这使得模型难以处理需要实时信息或系统交互的任务。

工具调用（Tool Calling）机制正是为解决以上问题而设计的。它允许开发者将自己的服务注册为"工具"，从而将大语言模型连接到外部系统的API。

这些系统可以为模型提供实时数据，并代表模型执行数据处理操作。

![](https://mmbiz.qpic.cn/sz_mmbiz_png/EwoZcpq2icK0Jickd1mlxh1OLd1egCkSrXMzlVHfSkyCVCDLftETm1WqHYZzNwHxTT2rnThviawx0Y896ylorat5A/0?wx_fmt=png)

```java
Spring AI中运行示例
@Bean
public Tool getCurrentPriceTool() {
    return Tool.builder()
        .name("get_current_price")
        .description("获取指定产品的当前价格信息")
        .input(PriceRequest.class)
        .run(request -> priceService.getCurrentPrice(request.getProductId()))
        .build();
}
```

函数调用使AI应用能够：
1. 访问实时数据（如市场价格、天气信息）
2. 执行系统操作（如预订服务、发送通知）
3. 查询企业系统数据（如客户信息、产品库存）

### 5. 结构化输出（Structured Output）

>当我们使用AI模型时，默认情况下它们会返回自由格式的文本响应。然而，在实际应用程序中，我们通常需要结构化数据而不是原始文本。这就是Spring AI中结构化输出功能的价值所在。

![](https://mmbiz.qpic.cn/sz_mmbiz_png/EwoZcpq2icK0Jickd1mlxh1OLd1egCkSrXZMNK3qdftqMCXKic9SS5I2NnYpQwRsqXwAPOqpXUqh930kjTV8DnEew/0?wx_fmt=png)

## 为什么需要结构化输出？

即使您在提示中明确要求AI模型"以JSON格式返回"或"返回JSON数据"，模型的输出仍然会以`java.lang.String`的形式出现。这个字符串可能包含格式正确的JSON内容，但它本质上仍然只是一个字符串，而不是可以直接在Java应用中使用的对象。

此外，仅在提示中要求"返回JSON"并非100%可靠，模型可能依然会：
- 返回不符合JSON语法的内容
- 在JSON前后添加额外说明文本

Spring AI支持将模型生成的文本直接映射到Java对象，简化后续处理逻辑：

```java
Spring AI中运行示例
// 定义结构化的响应类型
public record ProductAnalysis(String productName, String category, 
                           List<String> features, double marketScore) {}

// 将模型输出直接映射为Java对象
ProductAnalysis analysis = chatClient.call(
    "分析产品X的市场表现和主要特点", 
    ProductAnalysis.class
);
```

这种方式消除了手动解析文本的需求，大大提高了开发效率和代码可维护性。

## 二、后期工作规划与展望

在了解了Spring AI的基本架构和核心组件后，我们后续将围绕以下几个方向深入探索，以实现Spring AI在企业级应用中的落地和创新：

### 一、核心组件深度剖析

我们计划对Spring AI的核心组件进行更深入的剖析

1. **模型层适配机制研究**
   - 研究实现自定义模型适配器的最佳实践
   - 探索多模型混合调用的实现方案

2. **提示工程高级技巧**
   - 构建企业级提示模板库
   - 通过Nacos动态管理配置Prompt

3. **向量存储方案对比**
   - 研究向量索引优化策略
   - 探索混合检索方案的实现

### 二、企业级RAG系统开发

我们将构建一个完整的企业级RAG应用系统，包括：

1. **多源数据处理管道**
   - 构建支持PDF、Word、HTML、Markdown等多种格式的文档处理流水线
   - 实现增量更新和版本控制机制
   - 开发自动化文档质量评估和优化组件

2. **高级检索优化**
   - 实现混合检索策略（关键词+语义）
   - 开发上下文感知的检索优化器
   - 构建检索结果重排序和聚类功能

3. **RAG系统评估框架**
   - 构建自动化测试套件
   - 开发RAG性能指标体系
   - 实现人类反馈收集和模型迭代机制
---

> 参考资料：
>
> [Spring AI 官方文档](https://spring.io/projects/spring-ai)