# RAG检索增强生成与Agent智能体核心术语解析

> 在人工智能快速发展的今天，RAG（检索增强生成）和Agent（智能体）技术正成为构建智能应用的核心技术。RAG通过结合外部知识库提升AI回答的准确性和时效性，而Agent则赋予AI系统自主规划和执行复杂任务的能力。本文将深入解析这两个关键技术领域的核心术语，帮助开发者建立完整的技术认知体系。

---

## 目录

- [一、RAG 检索增强生成相关术语](#一rag-检索增强生成相关术语)
- [二、Agent Planning 智能体规划相关术语](#二agent-planning-智能体规划相关术语)

---

## 一、RAG 检索增强生成相关术语

### 1.1 RAG - Retrieval-Augmented Generation（检索增强生成）

**定义**：结合信息检索和文本生成的技术，通过检索相关文档来增强LLM的回答质量和准确性。

**核心优势**：
- 提供最新、准确的信息
- 减少模型幻觉现象
- 支持领域特定知识
- 无需重新训练模型

### 1.2 Chunk（文档块）

**定义**：将长文档分割成的较小文本片段，便于存储、检索和处理。

**分块策略**：
- **固定长度分块**：按字符数或token数固定切分
- **语义分块**：按段落、句子等语义单位切分
- **重叠分块**：相邻块之间保留部分重叠内容
- **层次分块**：按文档结构（章节、段落）分层切分

**最佳实践**：
- 块大小通常在200-1000 tokens之间
- 保持语义完整性
- 考虑重叠以避免信息丢失

### 1.3 Embedding（嵌入向量）

**定义**：将文本转换为高维数值向量的技术，用于表示文本的语义信息。

**常用模型**：
- **OpenAI text-embedding-ada-002**：通用性强，效果稳定
- **BGE系列**：中文效果优秀的开源模型
- **Sentence-BERT**：专门用于句子级别的嵌入
- **E5系列**：多语言支持的嵌入模型

### 1.4 Vector Database（向量数据库）

**定义**：专门用于存储和检索高维向量数据的数据库系统。

**主流产品**：
- **Pinecone**：云原生向量数据库
- **Weaviate**：开源向量搜索引擎
- **Chroma**：轻量级嵌入数据库
- **Milvus**：高性能向量数据库
- **FAISS**：Facebook开源的相似性搜索库

### 1.5 Similarity Search（相似性搜索）

**定义**：在向量空间中查找与查询向量最相似的向量的过程。

**距离度量方法**：
- **余弦相似度**：衡量向量方向的相似性
- **欧几里得距离**：衡量向量在空间中的直线距离
- **点积**：计算向量的内积
- **曼哈顿距离**：各维度差值的绝对值之和

### 1.6 Dense Retrieval（密集检索）

**定义**：使用密集向量表示进行语义检索的方法，相对于传统的稀疏检索（如BM25）。

**特点**：
- 能够捕获语义相似性
- 对同义词和释义敏感
- 需要大量训练数据
- 计算成本较高

### 1.7 Hybrid Search（混合搜索）

**定义**：结合密集检索和稀疏检索的优势，提供更全面的搜索结果。

**实现方式**：
- **加权融合**：对不同检索结果按权重合并
- **重排序**：先粗检索再精排序
- **多阶段检索**：逐步细化检索结果

### 1.8 Reranking（重排序）

**定义**：对初步检索结果进行重新排序，提高最相关文档的排名。

**常用模型**：
- **Cross-Encoder**：交叉编码器模型
- **BGE-Reranker**：专门的重排序模型
- **Cohere Rerank**：商业重排序API

### 1.9 Query Expansion（查询扩展）

**定义**：通过添加相关词汇或改写查询来提高检索效果的技术。

**扩展方法**：
- **同义词扩展**：添加查询词的同义词
- **LLM改写**：使用语言模型重写查询
- **历史查询**：基于用户历史查询扩展
- **伪相关反馈**：基于初步检索结果扩展

### 1.10 Context Compression（上下文压缩）

**定义**：从检索到的文档中提取最相关信息，减少输入到LLM的上下文长度。

**压缩技术**：
- **关键句提取**：识别最重要的句子
- **摘要生成**：生成文档摘要
- **相关性过滤**：过滤不相关内容
- **信息去重**：移除重复信息

### 1.11 RRF - Reciprocal Rank Fusion（倒数排名融合）

**定义**：一种融合多个检索结果排序列表的算法，通过倒数排名加权来合并不同检索方法的结果。

**计算公式**：
```
RRF(d) = Σ(1/(k + rank_i(d)))
```
其中k是平滑参数（通常为60），rank_i(d)是文档d在第i个排序列表中的排名。

**应用场景**：
- **混合检索**：融合密集检索和稀疏检索结果
- **多模态检索**：合并文本、图像等不同模态的检索结果
- **多查询融合**：合并不同查询变体的检索结果
- **集成学习**：融合多个检索模型的输出

### 1.12 Index Types（索引类型）

**定义**：用于加速向量检索的不同数据结构和算法。

**主要索引类型**：

#### 1.12.1 HNSW - Hierarchical Navigable Small World（分层可导航小世界）
- **特点**：基于图的近似最近邻搜索
- **优势**：查询速度快，内存效率高
- **参数**：M（连接数）、efConstruction（构建时搜索深度）
- **适用场景**：大规模向量检索，对查询速度要求高

#### 1.12.2 IVF - Inverted File Index（倒排文件索引）
- **特点**：将向量空间划分为多个聚类
- **优势**：内存占用少，支持大规模数据
- **参数**：nlist（聚类数量）、nprobe（搜索聚类数）
- **适用场景**：超大规模向量库，内存受限环境

#### 1.12.3 LSH - Locality Sensitive Hashing（局部敏感哈希）
- **特点**：使用哈希函数将相似向量映射到相同桶
- **优势**：理论保证，适合高维数据
- **参数**：哈希函数数量、桶的数量
- **适用场景**：高维稀疏向量，近似检索

#### 1.12.4 PQ - Product Quantization（乘积量化）
- **特点**：将向量分段并独立量化
- **优势**：大幅压缩存储空间
- **参数**：子向量数量、码本大小
- **适用场景**：存储空间受限，可接受精度损失

### 1.13 BM25 - Best Matching 25（最佳匹配25）

**定义**：基于TF-IDF的经典稀疏检索算法，广泛用于文本检索。

**计算公式**：
```
BM25(q,d) = Σ IDF(qi) × (f(qi,d) × (k1+1)) / (f(qi,d) + k1 × (1-b+b×|d|/avgdl))
```

**参数说明**：
- **k1**：控制词频饱和度（通常1.2-2.0）
- **b**：控制文档长度归一化（通常0.75）
- **IDF**：逆文档频率
- **f(qi,d)**：词qi在文档d中的频率

### 1.14 Dense Passage Retrieval（密集段落检索）

**定义**：使用密集向量表示进行段落级别检索的方法。

**核心组件**：
- **双编码器架构**：查询编码器和段落编码器
- **对比学习**：正负样本对比训练
- **难负样本挖掘**：选择困难的负样本
- **知识蒸馏**：从大模型向小模型转移知识

### 1.15 Sparse Retrieval（稀疏检索）

**定义**：基于精确词汇匹配的传统检索方法。

**特点**：
- **可解释性强**：匹配过程透明
- **零样本能力**：无需训练即可使用
- **词汇匹配**：依赖精确的词汇重叠
- **计算效率高**：倒排索引支持快速检索

### 1.16 Multi-Vector Retrieval（多向量检索）

**定义**：为每个文档生成多个向量表示以提高检索精度的方法。

**实现方式**：
- **ColBERT**：为每个token生成向量
- **多粒度表示**：句子级、段落级、文档级向量
- **多视角编码**：从不同角度编码文档
- **层次化表示**：不同抽象层次的向量

### 1.17 Cross-Encoder vs Bi-Encoder（交叉编码器 vs 双编码器）

**Bi-Encoder（双编码器）**：
- **架构**：查询和文档分别编码
- **优势**：可预计算文档向量，检索速度快
- **劣势**：查询和文档交互有限
- **适用**：第一阶段粗检索

**Cross-Encoder（交叉编码器）**：
- **架构**：查询和文档联合编码
- **优势**：充分的查询-文档交互，精度高
- **劣势**：需要实时计算，速度慢
- **适用**：第二阶段精排序

### 1.18 Negative Sampling（负样本采样）

**定义**：在训练检索模型时选择负样本的策略。

**采样策略**：
- **随机负样本**：随机选择不相关文档
- **难负样本**：选择容易混淆的文档
- **批内负样本**：使用同批次内其他样本作为负样本
- **动态负样本**：训练过程中动态更新负样本

### 1.19 Knowledge Graph Enhanced RAG（知识图谱增强RAG）

**定义**：结合知识图谱的结构化信息来增强RAG系统的方法。

**增强方式**：
- **实体链接**：将文本中的实体链接到知识图谱
- **关系推理**：利用实体间关系进行推理
- **路径检索**：在知识图谱中检索相关路径
- **混合检索**：结合文本检索和图检索

### 1.20 Adaptive RAG（自适应RAG）

**定义**：根据查询复杂度和类型动态调整检索策略的RAG系统。

**自适应机制**：
- **查询分类**：识别查询类型和复杂度
- **策略选择**：选择合适的检索和生成策略
- **动态路由**：将查询路由到最适合的处理流程
- **反馈学习**：根据结果质量调整策略

### 1.21 ETL Pipeline（ETL管道）

**定义**：用于RAG系统离线数据处理的提取、转换、加载流程。

**核心组件**：
- **DocumentReader**：从各种数据源提取文档
- **DocumentTransformer**：转换和处理文档内容
- **DocumentWriter**：将处理后的文档写入向量存储

**处理流程**：
```
数据源 → 提取(Extract) → 转换(Transform) → 加载(Load) → 向量存储
```

### 1.22 Document Splitting（文档分割）

**定义**：将长文档分割成适合向量化和检索的小块的技术。

**分割策略**：
- **固定长度分割**：按字符数或token数固定切分
- **语义分割**：按段落、句子等语义单位分割
- **递归分割**：使用多级分隔符递归分割
- **重叠分割**：相邻块之间保留重叠内容

**关键参数**：
- **chunk_size**：每个块的大小（通常200-1000 tokens）
- **chunk_overlap**：块之间的重叠长度（通常10-20%）
- **separators**：分割符优先级列表

### 1.23 Metadata Extraction（元数据提取）

**定义**：从文档中提取结构化信息作为元数据的过程。

**提取内容**：
- **文档属性**：标题、作者、创建时间、文件类型
- **内容特征**：主题、关键词、摘要、语言
- **结构信息**：章节、页码、层级关系
- **业务信息**：部门、项目、版本、状态

**提取方法**：
- **规则提取**：基于正则表达式和模式匹配
- **LLM提取**：使用语言模型智能提取
- **结构化解析**：解析文档的结构化部分
- **外部API**：调用专门的元数据提取服务

### 1.24 Similarity Threshold（相似度阈值）

**定义**：在向量检索中用于过滤检索结果的最小相似度值。

**阈值设置**：
- **严格阈值**：0.8-0.9，只返回高度相关的结果
- **平衡阈值**：0.6-0.8，平衡精确度和召回率
- **宽松阈值**：0.3-0.6，提高召回率但可能降低精确度

**影响因素**：
- 嵌入模型的特性
- 数据集的质量和多样性
- 应用场景的容错要求
- 用户体验的期望

### 1.25 Top-K Retrieval（Top-K检索）

**定义**：返回与查询最相似的前K个文档的检索方法。

**K值选择**：
- **小K值（1-3）**：精确度高，适合简单查询
- **中K值（3-10）**：平衡精确度和覆盖度
- **大K值（10+）**：覆盖度高，适合复杂查询

**优化策略**：
- **动态K值**：根据查询复杂度调整K值
- **阈值结合**：同时使用相似度阈值过滤
- **多轮检索**：先粗检索再精检索

### 1.26 Document Preprocessing（文档预处理）

**定义**：在向量化之前对文档进行清洗和标准化的过程。

**预处理步骤**：
- **格式清理**：移除HTML标签、特殊字符
- **文本标准化**：统一编码、大小写、标点符号
- **内容过滤**：移除广告、导航、版权信息
- **语言检测**：识别和处理多语言内容

### 1.27 Evaluation Metrics（评估指标）

**定义**：用于衡量RAG系统性能的量化指标。

**检索评估**：
- **Precision@K**：前K个结果中相关文档的比例
- **Recall@K**：前K个结果覆盖的相关文档比例
- **MRR（Mean Reciprocal Rank）**：平均倒数排名
- **NDCG（Normalized Discounted Cumulative Gain）**：归一化折扣累积增益

**生成评估**：
- **BLEU**：与参考答案的n-gram重叠度
- **ROUGE**：召回导向的评估指标
- **BERTScore**：基于语义相似度的评估
- **Faithfulness**：生成内容对检索文档的忠实度

### 1.28 Cold Start Problem（冷启动问题）

**定义**：RAG系统在缺乏历史数据或用户反馈时的性能问题。

**解决方案**：
- **预训练嵌入**：使用通用预训练模型
- **合成数据**：生成人工查询-文档对
- **迁移学习**：从相似领域迁移知识
- **专家标注**：人工标注初始数据集

### 1.29 Incremental Update（增量更新）

**定义**：在不重建整个索引的情况下更新向量数据库的方法。

**更新策略**：
- **追加更新**：只添加新文档
- **差异更新**：识别变化并更新相应部分
- **版本控制**：维护文档的多个版本
- **定期重建**：周期性完全重建索引

### 1.30 Multi-hop Reasoning（多跳推理）

**定义**：需要结合多个文档片段进行推理才能回答的复杂查询处理。

**实现方法**：
- **迭代检索**：基于初始结果进行后续检索
- **图推理**：在文档关系图上进行推理
- **分解查询**：将复杂查询分解为子问题
- **融合答案**：合并多个子答案形成最终回答

---

## 二、Agent Planning 智能体规划相关术语

### 3.1 Planning（规划）

**定义**：Agent根据目标和当前状态，制定实现目标的行动序列的过程。

**规划类型**：
- **任务分解规划**：将复杂任务拆分为子任务
- **顺序规划**：确定任务执行的先后顺序
- **资源规划**：分配和管理可用资源
- **应急规划**：处理异常情况的备选方案

### 3.2 Task Decomposition（任务分解）

**定义**：将复杂的大任务分解为更小、更易管理的子任务的过程。

**分解策略**：
- **层次分解**：按层级结构分解任务
- **功能分解**：按功能模块分解任务
- **时序分解**：按时间顺序分解任务
- **依赖分解**：按任务依赖关系分解

**示例**：
```
目标：写一篇技术博客
├── 确定主题和目标读者
├── 收集相关资料和信息
├── 制定文章大纲
├── 撰写各个章节内容
├── 审查和修改文章
└── 发布和推广文章
```

### 3.3 Chain-of-Thought（思维链）

**定义**：引导模型进行逐步推理的方法，通过展示中间推理步骤来提高复杂问题的解决能力。

**实现方式**：
- **Zero-shot CoT**：直接要求模型"一步步思考"
- **Few-shot CoT**：提供推理示例
- **Auto-CoT**：自动生成推理链
- **Manual-CoT**：人工设计推理步骤

### 3.4 Tree-of-Thought（思维树）

**定义**：将问题解决过程建模为树状结构，允许模型探索多个推理路径并进行回溯。

**核心机制**：
- **状态生成**：生成中间思考状态
- **状态评估**：评估每个状态的价值
- **搜索策略**：选择最优路径
- **回溯机制**：在错误路径上回退

### 3.5 ReAct - Reasoning and Acting（推理与行动）

**定义**：结合推理和行动的框架，让Agent在推理过程中执行行动并获取反馈。

**工作流程**：
1. **思考（Think）**：分析当前情况
2. **行动（Act）**：执行具体操作
3. **观察（Observe）**：获取行动结果
4. **反思（Reflect）**：评估和调整策略

### 3.6 Self-Reflection（自我反思）

**定义**：Agent评估自己的行为和决策，从错误中学习并改进的能力。

**反思维度**：
- **行动效果评估**：分析行动是否达到预期
- **策略有效性**：评估当前策略的合理性
- **错误原因分析**：识别失败的根本原因
- **改进方案制定**：提出具体的改进措施

### 3.7 Multi-Agent Planning（多智能体规划）

**定义**：多个Agent协作完成复杂任务的规划方法。

**协作模式**：
- **分工协作**：不同Agent负责不同子任务
- **竞争协作**：Agent之间既竞争又协作
- **层次协作**：建立Agent之间的层次关系
- **民主协作**：所有Agent平等参与决策

### 3.8 Goal-Oriented Planning（目标导向规划）

**定义**：以明确目标为导向，制定实现目标的最优路径的规划方法。

**关键要素**：
- **目标定义**：明确、可衡量的目标
- **状态空间**：所有可能的状态集合
- **行动空间**：所有可能的行动集合
- **转移函数**：状态之间的转换关系

### 3.9 Hierarchical Planning（层次规划）

**定义**：将规划问题分解为多个层次，从高层抽象规划到低层具体执行的方法。

**层次结构**：
- **战略层**：长期目标和总体策略
- **战术层**：中期计划和资源分配
- **操作层**：具体行动和执行细节

### 3.10 Dynamic Planning（动态规划）

**定义**：在执行过程中根据环境变化和新信息动态调整规划的方法。

**适应机制**：
- **实时监控**：持续监控环境变化
- **计划更新**：根据新信息更新计划
- **异常处理**：应对突发情况
- **学习适应**：从经验中学习改进

### 3.11 Monte Carlo Tree Search（蒙特卡洛树搜索）

**定义**：一种基于随机采样的搜索算法，用于在大型搜索空间中找到最优决策。

**四个阶段**：
1. **选择（Selection）**：从根节点选择到叶节点
2. **扩展（Expansion）**：扩展新的子节点
3. **模拟（Simulation）**：随机模拟到终止状态
4. **反向传播（Backpropagation）**：更新路径上的节点值

### 3.12 Constraint Satisfaction（约束满足）

**定义**：在满足给定约束条件下寻找可行解的问题求解方法。

**约束类型**：
- **硬约束**：必须满足的条件
- **软约束**：优先满足的条件
- **时间约束**：时间相关的限制
- **资源约束**：资源使用的限制

---

## 总结

本文深入解析了RAG检索增强生成和Agent智能体规划两个核心技术领域的关键术语，为开发者构建智能应用提供了完整的技术词汇基础。

### 技术发展趋势

**RAG技术持续演进**：
- 从基础的文档检索发展到智能的混合搜索
- 引入知识图谱增强和自适应检索策略
- 评估体系日趋完善，支持多维度性能优化

**Agent规划能力增强**：
- 从简单的思维链发展到复杂的多跳推理
- 支持多智能体协作和层次化规划
- 自我反思和动态调整能力不断提升

### 实践应用价值

**RAG技术应用**：
- 企业知识库问答系统
- 智能客服和技术支持
- 学术研究和文档分析
- 实时信息检索和整合

**Agent规划应用**：
- 复杂任务自动化执行
- 智能决策支持系统
- 多步骤工作流程管理
- 自主问题解决方案

### 学习建议

1. **理论与实践结合**：深入理解概念的同时，通过实际项目加深认知
2. **技术栈整合**：RAG和Agent技术往往需要结合使用，形成完整解决方案
3. **持续跟进发展**：这两个领域发展迅速，需要持续关注最新进展
4. **安全性重视**：在应用过程中始终考虑系统的安全性和可靠性

### 未来展望

RAG和Agent技术的结合将推动AI应用向更加智能化、自主化的方向发展。掌握这些核心术语和概念，将帮助开发者：

- 构建更加智能和可靠的AI应用系统
- 有效解决复杂的业务场景和技术挑战
- 在AI技术快速发展的浪潮中保持竞争优势
- 为用户提供更加优质的智能化服务体验

希望这份技术词汇解析能够成为您在RAG和Agent技术学习与应用道路上的有力助手！
