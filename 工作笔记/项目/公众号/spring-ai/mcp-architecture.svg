<svg width="900" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151" />
    </marker>

    <!-- 双向箭头标记 -->
    <marker id="doubleArrow" markerWidth="14" markerHeight="7"
            refX="7" refY="3.5" orient="auto">
      <polygon points="0 1, 5 0, 5 2, 14 2, 14 5, 5 5, 5 7, 0 6" fill="#374151" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="900" height="600" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="2" rx="12"/>

  <!-- 标题 -->
  <text x="450" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#1F2937">
    Spring AI MCP 架构组件图
  </text>

  <!-- 第一层：Spring AI 应用程序 -->
  <rect x="50" y="60" width="180" height="90" rx="10" fill="#3B82F6" stroke="#1D4ED8" stroke-width="2"/>
  <text x="140" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">Spring AI</text>
  <text x="140" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">应用程序</text>
  <text x="140" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#DBEAFE">生成式AI应用</text>
  <text x="140" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#DBEAFE">用户交互 & AI模型调用</text>

  <!-- 第二层：Spring MCP 客户端 -->
  <rect x="360" y="60" width="180" height="90" rx="10" fill="#10B981" stroke="#059669" stroke-width="2"/>
  <text x="450" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">Spring MCP</text>
  <text x="450" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">客户端</text>
  <text x="450" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#D1FAE5">协议实现 & 1:1连接</text>
  <text x="450" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#D1FAE5">通信管理 & 协议转换</text>

  <!-- 第三层：MCP 服务器 -->
  <rect x="670" y="60" width="180" height="90" rx="10" fill="#F59E0B" stroke="#D97706" stroke-width="2"/>
  <text x="760" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">MCP Server</text>
  <text x="760" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="white">轻量级程序</text>
  <text x="760" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#FEF3C7">标准化协议</text>
  <text x="760" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#FEF3C7">专注特定功能</text>

  <!-- 连接线：Spring AI -> MCP Client -->
  <line x1="230" y1="105" x2="350" y2="105" stroke="#374151" stroke-width="3" marker-end="url(#arrowhead)"/>
  <rect x="270" y="85" width="60" height="20" rx="3" fill="#FFFFFF" stroke="#374151" stroke-width="1"/>
  <text x="300" y="98" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#374151">API调用</text>

  <!-- 连接线：MCP Client -> MCP Server -->
  <line x1="540" y1="105" x2="660" y2="105" stroke="#374151" stroke-width="3" marker-end="url(#arrowhead)"/>
  <rect x="580" y="85" width="60" height="20" rx="3" fill="#FFFFFF" stroke="#374151" stroke-width="1"/>
  <text x="610" y="98" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#374151">MCP协议</text>

  <!-- 分隔线 -->
  <line x1="50" y1="190" x2="850" y2="190" stroke="#D1D5DB" stroke-width="2" stroke-dasharray="8,4"/>
  <text x="450" y="210" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6B7280">
    数据源与外部系统
  </text>

  <!-- 第四层：数据源和服务 -->

  <!-- 本地数据源 -->
  <rect x="100" y="240" width="160" height="110" rx="10" fill="#8B5CF6" stroke="#7C3AED" stroke-width="2"/>
  <text x="180" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">本地数据源</text>
  <text x="180" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Local Data Sources</text>
  <text x="180" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#F3E8FF">• 计算机文件</text>
  <text x="180" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#F3E8FF">• 本地数据库</text>
  <text x="180" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#F3E8FF">• 系统服务</text>

  <!-- 远程服务 -->
  <rect x="370" y="240" width="160" height="110" rx="10" fill="#EF4444" stroke="#DC2626" stroke-width="2"/>
  <text x="450" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">远程服务</text>
  <text x="450" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Remote Services</text>
  <text x="450" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#FEE2E2">• REST API</text>
  <text x="450" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#FEE2E2">• 云服务</text>
  <text x="450" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#FEE2E2">• 第三方平台</text>

  <!-- 其他外部系统 -->
  <rect x="640" y="240" width="160" height="110" rx="10" fill="#06B6D4" stroke="#0891B2" stroke-width="2"/>
  <text x="720" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">其他外部系统</text>
  <text x="720" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Other External Systems</text>
  <text x="720" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#CFFAFE">• Git仓库</text>
  <text x="720" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#CFFAFE">• 邮件服务</text>
  <text x="720" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#CFFAFE">• 消息队列</text>

  <!-- MCP Server 到各数据源的连接线 -->
  <!-- 到本地数据源 -->
  <line x1="760" y1="150" x2="760" y2="170" stroke="#374151" stroke-width="3"/>
  <line x1="760" y1="170" x2="180" y2="170" stroke="#374151" stroke-width="3"/>
  <line x1="180" y1="170" x2="180" y2="230" stroke="#374151" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 到远程服务 -->
  <line x1="760" y1="150" x2="760" y2="180" stroke="#374151" stroke-width="3"/>
  <line x1="760" y1="180" x2="450" y2="180" stroke="#374151" stroke-width="3"/>
  <line x1="450" y1="180" x2="450" y2="230" stroke="#374151" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 到其他外部系统 -->
  <line x1="760" y1="150" x2="720" y2="230" stroke="#374151" stroke-width="3" marker-end="url(#arrowhead)"/>

  <!-- 特性说明框 -->
  <rect x="50" y="380" width="800" height="170" rx="10" fill="#F8FAFC" stroke="#E2E8F0" stroke-width="2"/>
  <text x="70" y="405" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1F2937">
    🎯 MCP 核心特性
  </text>

  <!-- 特性列表 -->
  <g transform="translate(70, 430)">
    <!-- 第一列 -->
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#374151">
      ✅ 标准化集成
    </text>
    <text x="0" y="18" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 统一的协议规范
    </text>
    <text x="0" y="33" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 降低集成复杂度
    </text>

    <text x="0" y="65" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#374151">
      🔒 安全隔离
    </text>
    <text x="0" y="83" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 进程级别隔离
    </text>
    <text x="0" y="98" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 保障系统安全
    </text>

    <!-- 第二列 -->
    <text x="270" y="0" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#374151">
      🧩 模块化架构
    </text>
    <text x="270" y="18" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 每个Server专注特定功能
    </text>
    <text x="270" y="33" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 易于维护和扩展
    </text>

    <text x="270" y="65" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#374151">
      🚀 开发效率
    </text>
    <text x="270" y="83" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • Starter自动配置
    </text>
    <text x="270" y="98" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 开箱即用
    </text>

    <!-- 第三列 -->
    <text x="540" y="0" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#374151">
      🌍 生态丰富
    </text>
    <text x="540" y="18" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 大量现成MCP Server
    </text>
    <text x="540" y="33" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 统一生态系统
    </text>

    <text x="540" y="65" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#374151">
      ⚡ 灵活部署
    </text>
    <text x="540" y="83" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 支持Stdio和SSE
    </text>
    <text x="540" y="98" font-family="Arial, sans-serif" font-size="11" fill="#6B7280">
      • 本地/远程部署
    </text>
  </g>

  <!-- 底部说明 -->
  <text x="450" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#9CA3AF">
    Spring AI MCP：构建智能体的标准化数据连接协议
  </text>
</svg>
