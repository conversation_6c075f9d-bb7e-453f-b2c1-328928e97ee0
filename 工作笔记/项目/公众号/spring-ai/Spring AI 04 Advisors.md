# Spring AI Advisors：增强AI交互工具

Spring AI的Advisors API提供了一种灵活强大的方式，用于拦截、修改和增强AI驱动的交互。通过使用Advisors API，开发者可以创建更复杂、更可重用和更易于维护的AI组件。

## 一、Advisors的核心概念

> Advisors API是Spring AI提供的一种强大机制，用于封装常见的生成式AI模式，转换发送到和接收自大语言模型(LLMs)的数据，以及在各种模型和用例之间提供可移植性。



### 1.1 主要优势

使用Advisors API的关键优势包括：

1. **封装常见模式**：将RAG、内容安全检查等模式封装为可重用组件
2. **数据转换**：在请求发送前和响应接收后修改和增强数据
3. **跨模型可移植性**：编写一次Advisor，可用于多种AI模型和用例
4. **可观测性**：Advisors参与可观测性堆栈，支持指标和跟踪功能

### 1.2 工作原理

Advisors API的工作原理类似于Spring框架中的面向切面编程(AOP)：

- 在AI交互的请求和响应阶段拦截处理流程
- 可以访问和修改请求内容和上下文
- 可以访问和修改响应内容和上下文
- 多个Advisors可以组成处理链，按特定顺序执行

以下流程图展示了Advisors链的工作方式：


## 二、核心组件

Spring AI的Advisors API主要包括以下核心组件：

### 2.1 接口

- **CallAroundAdvisor**：用于非流式场景的顾问接口
- **StreamAroundAdvisor**：用于流式场景的顾问接口
- **Advisor**：基础接口，定义了名称和顺序等基本特性

### 2.2 其他关键类

- **AdvisedRequest**：表示未密封的Prompt请求
- **AdvisedResponse**：代表Chat Completion响应
- **AdvisorContext**：在顾问链中共享状态的上下文

## 三、使用Advisors

> 可以通过ChatClient API配置现有的Advisors，也可以实现自定义Advisors。

### 3.1 配置现有Advisors

```java
// 创建带有Advisors的ChatClient
var chatClient = ChatClient.builder(chatModel)
    .defaultAdvisors(
        MessageChatMemoryAdvisor.builder(chatMemory).build(),  // 聊天记忆顾问
        QuestionAnswerAdvisor.builder(vectorStore).build()     // RAG顾问
    )
    .build();

// 设置运行时参数
String response = chatClient.prompt()
    .advisors(advisor -> advisor.param(ChatMemory.CONVERSATION_ID, "678"))
    .user(userText)
    .call()
    .content();
```

### 3.2 Advisor执行顺序

Advisors的执行顺序由`getOrder()`方法决定：

- 较低的order值优先执行
- Advisor链按照栈的方式工作：
  - 第一个加入链的Advisor最先处理请求
  - 同时也是最后处理响应的Advisor
- 高优先级（低order值）的Advisor先执行请求处理，后执行响应处理
- 如果多个Advisor具有相同的order值，它们的执行顺序不保证

```java
@Override
public int getOrder() {
    return Ordered.HIGHEST_PRECEDENCE;  // 最高优先级，最先执行请求处理
}
```

## 四、内置Advisors

Spring AI提供了多个内置的Advisors，用于常见的AI交互场景：

### 4.1 聊天记忆Advisors

聊天记忆Advisors负责管理对话历史：

- **MessageChatMemoryAdvisor**：将记忆作为消息集合添加到prompt
- **PromptChatMemoryAdvisor**：将记忆整合到prompt的系统文本中
- **VectorStoreChatMemoryAdvisor**：从VectorStore检索记忆并添加到系统文本中

### 4.2 问答Advisor

**QuestionAnswerAdvisor**：利用向量存储提供问答能力，实现RAG（检索增强生成）模式。

### 4.3 内容安全Advisor

**SafeGuardAdvisor**：一个简单的顾问，用于防止模型生成有害或不适当的内容。

## 五、自定义Advisor实现

### 5.1 日志记录Advisor

以下是一个简单的日志记录Advisor，它记录请求和响应但不修改它们：

```java
public class SimpleLoggerAdvisor implements CallAroundAdvisor, StreamAroundAdvisor {

    private static final Logger logger = LoggerFactory.getLogger(SimpleLoggerAdvisor.class);

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }

    @Override
    public int getOrder() {
        return 0;
    }

    @Override
    public AdvisedResponse aroundCall(AdvisedRequest advisedRequest, CallAroundAdvisorChain chain) {

        logger.debug("请求前: {}", advisedRequest);

        AdvisedResponse advisedResponse = chain.nextAroundCall(advisedRequest);

        logger.debug("响应后: {}", advisedResponse);

        return advisedResponse;
    }

    @Override
    public Flux<AdvisedResponse> aroundStream(AdvisedRequest advisedRequest, StreamAroundAdvisorChain chain) {

        logger.debug("流式请求前: {}", advisedRequest);

        Flux<AdvisedResponse> advisedResponses = chain.nextAroundStream(advisedRequest);

        return new MessageAggregator().aggregateAdvisedResponse(advisedResponses,
                advisedResponse -> logger.debug("流式响应: {}", advisedResponse));
    }
}
```

### 5.2 二次阅读(Re²)Advisor

基于"二次阅读改进大型语言模型的推理能力"的研究，实现二次阅读技术的Advisor：

```java
public class ReReadingAdvisor implements CallAroundAdvisor, StreamAroundAdvisor {

    private AdvisedRequest before(AdvisedRequest advisedRequest) {

        Map<String, Object> advisedUserParams = new HashMap<>(advisedRequest.userParams());
        advisedUserParams.put("re2_input_query", advisedRequest.userText());

        return AdvisedRequest.from(advisedRequest)
            .userText("""
                {re2_input_query}
                再次阅读此问题: {re2_input_query}
                """)
            .userParams(advisedUserParams)
            .build();
    }

    @Override
    public AdvisedResponse aroundCall(AdvisedRequest advisedRequest, CallAroundAdvisorChain chain) {
        return chain.nextAroundCall(this.before(advisedRequest));
    }

    @Override
    public Flux<AdvisedResponse> aroundStream(AdvisedRequest advisedRequest, StreamAroundAdvisorChain chain) {
        return chain.nextAroundStream(this.before(advisedRequest));
    }

    @Override
    public int getOrder() {
        return 0;
    }

    @Override
    public String getName() {
        return this.getClass().getSimpleName();
    }
}
```

## 六、流式处理和非流式处理

Spring AI的Advisors支持两种处理模式：

**非流式处理**：处理完整的请求和响应
- 实现`CallAroundAdvisor`接口
- 重写`aroundCall()`方法

**流式处理**：处理连续的请求和响应流
- 实现`StreamAroundAdvisor`接口
- 重写`aroundStream()`方法
- 使用响应式编程概念（如Flux）

```java
@Override
public Flux<AdvisedResponse> aroundStream(AdvisedRequest advisedRequest, StreamAroundAdvisorChain chain) {
    return  Mono.just(advisedRequest)
            .publishOn(Schedulers.boundedElastic())
            .map(request -> {
                // 请求处理逻辑
                return modifiedRequest;
            })
            .flatMapMany(request -> chain.nextAroundStream(request))
            .map(response -> {
                // 响应处理逻辑
                return modifiedResponse;
            });
}
```



---

> 参考资料：
>
> [Spring AI Reference - Advisors API](https://docs.spring.io/spring-ai/reference/api/advisors.html)
>
> [Spring AI Alibaba - Advisors](https://java2ai.com/docs/latest/advisors/) 