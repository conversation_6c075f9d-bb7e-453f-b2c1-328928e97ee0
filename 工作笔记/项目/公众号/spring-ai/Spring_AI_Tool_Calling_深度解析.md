# Spring AI Tool Calling 完全指南：突破LLM边界的核心技术

> 当用户问"今天天气如何"时，ChatGPT只能回答"我无法获取实时天气信息"；当需要AI帮助预订机票时，模型只能生成预订步骤而无法真正执行操作。这些场景暴露了大语言模型的核心痛点：**知识静态化**和**能力局限性**。Spring AI的Tool Calling技术正是为解决这些实际问题而生，让AI从"只会说"变成"真能做"。

## 引言

**Tool Calling**（工具调用，也称为Function Calling函数调用）是现代AI应用中的核心模式，它让AI模型能够与外部API和系统进行交互，极大地扩展了模型的能力边界。想象一下，一个AI助手不仅能回答问题，还能帮你查询天气、发送邮件、预订机票——这就是Tool Calling的魅力所在。

## 一、Tool Calling核心概念

### 1.1 什么是Tool Calling

Tool Calling是一种让AI模型能够调用预定义工具或函数的技术模式。它解决了大语言模型的两个核心局限：

- **知识时效性**：模型训练数据有时间截止点，无法获取实时信息
- **操作能力**：模型只能生成文本，无法直接执行系统操作

### 1.2 工具分类

Spring AI将工具分为两大类：

#### 信息检索类工具（Information Retrieval）
用于从外部数据源获取信息，增强模型的知识库：
- 数据库查询
- Web服务调用
- 文件系统访问
- 搜索引擎查询

#### 执行操作类工具（Taking Action）
用于在软件系统中执行具体操作：
- 发送邮件
- 创建数据库记录
- 提交表单
- 触发工作流

### 1.3 Tool Calling核心流程

Tool Calling的执行遵循标准的六步流程，理解这个流程对于掌握整个技术至关重要：

![Tool Calling执行流程](tool-calling-flow.svg)

1. **工具定义注册**：当我们要让模型使用某个工具时，需要在聊天请求中包含工具定义。每个工具定义包含：
   - 工具名称（name）
   - 工具描述（description）
   - 输入参数的JSON Schema

2. **模型决策调用**：当模型决定调用工具时，它会发送一个响应，包含：
   - 要调用的工具名称
   - 按照定义的schema格式化的输入参数

3. **应用程序执行**：应用程序负责：
   - 根据工具名称识别对应的工具
   - 使用提供的输入参数执行工具

4. **结果处理**：应用程序处理工具调用的结果

5. **结果回传**：应用程序将工具调用结果发送回模型

6. **最终响应生成**：模型使用工具调用结果作为额外上下文，生成最终响应

```java
// 流程示例：查询天气
// 1. 注册工具定义
@Tool(description = "获取指定城市的当前天气信息")
String getWeather(String city) { ... }

// 2. 用户提问："北京今天天气如何？"
// 3. 模型识别需要调用getWeather工具，参数：city="北京"
// 4. 应用程序执行getWeather("北京")
// 5. 返回结果："北京今天晴天，温度25°C"
// 6. 模型生成最终回答："根据最新信息，北京今天是晴天，温度25°C，适合外出活动。"
```

### 1.4 安全机制

**重要提示**：模型本身永远无法直接访问任何API，所有工具调用都由客户端应用程序执行。这是一个关键的安全考虑。

## 二、快速入门实战

### 2.1 信息检索工具示例

让我们从一个简单的日期时间查询工具开始：

```java
import java.time.LocalDateTime;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.context.i18n.LocaleContextHolder;

class DateTimeTools {

    @Tool(description = "获取用户时区的当前日期和时间")
    String getCurrentDateTime() {
        return LocalDateTime.now()
            .atZone(LocaleContextHolder.getTimeZone().toZoneId())
            .toString();
    }
}
```

**使用工具：**

```java
ChatModel chatModel = ...

String response = ChatClient.create(chatModel)
        .prompt("明天是星期几？")
        .tools(new DateTimeTools())
        .call()
        .content();

System.out.println(response);
// 输出：明天是2024年10月21日，星期一。
```

### 2.2 操作执行工具示例

接下来添加一个设置闹钟的工具：

```java
@Tool(description = "为用户设置指定时间的闹钟，时间格式为ISO-8601")
void setAlarm(String time) {
    LocalDateTime alarmTime = LocalDateTime.parse(time, DateTimeFormatter.ISO_DATE_TIME);
    System.out.println("闹钟已设置为：" + alarmTime);
}
```

**组合使用多个工具：**

```java
String response = ChatClient.create(chatModel)
        .prompt("请帮我设置一个10分钟后的闹钟")
        .tools(new DateTimeTools())
        .call()
        .content();
```

模型会自动：
1. 调用`getCurrentDateTime()`获取当前时间
2. 计算10分钟后的时间
3. 调用`setAlarm()`设置闹钟

## 三、工具定义的三种方式

### 3.1 声明式定义：@Tool注解

这是最简单直观的方式：

```java
class CustomerTools {

    @Tool(description = "根据客户ID检索客户信息")
    Customer getCustomerInfo(Long id) {
        return customerRepository.findById(id);
    }

    @Tool(description = "更新客户信息")
    void updateCustomerInfo(
        Long id, 
        String name, 
        @ToolParam(description = "客户邮箱地址", required = false) String email
    ) {
        // 更新逻辑
    }
}
```

**@Tool注解参数说明：**
- `name`：工具名称（默认使用方法名）
- `description`：工具描述（帮助模型理解何时使用）
- `returnDirect`：是否直接返回结果给调用者
- `resultConverter`：结果转换器

### 3.2 编程式定义：MethodToolCallback

适用于需要更精细控制的场景：

```java
Method method = ReflectionUtils.findMethod(DateTimeTools.class, "getCurrentDateTime");
ToolCallback toolCallback = MethodToolCallback.builder()
    .toolDefinition(ToolDefinition.builder(method)
            .description("获取用户时区的当前日期和时间")
            .build())
    .toolMethod(method)
    .toolObject(new DateTimeTools())
    .build();
```

### 3.3 函数式定义：Function接口

```java
@Bean
Function<WeatherRequest, WeatherResponse> weatherFunction() {
    return request -> {
        // 调用天气API
        return weatherService.getWeather(request.getLocation());
    };
}
```

## 四、参数约束与智能提示：让AI精准理解工具用法

当AI模型调用工具时，它需要知道每个参数的具体含义、格式要求和是否必填。这就是**JSON Schema**的作用——它为AI模型提供了工具参数的"使用说明书"，确保模型能够正确调用工具。

### 4.1 为什么需要参数描述？

**问题场景**：如果我们只是简单定义一个方法：
```java
void bookFlight(String departure, String arrival, String date) { ... }
```

AI模型会困惑：
- `departure`是城市名还是机场代码？
- `date`是什么格式？"明天"、"2024-01-01"还是"Jan 1, 2024"？
- 这些参数都是必填的吗？

**解决方案**：通过详细的参数描述，让AI模型精确理解每个参数的用法：

```java
@Tool(description = "预订航班")
void bookFlight(
    @ToolParam(description = "出发城市代码，如PEK代表北京") String departure,
    @ToolParam(description = "到达城市代码，如SHA代表上海") String arrival,
    @ToolParam(description = "出发日期，格式：YYYY-MM-DD") String date
) {
    // 预订逻辑
}
```

### 4.2 可选参数的智能处理

在实际应用中，并非所有参数都是必需的。Spring AI支持多种方式定义可选参数：

```java
@Tool(description = "搜索产品")
void searchProducts(
    String keyword,                                    // 必填：搜索关键词
    @ToolParam(required = false) String category,      // 可选：产品分类
    @Nullable Integer maxPrice,                        // 可选：最高价格
    @JsonProperty(required = false) String brand       // 可选：品牌筛选
) {
    // 搜索逻辑
}
```

**智能效果**：
- 用户问："搜索手机"→ AI只传递`keyword="手机"`
- 用户问："搜索1000元以下的苹果手机"→ AI传递`keyword="手机"`, `maxPrice=1000`, `brand="苹果"`

### 4.3 JSON Schema自动生成

Spring AI会根据方法签名和注解自动生成JSON Schema：

```json
{
  "type": "object",
  "properties": {
    "keyword": {
      "type": "string",
      "description": "搜索关键词"
    },
    "category": {
      "type": "string",
      "description": "产品分类"
    },
    "maxPrice": {
      "type": "integer",
      "description": "最高价格"
    }
  },
  "required": ["keyword"]
}
```

这个Schema告诉AI模型：
- 哪些参数是什么类型
- 哪些参数是必填的
- 每个参数的具体含义

## 五、高级特性

### 5.1 工具上下文（Tool Context）

传递额外的上下文信息给工具：

```java
@Tool(description = "获取客户信息")
Customer getCustomerInfo(Long id, ToolContext toolContext) {
    String tenantId = toolContext.get("tenantId");
    return customerRepository.findById(id, tenantId);
}
```

**使用方式：**

```java
String response = ChatClient.create(chatModel)
        .prompt("告诉我客户42的详细信息")
        .tools(new CustomerTools())
        .toolContext(Map.of("tenantId", "acme"))
        .call()
        .content();
```

### 5.2 直接返回结果（Return Direct）

某些场景下，我们希望工具结果直接返回给调用者，而不是发送回模型：

```java
@Tool(description = "执行RAG检索", returnDirect = true)
String ragSearch(String query) {
    return vectorStore.search(query);
}
```

### 5.3 自定义结果转换器

```java
public class CustomResultConverter implements ToolCallResultConverter {
    @Override
    public String convert(Object result, Type returnType) {
        // 自定义转换逻辑
        return JsonUtils.toJson(result);
    }
}

@Tool(description = "获取复杂对象", resultConverter = CustomResultConverter.class)
ComplexObject getComplexData() {
    return complexService.getData();
}
```

## 六、工具执行管理

### 6.1 框架控制的执行（推荐）

Spring AI默认自动管理工具执行生命周期：

```java
// 框架自动处理工具调用
ChatResponse response = chatModel.call(prompt);
```

### 6.2 用户控制的执行

适用于需要精细控制的场景：

```java
ChatOptions chatOptions = ToolCallingChatOptions.builder()
    .toolCallbacks(new CustomerTools())
    .internalToolExecutionEnabled(false)  // 禁用自动执行
    .build();

Prompt prompt = new Prompt("查询客户42的信息", chatOptions);
ChatResponse chatResponse = chatModel.call(prompt);

// 手动处理工具调用
while (chatResponse.hasToolCalls()) {
    ToolExecutionResult result = toolCallingManager.executeToolCalls(prompt, chatResponse);
    prompt = new Prompt(result.conversationHistory(), chatOptions);
    chatResponse = chatModel.call(prompt);
}
```

## 七、异常处理与监控

### 7.1 异常处理

```java
@Bean
ToolExecutionExceptionProcessor toolExecutionExceptionProcessor() {
    return new DefaultToolExecutionExceptionProcessor(true); // true表示抛出异常
}
```

### 7.2 可观测性

Spring AI提供完整的监控支持：

```yaml
# application.yml
management:
  observations:
    spring:
      ai:
        tool:
          enabled: true
```

## 八、最佳实践

### 8.1 工具设计原则

1. **单一职责**：每个工具只做一件事
2. **详细描述**：提供清晰的工具和参数描述
3. **错误处理**：妥善处理异常情况
4. **安全考虑**：验证输入参数，避免安全漏洞

### 8.2 性能优化

1. **异步执行**：对于耗时操作使用异步处理
2. **缓存策略**：缓存频繁调用的结果
3. **批量操作**：合并相似的工具调用

### 8.3 测试策略

```java
@Test
void testWeatherTool() {
    WeatherTools weatherTools = new WeatherTools();
    String result = weatherTools.getWeather("北京");
    assertThat(result).contains("温度");
}
```

## 总结

Spring AI的Tool Calling功能为AI应用开发提供了强大而灵活的工具集成能力。通过合理使用工具调用，我们可以：

- **突破模型知识边界**：获取实时信息
- **增强交互能力**：执行系统操作
- **提升用户体验**：提供更智能的服务

掌握Tool Calling技术，是构建下一代智能应用的关键技能。随着AI技术的不断发展，工具调用将成为连接AI模型与现实世界的重要桥梁。

---

*本文基于Spring AI 1.0.0官方文档编写，更多详细信息请参考[Spring AI官方文档](https://docs.spring.io/spring-ai/reference/api/tools.html)。*
