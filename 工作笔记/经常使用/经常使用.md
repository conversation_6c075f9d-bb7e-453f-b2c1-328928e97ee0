[  "@cevipsa.com",  "@cpav3.com",  "@nuclene.com",  "@steveix.com",  "@mocvn.com",  "@tenvil.com",  "@tgvis.com",  "@amozix.com",  "@anypsd.com",  "@maxric.com" ]

```sql
    查技术助理的
    WITH base_history AS (
        SELECT
        h.trace_id,
        h.session_id,
        h.consumer_id,
        MAX(h.vote) as max_vote,
        MAX(CASE WHEN h.role = 'assistant' THEN h.feedback END) as assistant_feedback,
        MAX(h.rewrite_query) AS rewrite_query,
        MAX(h.created_at) AS created_at,
        MAX(h.feedback) AS feedback,
        MAX(h.chat_file) AS chat_file
        FROM rag_chat_history h
        WHERE created_at >= '2025-01-01'
        AND h.client_key in (1,2)
        AND h.trace_id NOT LIKE '%myroki_test_com'
        GROUP BY h.trace_id, h.session_id, h.consumer_id
        )
        SELECT
        b.trace_id,
        CASE
        WHEN b.trace_id LIKE 'h5%' THEN 'H5端'
        WHEN b.trace_id LIKE 'applet%' THEN '小程序端'
        WHEN b.trace_id LIKE 'app%' THEN 'APP端'
        WHEN b.trace_id LIKE 'box%' THEN '魔盒端'
        ELSE '其他来源'
        END AS user_source,
        b.session_id,
        b.consumer_id,
        user_msg.message AS user_message,
        assistant_msg.message AS assistant_message,
        user_msg.llm_message AS llm_message,
        CASE b.max_vote
        WHEN 0 THEN '正常'
        WHEN 1 THEN '点赞'
        WHEN 2 THEN '点踩'
        ELSE '未知'
        END AS vote,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.reason')) AS reason,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.type')) AS question_type,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.problems')) AS problem_description,
        b.rewrite_query,
        b.created_at,
        b.feedback,
        user_msg.id AS query_id,
        assistant_msg.id AS answer_id,
        b.chat_file,
        c.nickname
        FROM base_history b
        LEFT JOIN rag_chat_history user_msg
        ON b.trace_id = user_msg.trace_id
        AND user_msg.role = 'user'
        LEFT JOIN rag_chat_history assistant_msg
        ON b.trace_id = assistant_msg.trace_id
        AND assistant_msg.role = 'assistant'
        JOIN rag_consumer c
        ON b.consumer_id = c.id
        ORDER BY b.created_at ASC
```

```sql
  只查询小程序的
  WITH base_history AS (
        SELECT
        h.trace_id,
        h.session_id,
        h.consumer_id,
        MAX(h.vote) as max_vote,
        MAX(CASE WHEN h.role = 'assistant' THEN h.feedback END) as assistant_feedback,
        MAX(h.rewrite_query) AS rewrite_query,
        MAX(h.created_at) AS created_at,
        MAX(h.feedback) AS feedback,
        MAX(h.chat_file) AS chat_file
        FROM rag_chat_history h
        WHERE created_at >= '2025-04-01'
				AND created_at < '2025-05-01'
        AND h.client_key in (5)
        AND h.trace_id  LIKE 'applet%'
        GROUP BY h.trace_id, h.session_id, h.consumer_id
        )
        SELECT
        b.trace_id,
        CASE
        WHEN b.trace_id LIKE 'h5%' THEN 'H5端'
        WHEN b.trace_id LIKE 'applet%' THEN '小程序端'
        WHEN b.trace_id LIKE 'app%' THEN 'APP端'
        WHEN b.trace_id LIKE 'box%' THEN '魔盒端'
        ELSE '其他来源'
        END AS user_source,
        b.session_id,
        b.consumer_id,
        user_msg.message AS user_message,
        assistant_msg.message AS assistant_message,
        user_msg.llm_message AS llm_message,
        CASE b.max_vote
        WHEN 0 THEN '正常'
        WHEN 1 THEN '点赞'
        WHEN 2 THEN '点踩'
        ELSE '未知'
        END AS vote,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.reason')) AS reason,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.type')) AS question_type,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.problems')) AS problem_description,
        b.rewrite_query,
        b.created_at,
        b.feedback,
        user_msg.id AS query_id,
        assistant_msg.id AS answer_id,
        b.chat_file,
        c.nickname
        FROM base_history b
        LEFT JOIN rag_chat_history user_msg
        ON b.trace_id = user_msg.trace_id
        AND user_msg.role = 'user'
        LEFT JOIN rag_chat_history assistant_msg
        ON b.trace_id = assistant_msg.trace_id
        AND assistant_msg.role = 'assistant'
        JOIN rag_consumer c
        ON b.consumer_id = c.id
        ORDER BY b.created_at ASC
```

```sql
查询用户聊天2天以上的
WITH original_query AS (
    SELECT
        h.consumer_id,
        h.created_at,
        c.nickname
    FROM rag_chat_history h
    JOIN rag_consumer c ON h.consumer_id = c.id
    WHERE h.created_at >= '2025-02-01'
    AND h.created_at < '2025-03-01'
    AND h.client_key in (5)
)
SELECT 
    consumer_id,
    nickname,
    COUNT(DISTINCT DATE(created_at)) as chat_days,
    GROUP_CONCAT(DISTINCT DATE(created_at) ORDER BY created_at) as chat_dates
FROM original_query
GROUP BY consumer_id, nickname
HAVING chat_days >= 2
ORDER BY chat_days DESC;

WITH original_query AS (
    WITH base_history AS (
        SELECT
            h.trace_id,
            h.session_id,
            h.consumer_id,
            MAX(h.vote) as max_vote,
            MAX(CASE WHEN h.role = 'assistant' THEN h.feedback END) as assistant_feedback,
            MAX(h.rewrite_query) AS rewrite_query,
            MAX(h.created_at) AS created_at,
            MAX(h.feedback) AS feedback,
            MAX(h.chat_file) AS chat_file
        FROM rag_chat_history h
        WHERE created_at >= '2025-02-01'
        AND created_at < '2025-03-01'
        AND h.client_key in (5)
        AND h.trace_id NOT LIKE '%myroki_test_com'
        GROUP BY h.trace_id, h.session_id, h.consumer_id
    )
    SELECT 
        b.trace_id,
        CASE
            WHEN b.trace_id LIKE 'h5%' THEN 'H5端'
            WHEN b.trace_id LIKE 'applet%' THEN '小程序端'
            WHEN b.trace_id LIKE 'app%' THEN 'APP端'
            WHEN b.trace_id LIKE 'box%' THEN '魔盒端'
            ELSE '其他来源'
        END AS user_source,
        b.session_id,
        b.consumer_id,
        user_msg.message AS user_message,
        assistant_msg.message AS assistant_message,
        user_msg.llm_message AS llm_message,
        CASE b.max_vote
            WHEN 0 THEN '正常'
            WHEN 1 THEN '点赞'
            WHEN 2 THEN '点踩'
            ELSE '未知'
        END AS vote,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.reason')) AS reason,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.type')) AS question_type,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.problems')) AS problem_description,
        b.rewrite_query,
        b.created_at,
        b.feedback,
        user_msg.id AS query_id,
        assistant_msg.id AS answer_id,
        b.chat_file,
        c.nickname
    FROM base_history b
    LEFT JOIN rag_chat_history user_msg
        ON b.trace_id = user_msg.trace_id
        AND user_msg.role = 'user'
    LEFT JOIN rag_chat_history assistant_msg
        ON b.trace_id = assistant_msg.trace_id
        AND assistant_msg.role = 'assistant'
    JOIN rag_consumer c
        ON b.consumer_id = c.id
)
```

```sql
WITH original_query AS (
    SELECT
        h.consumer_id,
        h.created_at,
        c.nickname,
        CASE
            WHEN h.trace_id LIKE 'h5%' THEN 'H5端'
            WHEN h.trace_id LIKE 'applet%' THEN '小程序端'
            WHEN h.trace_id LIKE 'app%' THEN 'APP端'
            ELSE '其他来源'
        END AS user_source
    FROM rag_chat_history h
    JOIN rag_consumer c ON h.consumer_id = c.id
    WHERE h.created_at >= '2025-02-01'
		AND h.trace_id NOT LIKE '%myroki_test_com%'
    AND h.created_at < '2025-03-01'
    AND h.client_key in (5)
)
SELECT 
    consumer_id,
    nickname,
    COUNT(DISTINCT DATE(created_at)) as chat_days,
    GROUP_CONCAT(DISTINCT DATE(created_at) ORDER BY created_at) as chat_dates,
    CONCAT(
        'H5端', COUNT(DISTINCT CASE WHEN user_source = 'H5端' THEN DATE(created_at) END), 
        ' 小程序端', COUNT(DISTINCT CASE WHEN user_source = '小程序端' THEN DATE(created_at) END),
        ' APP端', COUNT(DISTINCT CASE WHEN user_source = 'APP端' THEN DATE(created_at) END)
    ) as source_stats
FROM original_query
GROUP BY consumer_id, nickname
HAVING chat_days >= 2
ORDER BY chat_days DESC;


统计用户来源 
```

```sql
WITH base_history AS (
        SELECT
        h.trace_id,
        h.session_id,
        h.consumer_id,
        MAX(h.vote) as max_vote,
        MAX(CASE WHEN h.role = 'assistant' THEN h.feedback END) as assistant_feedback,
        MAX(h.rewrite_query) AS rewrite_query,
        MAX(h.created_at) AS created_at,
        MAX(h.feedback) AS feedback,
        MAX(h.chat_file) AS chat_file
        FROM rag_chat_history h
        WHERE created_at >= '2025-05-01'
        AND h.client_key in (5)
        AND h.trace_id  LIKE 'app%'
        GROUP BY h.trace_id, h.session_id, h.consumer_id
        )
        SELECT
        b.trace_id,
        CASE
        WHEN b.trace_id LIKE 'h5%' THEN 'H5端'
        WHEN b.trace_id LIKE 'applet%' THEN '小程序端'
        WHEN b.trace_id LIKE 'app%' THEN 'APP端'
        WHEN b.trace_id LIKE 'box%' THEN '魔盒端'
        ELSE '其他来源'
        END AS user_source,
        b.session_id,
        b.consumer_id,
        user_msg.message AS user_message,
        assistant_msg.message AS assistant_message,
        user_msg.llm_message AS llm_message,
        CASE b.max_vote
        WHEN 0 THEN '正常'
        WHEN 1 THEN '点赞'
        WHEN 2 THEN '点踩'
        ELSE '未知'
        END AS vote,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.reason')) AS reason,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.type')) AS question_type,
        JSON_UNQUOTE(JSON_EXTRACT(b.assistant_feedback, '$.problems')) AS problem_description,
        b.rewrite_query,
        b.created_at,
        b.feedback,
        user_msg.id AS query_id,
        assistant_msg.id AS answer_id,
        b.chat_file,
        c.nickname,
				c.phone
        FROM base_history b
        LEFT JOIN rag_chat_history user_msg
        ON b.trace_id = user_msg.trace_id
        AND user_msg.role = 'user'
        LEFT JOIN rag_chat_history assistant_msg
        ON b.trace_id = assistant_msg.trace_id
        AND assistant_msg.role = 'assistant'
        JOIN rag_consumer c
        ON b.consumer_id = c.id
        ORDER BY b.created_at ASC
        
查询每条数据 包含手机号        
```

