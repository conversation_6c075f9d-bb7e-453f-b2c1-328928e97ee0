<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="appGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="toolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000000" flood-opacity="0.2"/>
    </filter>

    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="12" markerHeight="8"
            refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#4B5563" />
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="900" height="700" fill="#FFFFFF" stroke="#E5E7EB" stroke-width="2" rx="12"/>

  <!-- 标题 -->
  <text x="450" y="35" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="24" font-weight="bold" fill="#111827">
    Spring AI Tool Calling 执行流程
  </text>

  <!-- 主要参与者 -->

  <!-- 用户 -->
  <rect x="80" y="80" width="140" height="70" rx="12" fill="url(#userGradient)" filter="url(#shadow)"/>
  <text x="150" y="105" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="14" font-weight="bold" fill="white">👤 用户</text>
  <text x="150" y="125" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="white">User</text>
  <text x="150" y="140" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="10" fill="#E0E7FF">"北京天气如何？"</text>

  <!-- Spring AI 应用 -->
  <rect x="380" y="80" width="140" height="70" rx="12" fill="url(#appGradient)" filter="url(#shadow)"/>
  <text x="450" y="105" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="14" font-weight="bold" fill="white">🚀 Spring AI</text>
  <text x="450" y="125" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="white">Application</text>
  <text x="450" y="140" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="10" fill="#D1FAE5">工具调用管理</text>

  <!-- AI 模型 -->
  <rect x="680" y="80" width="140" height="70" rx="12" fill="url(#aiGradient)" filter="url(#shadow)"/>
  <text x="750" y="105" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="14" font-weight="bold" fill="white">🤖 AI 模型</text>
  <text x="750" y="125" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="white">LLM</text>
  <text x="750" y="140" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="10" fill="#FEF3C7">智能决策</text>

  <!-- 外部工具 -->
  <rect x="380" y="550" width="140" height="70" rx="12" fill="url(#toolGradient)" filter="url(#shadow)"/>
  <text x="450" y="575" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="14" font-weight="bold" fill="white">🔧 外部工具</text>
  <text x="450" y="595" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="white">External Tools</text>
  <text x="450" y="610" text-anchor="middle" font-family="'Segoe UI', Arial, sans-serif" font-size="10" fill="#F3E8FF">天气API、数据库等</text>
  
  <!-- 流程步骤 -->
  
  <!-- 步骤1: 工具定义注册 -->
  <line x1="170" y1="100" x2="330" y2="100" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="200" y="80" width="100" height="40" rx="5" fill="#FEF3C7" stroke="#F59E0B" stroke-width="1"/>
  <text x="250" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#92400E">1. 工具定义</text>
  <text x="250" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#92400E">Tool Definition</text>
  
  <!-- 步骤2: 发送请求 -->
  <line x1="460" y1="100" x2="620" y2="100" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="490" y="80" width="100" height="40" rx="5" fill="#DBEAFE" stroke="#3B82F6" stroke-width="1"/>
  <text x="540" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1D4ED8">2. 用户请求</text>
  <text x="540" y="108" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#1D4ED8">User Request</text>
  
  <!-- 步骤3: 模型决策 -->
  <path d="M 690 140 Q 720 180 690 220" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  <rect x="720" y="170" width="70" height="40" rx="5" fill="#FEE2E2" stroke="#EF4444" stroke-width="1"/>
  <text x="755" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#DC2626">3. 决策</text>
  <text x="755" y="198" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#DC2626">Decision</text>
  
  <!-- 步骤4: 工具调用请求 -->
  <line x1="620" y1="250" x2="460" y2="250" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="490" y="230" width="100" height="40" rx="5" fill="#F3E8FF" stroke="#8B5CF6" stroke-width="1"/>
  <text x="540" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#7C3AED">4. 工具调用</text>
  <text x="540" y="258" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#7C3AED">Tool Call</text>
  
  <!-- 步骤5: 执行工具 -->
  <line x1="400" y1="280" x2="400" y2="470" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="350" y="360" width="100" height="40" rx="5" fill="#ECFDF5" stroke="#10B981" stroke-width="1"/>
  <text x="400" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#059669">5. 执行工具</text>
  <text x="400" y="388" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#059669">Execute Tool</text>
  
  <!-- 步骤6: 返回结果 -->
  <line x1="400" y1="470" x2="400" y2="290" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="420" y="360" width="100" height="40" rx="5" fill="#FDF2F8" stroke="#EC4899" stroke-width="1"/>
  <text x="470" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#BE185D">6. 返回结果</text>
  <text x="470" y="388" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#BE185D">Return Result</text>
  
  <!-- 步骤7: 发送结果给模型 -->
  <line x1="460" y1="280" x2="620" y2="280" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="490" y="290" width="100" height="40" rx="5" fill="#F0FDF4" stroke="#22C55E" stroke-width="1"/>
  <text x="540" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#15803D">7. 结果回传</text>
  <text x="540" y="318" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#15803D">Send Result</text>
  
  <!-- 步骤8: 最终响应 -->
  <line x1="620" y1="320" x2="460" y2="320" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="490" y="340" width="100" height="40" rx="5" fill="#FFF7ED" stroke="#F97316" stroke-width="1"/>
  <text x="540" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#C2410C">8. 最终响应</text>
  <text x="540" y="368" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#C2410C">Final Response</text>
  
  <!-- 步骤9: 返回用户 -->
  <line x1="330" y1="350" x2="170" y2="350" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <rect x="200" y="330" width="100" height="40" rx="5" fill="#F0F9FF" stroke="#0EA5E9" stroke-width="1"/>
  <text x="250" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#0284C7">9. 返回用户</text>
  <text x="250" y="358" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#0284C7">Return to User</text>
  
  <!-- 示例文本 -->
  <rect x="50" y="420" width="700" height="120" rx="8" fill="#F8FAFC" stroke="#CBD5E1" stroke-width="1"/>
  <text x="70" y="440" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1F2937">示例流程：</text>
  <text x="70" y="460" font-family="Arial, sans-serif" font-size="11" fill="#374151">用户问："北京今天天气如何？"</text>
  <text x="70" y="480" font-family="Arial, sans-serif" font-size="11" fill="#374151">→ AI识别需要天气工具 → 调用getWeather("北京") → 返回"晴天25°C"</text>
  <text x="70" y="500" font-family="Arial, sans-serif" font-size="11" fill="#374151">→ AI生成回答："北京今天晴天，温度25°C，适合外出活动"</text>
  <text x="70" y="520" font-family="Arial, sans-serif" font-size="11" fill="#374151">→ 返回给用户</text>
</svg>
